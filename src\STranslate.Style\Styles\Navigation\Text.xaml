﻿<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:props="clr-namespace:STranslate.Style.Themes">

    <Style x:Key="Text_Style" TargetType="TextBlock">

        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="HorizontalAlignment" Value="Left" />
        <Setter Property="props:ThemeProps.Foreground" Value="{DynamicResource NavigationForeground}" />
        <Setter Property="FontSize" Value="{DynamicResource FontSize20}" />
        <Setter Property="Margin" Value="20,0,0,0" />

    </Style>

</ResourceDictionary>