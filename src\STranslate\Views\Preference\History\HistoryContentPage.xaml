﻿<UserControl
    x:Class="STranslate.Views.Preference.History.HistoryContentPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:props="clr-namespace:STranslate.Style.Themes;assembly=STranslate.Style"
    xmlns:vm="clr-namespace:STranslate.ViewModels.Preference.History"
    d:DataContext="{d:DesignInstance Type=vm:HistoryContentViewModel}"
    d:DesignHeight="450"
    d:DesignWidth="800"
    Background="Transparent"
    mc:Ignorable="d">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <Border
            Margin="7"
            props:ThemeProps.Background="{DynamicResource BorderBackground}"
            BorderBrush="{x:Null}"
            CornerRadius="5">
            <Grid Margin="10,0">
                <TextBlock
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center"
                    FontSize="{DynamicResource FontSize14}"
                    FontWeight="Black"
                    Text="{Binding Time, StringFormat=yyyy/MM/dd HH:mm:ss}" />

                <StackPanel HorizontalAlignment="Right" Orientation="Horizontal">
                    <Button
                        Command="{Binding DeleteCommand}"
                        Content="&#xe74b;"
                        FontSize="{DynamicResource FontSize14}"
                        FontWeight="Black"
                        Style="{DynamicResource ButtonIconStyle}" />

                    <!--  // TODO: 收藏 //  -->
                    <!--<Button Content="&#xe8b9;"
                            FontSize="{DynamicResource FontSize14}"
                            FontWeight="Black"
                            Style="{DynamicResource ButtonIconStyle}" />-->
                </StackPanel>
            </Grid>
        </Border>
        <ScrollViewer Grid.Row="1">
            <StackPanel>
                <Border Margin="6.5" Style="{DynamicResource BorderInOutputStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*" />
                            <RowDefinition Height="auto" />
                        </Grid.RowDefinitions>
                        <TextBox Style="{DynamicResource TextBoxOutputStyle}" Text="{Binding InputContent, Mode=OneWay}" />

                        <StackPanel
                            Grid.Row="1"
                            Margin="5"
                            VerticalAlignment="Bottom"
                            Orientation="Horizontal">
                            <Button
                                Margin="5,0"
                                Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.TTSCommand}"
                                CommandParameter="{Binding InputContent}"
                                Content="&#xe610;"
                                FontSize="{DynamicResource FontSize14}"
                                Style="{DynamicResource ButtonCopyIconStyle}"
                                ToolTip="{DynamicResource History.Content.TTS}" />
                            <Button
                                Margin="5,0"
                                Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.CopyResultCommand}"
                                CommandParameter="{Binding InputContent}"
                                Content="&#xe692;"
                                FontSize="{DynamicResource FontSize14}"
                                Style="{DynamicResource ButtonCopyIconStyle}"
                                ToolTip="{DynamicResource History.Content.Copy}" />
                        </StackPanel>
                    </Grid>
                </Border>

                <StackPanel Margin="10" Orientation="Horizontal">
                    <Border
                        props:ThemeProps.Background="{DynamicResource BtnBackground}"
                        BorderBrush="{x:Null}"
                        CornerRadius="5">
                        <TextBlock
                            Margin="5"
                            FontSize="{DynamicResource FontSize14}"
                            Text="{Binding SourceLang, Converter={StaticResource LangEnumDescriptionConverter}}" />
                    </Border>
                    <TextBlock Margin="10,0" Text="&gt;&gt;" />
                    <Border
                        props:ThemeProps.Background="{DynamicResource BtnBackground}"
                        BorderBrush="{x:Null}"
                        CornerRadius="5">
                        <TextBlock
                            Margin="5"
                            FontSize="{DynamicResource FontSize14}"
                            Text="{Binding TargetLang, Converter={StaticResource LangEnumDescriptionConverter}}" />
                    </Border>
                </StackPanel>
                <ListBox
                    Background="Transparent"
                    BorderThickness="0"
                    ItemsSource="{Binding OutputContents}"
                    PreviewMouseWheel="ListBox_PreviewMouseWheel"
                    ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <Border Style="{DynamicResource BorderInOutputStyle}">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="*" />
                                        <RowDefinition Height="auto" />
                                    </Grid.RowDefinitions>
                                    <TextBox
                                        Style="{DynamicResource TextBoxOutputStyle}"
                                        Tag="{Binding Item3.IsSuccess}"
                                        Text="{Binding Item3.Result, Mode=OneWay}" />

                                    <Grid
                                        Grid.Row="1"
                                        Margin="5"
                                        VerticalAlignment="Bottom">
                                        <StackPanel Orientation="Horizontal">
                                            <Button
                                                Margin="5,0,0,0"
                                                Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.TTSCommand}"
                                                CommandParameter="{Binding Item3.Result}"
                                                Content="&#xe610;"
                                                FontSize="{DynamicResource FontSize14}"
                                                Style="{DynamicResource ButtonCopyIconStyle}"
                                                ToolTip="{DynamicResource History.Content.TTS}" />
                                            <Button
                                                Margin="5,0,0,0"
                                                Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.CopyResultCommand}"
                                                CommandParameter="{Binding Item3.Result}"
                                                Content="&#xe692;"
                                                FontSize="{DynamicResource FontSize14}"
                                                Style="{DynamicResource ButtonCopyIconStyle}"
                                                ToolTip="{DynamicResource History.Content.CopyResult}" />
                                            <Button
                                                Margin="5,0,0,0"
                                                Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.CopySnakeResultCommand}"
                                                CommandParameter="{Binding Item3.Result}"
                                                Content="&#xe600;"
                                                FontSize="{DynamicResource FontSize14}"
                                                Style="{DynamicResource ButtonCopyIconStyle}"
                                                ToolTip="{DynamicResource History.Content.CopySnakeResult}" />
                                            <Button
                                                Margin="5,0,0,0"
                                                Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.CopySmallHumpResultCommand}"
                                                CommandParameter="{Binding Item3.Result}"
                                                Content="&#xe602;"
                                                FontSize="{DynamicResource FontSize14}"
                                                Style="{DynamicResource ButtonCopyIconStyle}"
                                                ToolTip="{DynamicResource History.Content.CopySmallHumpResult}" />
                                            <Button
                                                Margin="5,0,0,0"
                                                Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.CopyLargeHumpResultCommand}"
                                                CommandParameter="{Binding Item3.Result}"
                                                Content="&#xe601;"
                                                FontSize="{DynamicResource FontSize14}"
                                                Style="{DynamicResource ButtonCopyIconStyle}"
                                                ToolTip="{DynamicResource History.Content.CopyLargeHumpResult}" />
                                        </StackPanel>

                                        <StackPanel HorizontalAlignment="Right" Orientation="Horizontal">
                                            <TextBlock Margin="10,0" Text="{Binding Item1}" />
                                            <Image Width="20" Source="{Binding Item2, Converter={StaticResource String2IconConverter}}" />
                                        </StackPanel>
                                    </Grid>
                                </Grid>
                            </Border>
                        </DataTemplate>
                    </ListBox.ItemTemplate>
                </ListBox>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>