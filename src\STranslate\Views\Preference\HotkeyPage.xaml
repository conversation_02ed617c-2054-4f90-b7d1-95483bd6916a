﻿<UserControl
    x:Class="STranslate.Views.Preference.HotkeyPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:common="clr-namespace:STranslate.Style.Commons;assembly=STranslate.Style"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:model="clr-namespace:STranslate.Model;assembly=STranslate.Model"
    xmlns:props="clr-namespace:STranslate.Style.Themes;assembly=STranslate.Style"
    xmlns:vm="clr-namespace:STranslate.ViewModels.Preference"
    d:DataContext="{d:DesignInstance Type=vm:HotkeyViewModel}"
    d:DesignHeight="800"
    d:DesignWidth="800"
    d:FontFamily="{DynamicResource UserFont}"
    d:FontSize="{DynamicResource FontSize18}"
    Style="{StaticResource ResourceKey=Page_Style}"
    mc:Ignorable="d">
    <UserControl.Resources>
        <Style TargetType="Label">
            <Setter Property="Margin" Value="20,0" />
            <Setter Property="VerticalAlignment" Value="Center" />
            <Setter Property="HorizontalAlignment" Value="Right" />
        </Style>

        <Style BasedOn="{StaticResource CommonTB}" TargetType="TextBox">
            <Setter Property="Margin" Value="20,2" />
            <Setter Property="MinWidth" Value="180" />
            <Setter Property="HorizontalAlignment" Value="Left" />
            <Setter Property="CaretBrush" Value="Transparent" />
            <Setter Property="Cursor" Value="Hand" />
            <Setter Property="InputMethod.IsInputMethodEnabled" Value="False" />
        </Style>

        <Style TargetType="TextBlock">
            <Setter Property="Margin" Value="20,0" />
            <Setter Property="HorizontalAlignment" Value="Left" />
            <Setter Property="VerticalAlignment" Value="Center" />
            <Setter Property="DockPanel.Dock" Value="Right" />
            <Setter Property="FontWeight" Value="Bold" />
            <Setter Property="Visibility" Value="Hidden" />
            <Setter Property="Text" Value="{DynamicResource Hotkey.HotkeyConflict}" />
            <Setter Property="Foreground" Value="{DynamicResource HotkeyCollisionForeground}" />
        </Style>

        <!--#region TabControl Style-->
        <Style TargetType="{x:Type TabControl}">
            <Setter Property="Margin" Value="20,-20,20,20" />
            <Setter Property="props:ThemeProps.Background" Value="{DynamicResource BorderBackground}" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type TabControl}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>
                            <TabPanel Grid.Row="0" IsItemsHost="True" />
                            <Border
                                Grid.Row="1"
                                props:ThemeProps.BorderBrush="{DynamicResource BorderBrushColor}"
                                BorderThickness="1"
                                CornerRadius="6">
                                <ContentPresenter ContentSource="SelectedContent" />
                            </Border>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <Style TargetType="{x:Type TabItem}">
            <Setter Property="Background" Value="Transparent" />
            <!--  设置初始背景色为透明  -->
            <Setter Property="props:ThemeProps.BorderBrush" Value="{DynamicResource BorderBackground}" />
            <Setter Property="BorderThickness" Value="0,0,0,2" />
            <!--  底部边框  -->
            <Setter Property="Padding" Value="10" />
            <!--  文本颜色  -->
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type TabItem}">
                        <Border
                            x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="5">
                            <ContentPresenter
                                x:Name="contentPresenter"
                                Margin="{TemplateBinding Padding}"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                ContentSource="Header" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <!--  鼠标悬停效果  -->
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnMouseOverBackground}" />
                                <!--  悬停背景色  -->
                                <Setter TargetName="contentPresenter" Property="TextElement.Foreground" Value="{DynamicResource BtnPressedBackground}" />
                                <!--  悬停文本颜色  -->
                            </Trigger>
                            <!--  选中效果  -->
                            <Trigger Property="IsSelected" Value="True">
                                <Setter TargetName="border" Property="Background" Value="{DynamicResource BorderContentBackground}" />
                                <!--  选中背景色  -->
                                <Setter TargetName="border" Property="BorderBrush" Value="{DynamicResource BorderBrushColor}" />
                                <!--  选中边框颜色  -->
                                <Setter TargetName="contentPresenter" Property="TextElement.Foreground" Value="{DynamicResource TextForeground}" />
                                <!--  选中文本颜色  -->
                            </Trigger>
                            <!--  鼠标点击动画  -->
                            <EventTrigger RoutedEvent="MouseLeftButtonDown">
                                <BeginStoryboard>
                                    <Storyboard>
                                        <DoubleAnimation
                                            AutoReverse="True"
                                            Storyboard.TargetName="border"
                                            Storyboard.TargetProperty="(Border.Opacity)"
                                            From="1.0"
                                            To="0.8"
                                            Duration="0:0:0.1" />
                                    </Storyboard>
                                </BeginStoryboard>
                            </EventTrigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <!--#endregion-->

        <!--#region DataGrid Style-->
        <Style TargetType="{x:Type DataGrid}">
            <Setter Property="Margin" Value="2" />
            <Setter Property="RowHeaderWidth" Value="0" />
            <Setter Property="IsReadOnly" Value="True" />
            <Setter Property="AutoGenerateColumns" Value="False" />
            <Setter Property="CanUserSortColumns" Value="False" />
            <Setter Property="CanUserResizeRows" Value="False" />
            <Setter Property="CanUserResizeColumns" Value="False" />
            <Setter Property="CanUserReorderColumns" Value="False" />
            <Setter Property="props:ThemeProps.Background" Value="{DynamicResource BorderBackground}" />
            <Setter Property="props:ThemeProps.BorderBrush" Value="{DynamicResource BorderBackground}" />
            <Setter Property="HorizontalGridLinesBrush">
                <Setter.Value>
                    <SolidColorBrush Color="{DynamicResource PopupDropShadowEffectColor}" />
                </Setter.Value>
            </Setter>
            <Setter Property="VerticalGridLinesBrush">
                <Setter.Value>
                    <SolidColorBrush Color="{DynamicResource PopupDropShadowEffectColor}" />
                </Setter.Value>
            </Setter>
        </Style>
        <Style TargetType="{x:Type DataGridColumnHeader}">
            <Setter Property="Margin" Value="5,5,0,8" />
            <Setter Property="SnapsToDevicePixels" Value="True" />
            <Setter Property="props:ThemeProps.BorderBrush" Value="{DynamicResource BorderBrushColor}" />
            <Setter Property="props:ThemeProps.Background" Value="{DynamicResource BorderBackground}" />
            <!--<Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type DataGridColumnHeader}">
                        <Grid>
                            <Border Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="0,0,0,1">
            -->
            <!--  添加下边框  -->
            <!--
                                <ContentPresenter HorizontalAlignment="Left"
                                                  VerticalAlignment="Center" />
                            </Border>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>-->
        </Style>
        <Style TargetType="{x:Type DataGridRow}">
            <Setter Property="props:ThemeProps.Foreground" Value="{DynamicResource TextForeground}" />
            <Setter Property="props:ThemeProps.Background" Value="{DynamicResource BorderBackground}" />
        </Style>
        <Style TargetType="DataGridCell">
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type DataGridCell}">
                        <Border
                            Padding="5"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                            <!--  添加内边距  -->
                            <ContentPresenter />
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <!--#endregion-->
    </UserControl.Resources>
    <Border
        Margin="8"
        props:ThemeProps.Background="{DynamicResource BorderBackground}"
        CornerRadius="5">
        <TabControl>
            <TabItem Header="{DynamicResource Hotkey.GlobalHotkey}">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <!--  Added ScrollViewer  -->
                    <StackPanel Margin="0,20,0,0">
                        <!--  // 输入翻译 //  -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="3*" />
                                <ColumnDefinition Width="3*" />
                                <ColumnDefinition Width="2*" />
                            </Grid.ColumnDefinitions>
                            <Label Grid.Column="0" Content="{DynamicResource Hotkey.InputTranslate}" />
                            <TextBox Grid.Column="1" Text="{Binding InputHk.Content}">
                                <i:Interaction.Behaviors>
                                    <common:KeyArgsToCommandBehavior
                                        Keydown="{Binding KeydownCommand}"
                                        KeydownParam="{Binding RelativeSource={RelativeSource AncestorType=TextBox}}"
                                        Keyup="{Binding KeyupCommand}"
                                        KeyupParam="{x:Static model:HotkeyEnum.InputHk}" />
                                </i:Interaction.Behaviors>
                            </TextBox>
                            <TextBlock Grid.Column="2" Visibility="{Binding InputHk.ContentVisible, Converter={StaticResource BooleanToVisibilityHiddenConverter}}" />
                        </Grid>

                        <!--  // 划词翻译 //  -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="3*" />
                                <ColumnDefinition Width="3*" />
                                <ColumnDefinition Width="2*" />
                            </Grid.ColumnDefinitions>
                            <Label Grid.Column="0" Content="{DynamicResource Hotkey.CrosswordTranslate}" />
                            <TextBox Grid.Column="1" Text="{Binding CrosswordHk.Content}">
                                <i:Interaction.Behaviors>
                                    <common:KeyArgsToCommandBehavior
                                        Keydown="{Binding KeydownCommand}"
                                        KeydownParam="{Binding RelativeSource={RelativeSource AncestorType=TextBox}}"
                                        Keyup="{Binding KeyupCommand}"
                                        KeyupParam="{x:Static model:HotkeyEnum.CrosswordHk}" />
                                </i:Interaction.Behaviors>
                            </TextBox>
                            <TextBlock Grid.Column="2" Visibility="{Binding CrosswordHk.ContentVisible, Converter={StaticResource BooleanToVisibilityHiddenConverter}}" />
                        </Grid>

                        <!--  // 截图翻译 //  -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="3*" />
                                <ColumnDefinition Width="3*" />
                                <ColumnDefinition Width="2*" />
                            </Grid.ColumnDefinitions>
                            <Label Grid.Column="0" Content="{DynamicResource Hotkey.ScreenshotTranslate}" />
                            <TextBox Grid.Column="1" Text="{Binding ScreenshotHk.Content}">
                                <i:Interaction.Behaviors>
                                    <common:KeyArgsToCommandBehavior
                                        Keydown="{Binding KeydownCommand}"
                                        KeydownParam="{Binding RelativeSource={RelativeSource AncestorType=TextBox}}"
                                        Keyup="{Binding KeyupCommand}"
                                        KeyupParam="{x:Static model:HotkeyEnum.ScreenshotHk}" />
                                </i:Interaction.Behaviors>
                            </TextBox>
                            <TextBlock Grid.Column="2" Visibility="{Binding ScreenshotHk.ContentVisible, Converter={StaticResource BooleanToVisibilityHiddenConverter}}" />
                        </Grid>

                        <!--  // 替换翻译 //  -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="3*" />
                                <ColumnDefinition Width="3*" />
                                <ColumnDefinition Width="2*" />
                            </Grid.ColumnDefinitions>
                            <Label Grid.Column="0" Content="{DynamicResource Hotkey.ReplaceTranslate}" />
                            <TextBox Grid.Column="1" Text="{Binding ReplaceHk.Content}">
                                <i:Interaction.Behaviors>
                                    <common:KeyArgsToCommandBehavior
                                        Keydown="{Binding KeydownCommand}"
                                        KeydownParam="{Binding RelativeSource={RelativeSource AncestorType=TextBox}}"
                                        Keyup="{Binding KeyupCommand}"
                                        KeyupParam="{x:Static model:HotkeyEnum.ReplaceHk}" />
                                </i:Interaction.Behaviors>
                            </TextBox>
                            <TextBlock Grid.Column="2" Visibility="{Binding ReplaceHk.ContentVisible, Converter={StaticResource BooleanToVisibilityHiddenConverter}}" />
                        </Grid>

                        <!--  // 显示界面 //  -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="3*" />
                                <ColumnDefinition Width="3*" />
                                <ColumnDefinition Width="2*" />
                            </Grid.ColumnDefinitions>
                            <Label Grid.Column="0" Content="{DynamicResource Hotkey.ShowInterface}" />
                            <TextBox Grid.Column="1" Text="{Binding OpenHk.Content}">
                                <i:Interaction.Behaviors>
                                    <common:KeyArgsToCommandBehavior
                                        Keydown="{Binding KeydownCommand}"
                                        KeydownParam="{Binding RelativeSource={RelativeSource AncestorType=TextBox}}"
                                        Keyup="{Binding KeyupCommand}"
                                        KeyupParam="{x:Static model:HotkeyEnum.OpenHk}" />
                                </i:Interaction.Behaviors>
                            </TextBox>
                            <TextBlock Grid.Column="2" Visibility="{Binding OpenHk.ContentVisible, Converter={StaticResource BooleanToVisibilityHiddenConverter}}" />
                        </Grid>

                        <!--  // 鼠标划词 //  -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="3*" />
                                <ColumnDefinition Width="3*" />
                                <ColumnDefinition Width="2*" />
                            </Grid.ColumnDefinitions>
                            <Label Grid.Column="0" Content="{DynamicResource Hotkey.MouseCrossword}" />
                            <TextBox Grid.Column="1" Text="{Binding MousehookHk.Content}">
                                <i:Interaction.Behaviors>
                                    <common:KeyArgsToCommandBehavior
                                        Keydown="{Binding KeydownCommand}"
                                        KeydownParam="{Binding RelativeSource={RelativeSource AncestorType=TextBox}}"
                                        Keyup="{Binding KeyupCommand}"
                                        KeyupParam="{x:Static model:HotkeyEnum.MousehookHk}" />
                                </i:Interaction.Behaviors>
                            </TextBox>
                            <TextBlock Grid.Column="2" Visibility="{Binding MousehookHk.ContentVisible, Converter={StaticResource BooleanToVisibilityHiddenConverter}}" />
                        </Grid>

                        <!--  // 文字识别 //  -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="3*" />
                                <ColumnDefinition Width="3*" />
                                <ColumnDefinition Width="2*" />
                            </Grid.ColumnDefinitions>
                            <Label Grid.Column="0" Content="{DynamicResource Hotkey.OCR}" />
                            <TextBox Grid.Column="1" Text="{Binding OcrHk.Content}">
                                <i:Interaction.Behaviors>
                                    <common:KeyArgsToCommandBehavior
                                        Keydown="{Binding KeydownCommand}"
                                        KeydownParam="{Binding RelativeSource={RelativeSource AncestorType=TextBox}}"
                                        Keyup="{Binding KeyupCommand}"
                                        KeyupParam="{x:Static model:HotkeyEnum.OcrHk}" />
                                </i:Interaction.Behaviors>
                            </TextBox>
                            <TextBlock Grid.Column="2" Visibility="{Binding OcrHk.ContentVisible, Converter={StaticResource BooleanToVisibilityHiddenConverter}}" />
                        </Grid>

                        <!--  // 静默OCR //  -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="3*" />
                                <ColumnDefinition Width="3*" />
                                <ColumnDefinition Width="2*" />
                            </Grid.ColumnDefinitions>
                            <Label Grid.Column="0" Content="{DynamicResource Hotkey.SilentOCR}" />
                            <TextBox Grid.Column="1" Text="{Binding SilentOcrHk.Content}">
                                <i:Interaction.Behaviors>
                                    <common:KeyArgsToCommandBehavior
                                        Keydown="{Binding KeydownCommand}"
                                        KeydownParam="{Binding RelativeSource={RelativeSource AncestorType=TextBox}}"
                                        Keyup="{Binding KeyupCommand}"
                                        KeyupParam="{x:Static model:HotkeyEnum.SilentOcrHk}" />
                                </i:Interaction.Behaviors>
                            </TextBox>
                            <TextBlock Grid.Column="2" Visibility="{Binding SilentOcrHk.ContentVisible, Converter={StaticResource BooleanToVisibilityHiddenConverter}}" />
                        </Grid>

                        <!--  // 监听剪贴板 //  -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="3*" />
                                <ColumnDefinition Width="3*" />
                                <ColumnDefinition Width="2*" />
                            </Grid.ColumnDefinitions>
                            <Label Grid.Column="0" Content="{DynamicResource Hotkey.ClipboardMonitor}" />
                            <TextBox Grid.Column="1" Text="{Binding ClipboardMonitorHk.Content}">
                                <i:Interaction.Behaviors>
                                    <common:KeyArgsToCommandBehavior
                                        Keydown="{Binding KeydownCommand}"
                                        KeydownParam="{Binding RelativeSource={RelativeSource AncestorType=TextBox}}"
                                        Keyup="{Binding KeyupCommand}"
                                        KeyupParam="{x:Static model:HotkeyEnum.ClipboardMonitorHk}" />
                                </i:Interaction.Behaviors>
                            </TextBox>
                            <TextBlock Grid.Column="2" Visibility="{Binding ClipboardMonitorHk.ContentVisible, Converter={StaticResource BooleanToVisibilityHiddenConverter}}" />
                        </Grid>

                        <!--  // 静默TTS //  -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="3*" />
                                <ColumnDefinition Width="3*" />
                                <ColumnDefinition Width="2*" />
                            </Grid.ColumnDefinitions>
                            <Label Grid.Column="0" Content="{DynamicResource Hotkey.SilentTTS}" />
                            <TextBox Grid.Column="1" Text="{Binding SilentTtsHk.Content}">
                                <i:Interaction.Behaviors>
                                    <common:KeyArgsToCommandBehavior
                                        Keydown="{Binding KeydownCommand}"
                                        KeydownParam="{Binding RelativeSource={RelativeSource AncestorType=TextBox}}"
                                        Keyup="{Binding KeyupCommand}"
                                        KeyupParam="{x:Static model:HotkeyEnum.SilentTtsHk}" />
                                </i:Interaction.Behaviors>
                            </TextBox>
                            <TextBlock Grid.Column="2" Visibility="{Binding SilentTtsHk.ContentVisible, Converter={StaticResource BooleanToVisibilityHiddenConverter}}" />
                        </Grid>

                        <!--  // 保存撤销 //  -->
                        <Grid Margin="20,10,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="2*" />
                                <ColumnDefinition Width="2*" />
                                <ColumnDefinition />
                            </Grid.ColumnDefinitions>
                            <Button
                                Grid.Column="0"
                                MinWidth="160"
                                Margin="10,0"
                                HorizontalAlignment="Right"
                                Command="{Binding SaveCommand}"
                                Content="{DynamicResource Hotkey.SaveConfig}" />
                            <Button
                                Grid.Column="1"
                                MinWidth="160"
                                HorizontalAlignment="Left"
                                Command="{Binding ResetCommand}"
                                Content="{DynamicResource Hotkey.CancelModify}" />
                        </Grid>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
            <TabItem Header="{DynamicResource Hotkey.SoftHotkey}">
                <DataGrid AutoGenerateColumns="False">
                    <DataGrid.Columns>
                        <DataGridTextColumn
                            Width="*"
                            Binding="{Binding Column1}"
                            Header="{DynamicResource Hotkey.ShortcutKey}" />
                        <DataGridTextColumn
                            Width="2*"
                            Binding="{Binding Column2}"
                            Header="{DynamicResource Hotkey.Function}" />
                    </DataGrid.Columns>
                    <DataGrid.Items>
                        <model:KeyValueItem Column1="ESC" Column2="{DynamicResource Hotkey.SoftHotkey.ESC}" />
                        <model:KeyValueItem Column1="Ctrl + ," Column2="{DynamicResource Hotkey.SoftHotkey.OpenSettings}" />
                        <model:KeyValueItem Column1="Ctrl + /" Column2="{DynamicResource Hotkey.SoftHotkey.OpenHistory}" />
                        <model:KeyValueItem Column1="Ctrl + B" Column2="{DynamicResource Hotkey.SoftHotkey.ToggleAutoTranslate}" />
                        <model:KeyValueItem Column1="Ctrl + E" Column2="{DynamicResource Hotkey.SoftHotkey.ToggleIncrementalTranslation}" />
                        <model:KeyValueItem Column1="Ctrl + Shift + A" Column2="{DynamicResource Hotkey.SoftHotkey.ToggleInputBox}" />
                        <model:KeyValueItem Column1="Ctrl + Shift + M" Column2="{DynamicResource Hotkey.SoftHotkey.ResetWindowPosition}" />
                        <model:KeyValueItem Column1="Ctrl + Shift + T" Column2="{DynamicResource Hotkey.SoftHotkey.ToggleTopmost}" />
                        <model:KeyValueItem Column1="Ctrl + Shift + R" Column2="{DynamicResource Hotkey.SoftHotkey.SwitchTheme}" />
                        <model:KeyValueItem Column1="Ctrl + Shift + S" Column2="{DynamicResource Hotkey.SoftHotkey.SaveToVocabulary}" />
                        <model:KeyValueItem Column1="Ctrl + Shift + Q" Column2="{DynamicResource Hotkey.SoftHotkey.ExitProgram}" />
                        <model:KeyValueItem Column1="{DynamicResource Hotkey.SoftHotkey.Key.CtrlWithMouseWheelUp}" Column2="{DynamicResource Hotkey.SoftHotkey.ZoomInText}" />
                        <model:KeyValueItem Column1="{DynamicResource Hotkey.SoftHotkey.Key.CtrlWithMouseWheelDown}" Column2="{DynamicResource Hotkey.SoftHotkey.ZoomOutText}" />
                        <model:KeyValueItem Column1="Ctrl + `" Column2="{DynamicResource Hotkey.SoftHotkey.ResetTextSize}" />
                        <model:KeyValueItem Column1="Ctrl + 1...8" Column2="{DynamicResource Hotkey.SoftHotkey.CopyTranslationByService}" />
                        <model:KeyValueItem Column1="Ctrl + 9" Column2="{DynamicResource Hotkey.SoftHotkey.CopyLastTranslation}" />
                        <model:KeyValueItem Column1="Alt + `" Column2="{DynamicResource Hotkey.SoftHotkey.TTSInput}" />
                        <model:KeyValueItem Column1="Alt + 1...8" Column2="{DynamicResource Hotkey.SoftHotkey.TTSByService}" />
                        <model:KeyValueItem Column1="Alt + 9" Column2="{DynamicResource Hotkey.SoftHotkey.TTSLastTranslation}" />
                        <model:KeyValueItem Column1="Ctrl + +" Column2="{DynamicResource Hotkey.SoftHotkey.ZoomInUI}" />
                        <model:KeyValueItem Column1="Ctrl + -" Column2="{DynamicResource Hotkey.SoftHotkey.ZoomOutUI}" />
                        <model:KeyValueItem Column1="Ctrl + 0" Column2="{DynamicResource Hotkey.SoftHotkey.ResetUI}" />
                        <model:KeyValueItem Column1="Ctrl + Alt + +" Column2="{DynamicResource Hotkey.SoftHotkey.IncreaseWidth}" />
                        <model:KeyValueItem Column1="Ctrl + Alt + -" Column2="{DynamicResource Hotkey.SoftHotkey.DecreaseWidth}" />
                        <model:KeyValueItem Column1="Ctrl + Shift + +" Column2="{DynamicResource Hotkey.SoftHotkey.IncreaseMaxHeight}" />
                        <model:KeyValueItem Column1="Ctrl + Shift + -" Column2="{DynamicResource Hotkey.SoftHotkey.DecreaseMaxHeight}" />
                        <model:KeyValueItem Column1="Ctrl + [" Column2="{DynamicResource Hotkey.SoftHotkey.DecreaseFontSize}" />
                        <model:KeyValueItem Column1="Ctrl + ]" Column2="{DynamicResource Hotkey.SoftHotkey.IncreaseFontSize}" />
                        <model:KeyValueItem Column1="Ctrl + '" Column2="{DynamicResource Hotkey.SoftHotkey.ResetFontSize}" />
                        <model:KeyValueItem Column1="{DynamicResource Hotkey.SoftHotkey.Key.CtrlWithReturn}" Column2="{DynamicResource Hotkey.SoftHotkey.ToggleLineBreakMode}" />
                        <model:KeyValueItem Column1="{DynamicResource Hotkey.SoftHotkey.Key.CtrlWithTransBack}" Column2="{DynamicResource Hotkey.SoftHotkey.ToggleBackTranslation}" />
                    </DataGrid.Items>
                </DataGrid>
            </TabItem>
        </TabControl>
    </Border>
</UserControl>