﻿<UserControl
    x:Class="STranslate.Views.Preference.TTS.TTSOfflinePage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:common="clr-namespace:STranslate.Style.Commons;assembly=STranslate.Style"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:props="clr-namespace:STranslate.Style.Themes;assembly=STranslate.Style"
    xmlns:vm="clr-namespace:STranslate.ViewModels.Preference.TTS"
    d:DataContext="{d:DesignInstance Type=vm:TTSOffline}"
    d:DesignHeight="450"
    d:DesignWidth="800"
    Background="Transparent"
    FontSize="{DynamicResource FontSize18}"
    mc:Ignorable="d">
    <Border
        Padding="10,0,0,0"
        props:ThemeProps.BorderBrush="{DynamicResource BorderBrushColor}"
        BorderThickness="1"
        CornerRadius="4">
        <StackPanel>
            <Grid Margin="0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="80" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <TextBlock Text="{DynamicResource Service.Name}" />

                <common:PlaceholderTextBox
                    Grid.Column="1"
                    MinWidth="160"
                    HorizontalAlignment="Left"
                    Placeholder="离线TTS"
                    Text="{Binding Name, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" />
            </Grid>

            <Grid Margin="0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="80" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <TextBlock Text="{DynamicResource Service.Icon}" />

                <ComboBox
                    Grid.Column="1"
                    HorizontalAlignment="Left"
                    BorderThickness="1"
                    ItemsSource="{Binding Icons}"
                    SelectedValue="{Binding Icon}"
                    SelectedValuePath="Key"
                    Style="{DynamicResource IconComboBoxStyle}" />
            </Grid>

            <Grid Margin="0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="80" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="60" />
                </Grid.ColumnDefinitions>
                <TextBlock Text="{DynamicResource Service.TTS.Speed}" ToolTip="{DynamicResource Service.TTS.Speed.Tooltip}" />
                <Slider
                    Grid.Column="1"
                    Height="14"
                    MinWidth="200"
                    HorizontalAlignment="Left"
                    IsSnapToTickEnabled="True"
                    Maximum="10"
                    Minimum="-10"
                    TickFrequency="1"
                    TickPlacement="None"
                    Value="{Binding Rate, Mode=TwoWay}" />
                <TextBlock
                    Grid.Column="2"
                    Margin="10,0,0,0"
                    Text="{Binding Rate}" />
            </Grid>

            <Grid Margin="0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="80" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <TextBlock Text="{DynamicResource Service.TTS.Voice}" />

                <Button
                    Grid.Column="1"
                    MinWidth="200"
                    HorizontalAlignment="Left"
                    Command="{Binding GetSystemInfoCommand}"
                    Content="{DynamicResource Service.TTS.Offline}" />
            </Grid>
        </StackPanel>
    </Border>
</UserControl>