﻿<UserControl
    x:Class="STranslate.Views.Preference.TTS.TTSLingvaPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:common="clr-namespace:STranslate.Style.Commons;assembly=STranslate.Style"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:props="clr-namespace:STranslate.Style.Themes;assembly=STranslate.Style"
    xmlns:vm="clr-namespace:STranslate.ViewModels.Preference.TTS"
    d:DataContext="{d:DesignInstance Type=vm:TTSLingva}"
    d:DesignHeight="450"
    d:DesignWidth="800"
    Background="Transparent"
    FontSize="{DynamicResource FontSize18}"
    mc:Ignorable="d">
    <Border
        Padding="10,0,0,0"
        props:ThemeProps.BorderBrush="{DynamicResource BorderBrushColor}"
        BorderThickness="1"
        CornerRadius="4">
        <StackPanel>
            <Grid Margin="0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="80" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <TextBlock Text="{DynamicResource Service.Name}" />

                <common:PlaceholderTextBox
                    Grid.Column="1"
                    MinWidth="160"
                    HorizontalAlignment="Left"
                    Placeholder="Lingva TTS"
                    Text="{Binding Name, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" />
            </Grid>

            <Grid Margin="0,10" Visibility="Collapsed">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="80" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <TextBlock Text="{DynamicResource Service.Icon}" />

                <ComboBox
                    Grid.Column="1"
                    HorizontalAlignment="Left"
                    BorderThickness="1"
                    ItemsSource="{Binding Icons}"
                    SelectedValue="{Binding Icon}"
                    SelectedValuePath="Key"
                    Style="{DynamicResource IconComboBoxStyle}" />
            </Grid>

            <Grid Margin="0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="80" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <TextBlock Text="{DynamicResource Service.Api}" />

                <common:PlaceholderTextBox
                    Grid.Column="1"
                    MinWidth="206"
                    HorizontalAlignment="Left"
                    Placeholder="https://lingva.zggsong.com"
                    Text="{Binding Url, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" />
            </Grid>

            <Grid Margin="0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="80" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <TextBlock VerticalAlignment="Top" Text="{DynamicResource Service.Description}" />
                <TextBlock
                    Grid.Column="1"
                    FontSize="{DynamicResource FontSize14}"
                    Text="{DynamicResource Service.TTS.Lingva}"
                    TextWrapping="WrapWithOverflow" />
            </Grid>

            <Grid Margin="0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="80" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <TextBlock Text="Other: " />
                <TextBlock
                    Grid.Column="1"
                    Margin="10,0"
                    ToolTip="{DynamicResource Service.OpenInBrower}">
                    <Hyperlink Click="Hyperlink_Click">
                        <ContentControl Content="{DynamicResource Service.EnterOfficialWebsite}" />
                    </Hyperlink>
                </TextBlock>
            </Grid>
        </StackPanel>
    </Border>
</UserControl>