﻿<UserControl
    x:Class="STranslate.Views.Preference.TranslatorPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:common="clr-namespace:STranslate.Style.Commons;assembly=STranslate.Style"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dd="urn:gong-wpf-dragdrop"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:model="clr-namespace:STranslate.Model;assembly=STranslate.Model"
    xmlns:props="clr-namespace:STranslate.Style.Themes;assembly=STranslate.Style"
    xmlns:vm="clr-namespace:STranslate.ViewModels.Preference"
    d:DataContext="{d:DesignInstance Type=vm:TranslatorViewModel}"
    d:DesignHeight="450"
    d:DesignWidth="800"
    d:FontFamily="{DynamicResource UserFont}"
    d:FontSize="{DynamicResource FontSize18}"
    Style="{StaticResource ResourceKey=Page_Style}"
    mc:Ignorable="d">
    <UserControl.Resources>
        <DataTemplate x:Key="TranslatorTemplate" DataType="{x:Type model:ITranslator}">
            <Border x:Name="ServiceControl" Style="{DynamicResource BorderPopupStyle}">
                <Grid Margin="10,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="40" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="40" />
                    </Grid.ColumnDefinitions>

                    <Image Width="20" Source="{Binding Icon, Converter={StaticResource String2IconConverter}}" />

                    <TextBlock
                        Grid.Column="1"
                        VerticalAlignment="Center"
                        Text="{Binding Name}" />

                    <Border
                        Grid.Column="2"
                        Padding="1"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Background="{Binding Type, Converter={StaticResource ServiceType2BrushConverter}}"
                        CornerRadius="6">
                        <TextBlock
                            Padding="2,0"
                            props:ThemeProps.Foreground="{DynamicResource ServiceTypeForeground}"
                            FontSize="{DynamicResource FontSize12}"
                            Text="{Binding Type, Converter={StaticResource ServiceType2StringConverter}}" />
                    </Border>
                </Grid>
            </Border>
            <DataTemplate.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter TargetName="ServiceControl" Property="props:ThemeProps.Background" Value="{DynamicResource BtnMouseOverBackground}" />
                </Trigger>
            </DataTemplate.Triggers>
        </DataTemplate>

        <DataTemplate x:Key="SeparatorTemplate">
            <Separator />
        </DataTemplate>

        <common:TranslatorTemplateSelector
            x:Key="TranslatorTemplateSelector"
            SeparatorTemplate="{StaticResource SeparatorTemplate}"
            TranslatorTemplate="{StaticResource TranslatorTemplate}" />
    </UserControl.Resources>
    <UserControl.InputBindings>
        <KeyBinding
            Key="R"
            Command="{Binding SortCommand}"
            Modifiers="Ctrl+Shift" />
    </UserControl.InputBindings>
    <Border CornerRadius="5">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <!--  // Header //  -->
            <StackPanel Margin="10,5">
                <!--<TextBlock FontSize="{DynamicResource FontSize20}" Text="{DynamicResource Service.Translator.Title}" />-->
                <TextBlock FontSize="{DynamicResource FontSize18}" Text="{DynamicResource Service.Translator.SubTitle}" />
            </StackPanel>

            <Grid Grid.Row="1">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*" />
                    <RowDefinition Height="60" />
                </Grid.RowDefinitions>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*" MinWidth="200" />
                        <ColumnDefinition Width="10" />
                        <ColumnDefinition Width="3*" MinWidth="200" />
                    </Grid.ColumnDefinitions>

                    <!--  // Services //  -->
                    <Border
                        Margin="10,10,0,10"
                        props:ThemeProps.Background="{DynamicResource BorderBackground}"
                        props:ThemeProps.BorderBrush="{DynamicResource BorderBrushColor}"
                        BorderThickness="1"
                        CornerRadius="6">
                        <Border.Resources>
                            <common:BindingProxy x:Key="TVm" Data="{Binding .}" />
                        </Border.Resources>
                        <ListBox
                            x:Name="CurrentServiceListBox"
                            dd:DragDrop.IsDragSource="True"
                            dd:DragDrop.IsDropTarget="True"
                            dd:DragDrop.UseDefaultDragAdorner="True"
                            Background="Transparent"
                            BorderThickness="0"
                            ItemsSource="{Binding CurTransServiceList}"
                            ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                            ScrollViewer.VerticalScrollBarVisibility="Auto"
                            SelectedIndex="{Binding SelectedIndex}">
                            <i:Interaction.Triggers>
                                <i:EventTrigger EventName="SelectionChanged">
                                    <i:InvokeCommandAction Command="{Binding TogglePageCommand}" CommandParameter="{Binding ElementName=CurrentServiceListBox, Path=SelectedItem}" />
                                </i:EventTrigger>
                            </i:Interaction.Triggers>
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <Border
                                        x:Name="ServiceControl"
                                        Height="50"
                                        Style="{DynamicResource BorderInOutputStyle}">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="40" />
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="auto" MaxWidth="48" />
                                                <ColumnDefinition Width="50" />
                                            </Grid.ColumnDefinitions>

                                            <Image Width="20" Source="{Binding Icon, Converter={StaticResource String2IconConverter}}" />

                                            <TextBlock
                                                Grid.Column="1"
                                                VerticalAlignment="Center"
                                                Text="{Binding Name}"
                                                TextTrimming="CharacterEllipsis" />

                                            <Border
                                                Grid.Column="2"
                                                Margin="0,0,6,0"
                                                Padding="1"
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                Background="{Binding Type, Converter={StaticResource ServiceType2BrushConverter}}"
                                                CornerRadius="6">
                                                <TextBlock
                                                    Padding="2,0"
                                                    props:ThemeProps.Foreground="{DynamicResource ServiceTypeForeground}"
                                                    FontSize="{DynamicResource FontSize12}"
                                                    Text="{Binding Type, Converter={StaticResource ServiceType2StringConverter}}" />
                                            </Border>
                                            <ToggleButton Grid.Column="3" IsChecked="{Binding IsEnabled}" />
                                        </Grid>
                                        <Border.Resources>
                                            <common:BindingProxy x:Key="SingleSvc" Data="{Binding .}" />
                                        </Border.Resources>
                                        <Border.ContextMenu>
                                            <ContextMenu>
                                                <MenuItem
                                                    Command="{Binding Source={StaticResource TVm}, Path=Data.DuplicateSvcCommand}"
                                                    CommandParameter="{Binding Source={StaticResource SingleSvc}, Path=Data, Mode=OneWay}"
                                                    Header="{DynamicResource Service.Translator.Duplicate}"
                                                    Icon="&#xe652;" />
                                                <Separator />
                                                <MenuItem
                                                    Command="{Binding Source={StaticResource TVm}, Path=Data.DeleteCommand}"
                                                    CommandParameter="{Binding Source={StaticResource SingleSvc}, Path=Data, Mode=OneWay}"
                                                    Header="{DynamicResource Service.Translator.Delete}"
                                                    Icon="&#xe74b;" />
                                            </ContextMenu>
                                        </Border.ContextMenu>
                                    </Border>
                                    <DataTemplate.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter TargetName="ServiceControl" Property="props:ThemeProps.Background" Value="{DynamicResource BtnMouseOverBackground}" />
                                        </Trigger>
                                        <DataTrigger Binding="{Binding Path=IsSelected, RelativeSource={RelativeSource FindAncestor, AncestorType=ListBoxItem}}" Value="True">
                                            <Setter TargetName="ServiceControl" Property="props:ThemeProps.Background" Value="{DynamicResource BtnPressedBackground}" />
                                        </DataTrigger>
                                    </DataTemplate.Triggers>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>
                    </Border>

                    <GridSplitter
                        Grid.Column="1"
                        Margin="0,10"
                        HorizontalAlignment="Stretch"
                        VerticalAlignment="Stretch"
                        Background="Transparent"
                        ResizeBehavior="PreviousAndNext"
                        ResizeDirection="Columns" />

                    <!--  // Content //  -->
                    <ContentControl
                        Grid.Column="2"
                        Margin="0,10,10,10"
                        Content="{Binding ServiceContent}" />
                </Grid>

                <!--  // Footer //  -->
                <Grid Grid.Row="1" Margin="20,0">
                    <Grid.Resources>
                        <common:BindingProxy x:Key="Services" Data="{Binding TransServices}" />
                    </Grid.Resources>

                    <!--  // Add Service //  -->
                    <Grid>
                        <Button
                            Width="50"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Center"
                            HorizontalContentAlignment="Center"
                            VerticalContentAlignment="Center"
                            props:ThemeProps.BorderBrush="{DynamicResource BorderBrushColor}"
                            BorderThickness="1"
                            Command="{Binding OpenSelectPageCommand}"
                            CommandParameter="{Binding TransServices}"
                            Content="+"
                            Style="{DynamicResource ButtonIconStyle}" />

                        <!--#region 旧版服务列表-丢弃-->
                        <!--<ToggleButton
                            x:Name="BTN_Add"
                            Width="50"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Center"
                            HorizontalContentAlignment="Center"
                            VerticalContentAlignment="Center"
                            props:ThemeProps.BorderBrush="{DynamicResource BorderBrushColor}"
                            BorderThickness="1"
                            Content="+"
                            Style="{DynamicResource ToggleButtonIconStyle}" />
                        <Popup
                            x:Name="Popup_Add"
                            MinWidth="80"
                            MinHeight="30"
                            MaxHeight="400"
                            AllowsTransparency="True"
                            IsOpen="{Binding ElementName=BTN_Add, Path=IsChecked}"
                            Placement="Bottom"
                            PlacementTarget="{Binding ElementName=BTN_Add}"
                            PopupAnimation="Slide"
                            StaysOpen="False">
                            <Border Style="{DynamicResource BorderStyle}">
                                <ScrollViewer Margin="0,5" HorizontalScrollBarVisibility="Disabled">
                                    <ListBox
                                        Name="ServiceListBox"
                                        Background="Transparent"
                                        BorderThickness="0"
                                        ItemTemplateSelector="{StaticResource TranslatorTemplateSelector}"
                                        PreviewMouseWheel="ServiceListBox_PreviewMouseWheel">
                        -->
                        <!--  ItemsSource="{Binding TransServices}"  -->
                        <!--
                                        <ListBox.ItemsSource>
                                            <CompositeCollection>
                                                <CollectionContainer Collection="{Binding Source={StaticResource Services}, Path=Data, Converter={StaticResource ServiceTypeFilterConverter}, ConverterParameter=selfBuild}" />
                                                <Separator />
                                                <CollectionContainer Collection="{Binding Source={StaticResource Services}, Path=Data, Converter={StaticResource ServiceTypeFilterConverter}, ConverterParameter=local}" />
                                                <Separator />
                                                <CollectionContainer Collection="{Binding Source={StaticResource Services}, Path=Data, Converter={StaticResource ServiceTypeFilterConverter}, ConverterParameter=official}" />
                                            </CompositeCollection>
                                        </ListBox.ItemsSource>
                                        <i:Interaction.Triggers>
                                            <i:EventTrigger EventName="PreviewMouseLeftButtonUp">
                                                <i:InvokeCommandAction Command="{Binding AddCommand}">
                                                    <i:InvokeCommandAction.CommandParameter>
                                                        <MultiBinding Converter="{StaticResource MultiValue2ListConverter}">
                                                            <Binding ElementName="ServiceListBox" Path="SelectedItem" />
                                                            <Binding ElementName="BTN_Add" />
                                                        </MultiBinding>
                                                    </i:InvokeCommandAction.CommandParameter>
                                                </i:InvokeCommandAction>
                                            </i:EventTrigger>
                                        </i:Interaction.Triggers>
                                    </ListBox>
                                </ScrollViewer>
                            </Border>
                        </Popup>-->
                        <!--#endregion-->

                        <Button
                            x:Name="BTN_Del"
                            Width="50"
                            Margin="54,0,0,0"
                            HorizontalAlignment="Left"
                            HorizontalContentAlignment="Center"
                            VerticalContentAlignment="Center"
                            props:ThemeProps.BorderBrush="{DynamicResource BorderBrushColor}"
                            BorderThickness="1"
                            Command="{Binding DeleteCommand}"
                            CommandParameter="{Binding ElementName=CurrentServiceListBox, Path=SelectedItem}"
                            Content="-"
                            Style="{DynamicResource ButtonIconStyle}" />
                    </Grid>

                    <!--  // Save //  -->
                    <Grid>
                        <Button
                            Margin="0,0,128,0"
                            HorizontalAlignment="Right"
                            HorizontalContentAlignment="Center"
                            VerticalContentAlignment="Center"
                            Content="&#xe994; "
                            Style="{DynamicResource ButtonIconStyle}"
                            Visibility="Collapsed" />
                        <Button
                            Width="60"
                            Margin="0,0,76,0"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Center"
                            HorizontalContentAlignment="Center"
                            VerticalContentAlignment="Center"
                            Command="{Binding ResetCommand}"
                            Content="{DynamicResource Preference.Reset}" />
                        <Button
                            Width="60"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Center"
                            HorizontalContentAlignment="Center"
                            VerticalContentAlignment="Center"
                            Command="{Binding SaveCommand}"
                            Content="{DynamicResource Preference.Save}" />
                    </Grid>
                </Grid>
            </Grid>
        </Grid>
    </Border>
</UserControl>