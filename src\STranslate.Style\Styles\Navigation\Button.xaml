﻿<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:props="clr-namespace:STranslate.Style.Themes">

    <Style x:Key="BtnStyle" TargetType="RadioButton">

        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="HorizontalAlignment" Value="Center" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="Margin" Value="0,5,0,0" />

        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="RadioButton">

                    <Border
                        x:Name="Border"
                        Width="228"
                        Height="50"
                        props:ThemeProps.Background="{DynamicResource NavigationBackground}"
                        BorderThickness="0">

                        <Grid>
                            <Border
                                x:Name="Indicator"
                                Width="4"
                                Height="35"
                                Margin="5,0"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Center"
                                Background="{DynamicResource ThemeAccentColor}"
                                CornerRadius="2"
                                Visibility="Collapsed" />

                            <ContentPresenter />

                        </Grid>

                    </Border>

                    <ControlTemplate.Triggers>

                        <!--  // 未选中导航栏mouse hover时添加效果 //  -->
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsChecked" Value="False" />
                                <Condition Property="IsMouseOver" Value="True" />
                            </MultiTrigger.Conditions>
                            <MultiTrigger.Setters>
                                <Setter TargetName="Indicator" Property="Visibility" Value="Visible" />
                                <Setter TargetName="Indicator" Property="Background" Value="{DynamicResource NavigationIndicatorMouseOverBackground}" />
                                <Setter TargetName="Border" Property="props:ThemeProps.Background" Value="{DynamicResource NavigationMouseOverBackground}" />
                            </MultiTrigger.Setters>
                        </MultiTrigger>

                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="Indicator" Property="Visibility" Value="Visible" />
                            <Setter TargetName="Border" Property="props:ThemeProps.Background" Value="{DynamicResource NavigationSelected}" />
                        </Trigger>

                    </ControlTemplate.Triggers>

                </ControlTemplate>
            </Setter.Value>
        </Setter>

    </Style>

</ResourceDictionary>