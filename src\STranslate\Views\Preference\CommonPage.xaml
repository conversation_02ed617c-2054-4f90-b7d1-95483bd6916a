﻿<UserControl
    x:Class="STranslate.Views.Preference.CommonPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:common="clr-namespace:STranslate.Style.Commons;assembly=STranslate.Style"
    xmlns:control="clr-namespace:STranslate.Style.Controls;assembly=STranslate.Style"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:lang="clr-namespace:STranslate.Style.Styles;assembly=STranslate.Style"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:model="clr-namespace:STranslate.Model;assembly=STranslate.Model"
    xmlns:props="clr-namespace:STranslate.Style.Themes;assembly=STranslate.Style"
    xmlns:vm="clr-namespace:STranslate.ViewModels.Preference"
    d:DataContext="{d:DesignInstance Type=vm:CommonViewModel}"
    d:DesignHeight="1000"
    d:DesignWidth="800"
    d:FontFamily="{DynamicResource UserFont}"
    d:FontSize="{DynamicResource FontSize18}"
    Style="{StaticResource ResourceKey=Page_Style}"
    mc:Ignorable="d">
    <Border CornerRadius="5">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition />
                <RowDefinition Height="60" />
            </Grid.RowDefinitions>

            <ScrollViewer HorizontalScrollBarVisibility="Disabled" VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <StackPanel.Resources>
                        <Style BasedOn="{StaticResource {x:Type Expander}}" TargetType="{x:Type Expander}">
                            <Setter Property="Margin" Value="8" />
                        </Style>
                        <Style TargetType="{x:Type Border}">
                            <Setter Property="props:ThemeProps.Background" Value="{DynamicResource BorderContentBackground}" />
                            <Setter Property="CornerRadius" Value="5" />
                        </Style>
                        <Style
                            x:Key="ExpanderHeaderTextBlock"
                            BasedOn="{StaticResource {x:Type TextBlock}}"
                            TargetType="{x:Type TextBlock}">
                            <Setter Property="Padding" Value="5,8" />
                            <Setter Property="FontSize" Value="{DynamicResource FontSize19}" />
                            <Setter Property="props:ThemeProps.Foreground" Value="{DynamicResource TextBlockLangForeground}" />
                            <Setter Property="FontWeight" Value="Bold" />
                        </Style>
                    </StackPanel.Resources>

                    <!--#region 常用配置-->
                    <Expander IsExpanded="True">
                        <Expander.Header>
                            <TextBlock Style="{DynamicResource ExpanderHeaderTextBlock}" Text="{DynamicResource Common.Title}" />
                        </Expander.Header>
                        <Border>
                            <StackPanel>
                                <DockPanel Margin="20,10">
                                    <TextBlock HorizontalAlignment="Left" Text="{DynamicResource Common.AutoStart}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.AutoStartTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsStartup}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Common.StartMode}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.StartModeTip}" />
                                    <ComboBox
                                        Height="30"
                                        HorizontalAlignment="Right"
                                        common:LangAwareSelector.IsLangAware="True"
                                        BorderThickness="1"
                                        DisplayMemberPath="Description"
                                        ItemsSource="{Binding Source={common:Enumeration {x:Type model:StartModeKind}}}"
                                        SelectedValue="{Binding StartMode, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                        SelectedValuePath="Value" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Common.AutoCheckUpdate}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.AutoCheckUpdateTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding AutoCheckUpdate}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Common.DownloadProxy}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.DownloadProxyTip}" />
                                    <ComboBox
                                        Height="30"
                                        HorizontalAlignment="Right"
                                        BorderThickness="1"
                                        DisplayMemberPath="Value"
                                        ItemsSource="{Binding Source={common:Enumeration {x:Type model:DownloadProxyKind}}}"
                                        SelectedValue="{Binding DownloadProxy, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                        SelectedValuePath="Value" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Common.AppLanguage}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.AppLanguageTip}" />
                                    <ComboBox
                                        Height="30"
                                        HorizontalAlignment="Right"
                                        BorderThickness="1"
                                        DisplayMemberPath="Description"
                                        ItemsSource="{Binding Source={common:Enumeration {x:Type model:AppLanguageKind}}}"
                                        SelectedValue="{Binding AppLanguage, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                        SelectedValuePath="Value" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Common.AutoTranslate}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.AutoTranslateTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding AutoTranslate}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Common.ThemeSelection}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.ThemeSelectionTip}" />
                                    <ComboBox
                                        Height="30"
                                        HorizontalAlignment="Right"
                                        common:LangAwareSelector.IsLangAware="True"
                                        BorderThickness="1"
                                        DisplayMemberPath="Description"
                                        ItemsSource="{Binding Source={common:Enumeration {x:Type model:ThemeType}}}"
                                        SelectedValue="{Binding ThemeType, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                        SelectedValuePath="Value" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Common.FontSelection}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.FontSelectionTip}" />
                                    <ComboBox
                                        Height="30"
                                        HorizontalAlignment="Right"
                                        BorderThickness="1"
                                        ItemsSource="{Binding GetFontFamilys}"
                                        SelectedValue="{Binding CustomFont, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}">
                                        <ComboBox.ItemsPanel>
                                            <ItemsPanelTemplate>
                                                <VirtualizingStackPanel />
                                            </ItemsPanelTemplate>
                                        </ComboBox.ItemsPanel>
                                    </ComboBox>
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Common.GlobalFontSize}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.GlobalFontSizeTip}" />
                                    <ComboBox
                                        Height="30"
                                        HorizontalAlignment="Right"
                                        common:LangAwareSelector.IsLangAware="True"
                                        BorderThickness="1"
                                        DisplayMemberPath="Description"
                                        ItemsSource="{Binding Source={common:Enumeration {x:Type model:GlobalFontSizeEnum}}}"
                                        SelectedValue="{Binding GlobalFontSize, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                        SelectedValuePath="Value">
                                        <!--  InvalidOperationException: 无法同时设置 DisplayMemberPath 和 ItemTemplate。  -->
                                        <!--  https://stackoverflow.org.cn/questions/18273415  -->
                                        <ComboBox.ItemContainerStyle>
                                            <Style BasedOn="{StaticResource {x:Type ComboBoxItem}}" TargetType="{x:Type ComboBoxItem}">
                                                <Setter Property="TextBlock.Text" Value="{Binding ., Converter={StaticResource GlobalFontSizeToDescriptionConverter}}" />
                                                <Setter Property="TextBlock.FontSize" Value="{Binding ., Converter={StaticResource GlobalFontSizeToFontSizeConverter}}" />
                                            </Style>
                                        </ComboBox.ItemContainerStyle>
                                    </ComboBox>
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Common.DisableGlobalHotkeys}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.DisableGlobalHotkeysTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding DisableGlobalHotkeys}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Common.IgnoreHotkeysOnFullscreen}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.IgnoreHotkeysOnFullscreenTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IgnoreHotkeysOnFullscreen}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Common.OftenUsedLang}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.OftenUsedLangTip}" />
                                    <Button
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        BorderThickness="1"
                                        Command="{Binding OftenUsedLangChangeCommand}"
                                        Content="{DynamicResource Common.OftenUsedLangSetButton}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Common.LangDetect}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.LangDetectTip}" />
                                    <ComboBox
                                        Height="30"
                                        HorizontalAlignment="Right"
                                        common:LangAwareSelector.IsLangAware="True"
                                        BorderThickness="1"
                                        DisplayMemberPath="Description"
                                        ItemsSource="{Binding Source={common:Enumeration {x:Type model:LangDetectType}}}"
                                        SelectedValue="{Binding DetectType, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                        SelectedValuePath="Value" />
                                </DockPanel>

                                <DockPanel Margin="20,10" Visibility="{Binding DetectType, ConverterParameter=Local, Converter={StaticResource ParamEqualToVisibility}}">
                                    <TextBlock Text="{DynamicResource Common.LangDetectRatio}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.LangDetectRatioTip}" />
                                    <StackPanel
                                        HorizontalAlignment="Right"
                                        DockPanel.Dock="Right"
                                        Orientation="Horizontal">
                                        <Slider
                                            Height="14"
                                            MinWidth="160"
                                            Margin="0,0,30,0"
                                            IsSnapToTickEnabled="True"
                                            Maximum="0.99"
                                            Minimum="0.01"
                                            TickFrequency="0.01"
                                            TickPlacement="None"
                                            Value="{Binding AutoScale}" />
                                        <TextBlock
                                            Width="65"
                                            Margin="10,0"
                                            Text="{Binding AutoScale, StringFormat={}{0:P0}}" />
                                    </StackPanel>
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Common.WordPickingInterval}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.WordPickingIntervalTip}" />
                                    <StackPanel
                                        HorizontalAlignment="Right"
                                        DockPanel.Dock="Right"
                                        Orientation="Horizontal">
                                        <Slider
                                            Height="14"
                                            MinWidth="160"
                                            Margin="0,0,30,0"
                                            IsSnapToTickEnabled="True"
                                            Maximum="2000"
                                            Minimum="50"
                                            TickFrequency="50"
                                            TickPlacement="None"
                                            Value="{Binding WordPickingInterval}" />
                                        <TextBlock
                                            Width="75"
                                            Margin="0,0,10,0"
                                            Text="{Binding WordPickingInterval, StringFormat={}{0} ms}" />
                                    </StackPanel>
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Common.DoubleTapTrayFunc}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.DoubleTapTrayFuncTip}" />
                                    <ComboBox
                                        Height="30"
                                        HorizontalAlignment="Right"
                                        common:LangAwareSelector.IsLangAware="True"
                                        BorderThickness="1"
                                        DisplayMemberPath="Description"
                                        ItemsSource="{Binding Source={common:Enumeration {x:Type model:DoubleTapFuncEnum}}}"
                                        SelectedValue="{Binding DoubleTapTrayFunc, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                        SelectedValuePath="Value" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Common.HistorySize}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.HistorySizeTip}" />
                                    <ComboBox
                                        Height="30"
                                        HorizontalAlignment="Right"
                                        BorderThickness="1"
                                        SelectedIndex="{Binding HistorySizeType}">
                                        <ComboBoxItem Content="{DynamicResource Common.HistorySize.50}" />
                                        <ComboBoxItem Content="{DynamicResource Common.HistorySize.100}" />
                                        <ComboBoxItem Content="{DynamicResource Common.HistorySize.200}" />
                                        <ComboBoxItem Content="{DynamicResource Common.HistorySize.500}" />
                                        <ComboBoxItem Content="{DynamicResource Common.HistorySize.1000}" />
                                        <ComboBoxItem Content="{DynamicResource Common.HistorySize.Unlimited}" />
                                        <ComboBoxItem Content="{DynamicResource Common.HistorySize.None}" />
                                    </ComboBox>
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Common.SilentOcrLang}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.SilentOcrLangTip}" />
                                    <ComboBox
                                        x:Name="LangCb"
                                        Height="30"
                                        HorizontalAlignment="Right"
                                        common:LangAwareSelector.IsLangAware="True"
                                        BorderThickness="1"
                                        DisplayMemberPath="Description"
                                        SelectedValue="{Binding MainOcrLang, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                        SelectedValuePath="Value">
                                        <ComboBox.ItemsSource>
                                            <MultiBinding Converter="{StaticResource MultiLangFilterConverter}">
                                                <Binding Source="{common:LangEnumeration {x:Type model:LangEnum}}" />
                                                <Binding Path="InputVm.OftenUsedLang" />
                                            </MultiBinding>
                                        </ComboBox.ItemsSource>
                                    </ComboBox>
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Common.SourceLangIfAuto}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.SourceLangIfAutoTip}" />
                                    <ComboBox
                                        Height="30"
                                        HorizontalAlignment="Right"
                                        common:LangAwareSelector.IsLangAware="True"
                                        BorderThickness="1"
                                        DisplayMemberPath="Description"
                                        SelectedValue="{Binding SourceLangIfAuto, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                        SelectedValuePath="Value">
                                        <ComboBox.ItemsSource>
                                            <MultiBinding Converter="{StaticResource MultiLangFilterConverter}">
                                                <Binding Source="{common:LangEnumeration {x:Type model:LangEnum}}" />
                                                <Binding Path="InputVm.OftenUsedLang" />
                                            </MultiBinding>
                                        </ComboBox.ItemsSource>
                                    </ComboBox>
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Common.TargetLangIfSourceZh}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.TargetLangIfSourceZhTip}" />
                                    <ComboBox
                                        Height="30"
                                        HorizontalAlignment="Right"
                                        common:LangAwareSelector.IsLangAware="True"
                                        BorderThickness="1"
                                        DisplayMemberPath="Description"
                                        SelectedValue="{Binding TargetLangIfSourceZh, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                        SelectedValuePath="Value">
                                        <ComboBox.ItemsSource>
                                            <MultiBinding Converter="{StaticResource MultiLangFilterConverter}">
                                                <Binding Source="{common:LangEnumeration {x:Type model:LangEnum}}" />
                                                <Binding Path="InputVm.OftenUsedLang" />
                                            </MultiBinding>
                                        </ComboBox.ItemsSource>
                                    </ComboBox>
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Common.TargetLangIfSourceNotZh}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.TargetLangIfSourceNotZhTip}" />
                                    <ComboBox
                                        Height="30"
                                        HorizontalAlignment="Right"
                                        common:LangAwareSelector.IsLangAware="True"
                                        BorderThickness="1"
                                        DisplayMemberPath="Description"
                                        SelectedValue="{Binding TargetLangIfSourceNotZh, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                        SelectedValuePath="Value">
                                        <ComboBox.ItemsSource>
                                            <MultiBinding Converter="{StaticResource MultiLangFilterConverter}">
                                                <Binding Source="{common:LangEnumeration {x:Type model:LangEnum}}" />
                                                <Binding Path="InputVm.OftenUsedLang" />
                                            </MultiBinding>
                                        </ComboBox.ItemsSource>
                                    </ComboBox>
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Common.HttpTimeout}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.HttpTimeoutTip}" />
                                    <ComboBox
                                        Height="30"
                                        HorizontalAlignment="Right"
                                        BorderThickness="1"
                                        SelectedValue="{Binding HttpTimeout}"
                                        SelectedValuePath="Content">
                                        <ComboBoxItem Content="5" />
                                        <ComboBoxItem Content="10" />
                                        <ComboBoxItem Content="15" />
                                        <ComboBoxItem Content="20" />
                                        <ComboBoxItem Content="25" />
                                        <ComboBoxItem Content="30" />
                                        <ComboBoxItem Content="60" />
                                        <ComboBoxItem Content="120" />
                                    </ComboBox>
                                </DockPanel>
                            </StackPanel>
                        </Border>
                    </Expander>
                    <!--#endregion-->

                    <!--#region 网络配置-->
                    <Expander>
                        <Expander.Header>
                            <TextBlock Style="{DynamicResource ExpanderHeaderTextBlock}" Text="{DynamicResource Network.Title}" />
                        </Expander.Header>
                        <Border>
                            <StackPanel>
                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Network.ExternalCall}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.ExternalCallTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding ExternalCall}" />
                                </DockPanel>

                                <DockPanel Margin="20,10" Visibility="{Binding ExternalCall, Converter={StaticResource BooleanToVisibilityConverter}}">
                                    <TextBlock Text="{DynamicResource Network.ExternalCallPort}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.ExternalCallPortTip}" />
                                    <common:PlaceholderTextBox
                                        MinWidth="100"
                                        HorizontalAlignment="Right"
                                        VerticalAlignment="Center"
                                        Placeholder="{DynamicResource Network.PortPlaceholder}"
                                        Text="{Binding ExternalCallPort, Mode=TwoWay, Converter={StaticResource TextBoxToIntConverter}, ConverterParameter=50020}" />
                                </DockPanel>

                                <Separator />

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Network.ProxySettings}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.ProxySettingsTip}" />
                                    <ComboBox
                                        Height="30"
                                        HorizontalAlignment="Right"
                                        common:LangAwareSelector.IsLangAware="True"
                                        BorderThickness="1"
                                        DisplayMemberPath="Description"
                                        ItemsSource="{Binding Source={common:Enumeration {x:Type model:ProxyMethodEnum}}}"
                                        SelectedValue="{Binding ProxyMethod, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                        SelectedValuePath="Value" />
                                </DockPanel>

                                <DockPanel Margin="20,10" Visibility="{Binding ProxyMethod, Converter={StaticResource EnumToVisibilityConverter}}">
                                    <TextBlock Text="{DynamicResource Network.Server}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.ServerTip}" />
                                    <common:PlaceholderTextBox
                                        MinWidth="200"
                                        HorizontalAlignment="Right"
                                        VerticalAlignment="Center"
                                        Placeholder="{DynamicResource Network.ServerPlaceholder}"
                                        Text="{Binding ProxyIp, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" />
                                </DockPanel>
                                <DockPanel Margin="20,10" Visibility="{Binding ProxyMethod, Converter={StaticResource EnumToVisibilityConverter}}">
                                    <TextBlock Text="{DynamicResource Network.Port}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.PortTip}" />
                                    <common:PlaceholderTextBox
                                        MinWidth="200"
                                        HorizontalAlignment="Right"
                                        VerticalAlignment="Center"
                                        Placeholder="{DynamicResource Network.PortPlaceholder}"
                                        Text="{Binding ProxyPort, Mode=TwoWay, Converter={StaticResource TextBoxToIntConverter}, ConverterParameter=8089}" />
                                </DockPanel>

                                <DockPanel Margin="20,10" Visibility="{Binding ProxyMethod, Converter={StaticResource EnumToVisibilityConverter}}">
                                    <TextBlock Text="{DynamicResource Network.ProxyAuth}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.ProxyAuthTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsProxyAuthentication}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Network.Username}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.UsernameTip}" />
                                    <common:PlaceholderTextBox
                                        MinWidth="200"
                                        HorizontalAlignment="Right"
                                        VerticalAlignment="Center"
                                        Placeholder="{DynamicResource Network.UsernamePlaceholder}"
                                        Text="{Binding ProxyUsername, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" />

                                    <DockPanel.Visibility>
                                        <MultiBinding Converter="{StaticResource MultiProxyParam2VisibilityConverter}">
                                            <Binding Path="ProxyMethod" />
                                            <Binding Path="IsProxyAuthentication" />
                                        </MultiBinding>
                                    </DockPanel.Visibility>
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Network.Password}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.PasswordTip}" />
                                    <common:PlaceholderTextBox
                                        MinWidth="200"
                                        HorizontalAlignment="Right"
                                        VerticalAlignment="Center"
                                        DockPanel.Dock="Right"
                                        Placeholder="{DynamicResource Network.PasswordPlaceholder}"
                                        Text="{Binding ProxyPassword, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                        Visibility="{Binding IsProxyPasswordHide, Converter={StaticResource BooleanToVisibilityReverseConverter}}" />
                                    <PasswordBox
                                        MinWidth="200"
                                        HorizontalAlignment="Right"
                                        common:BoundPasswordBox.Attach="True"
                                        common:BoundPasswordBox.Password="{Binding ProxyPassword, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                                        DockPanel.Dock="Right"
                                        Tag="{DynamicResource Network.PasswordPlaceholder}"
                                        ToolTip="{Binding ProxyPassword}"
                                        Visibility="{Binding IsProxyPasswordHide, Converter={StaticResource BooleanToVisibilityConverter}}" />
                                    <Button
                                        HorizontalAlignment="Right"
                                        Command="{Binding ShowEncryptInfoCommand}"
                                        CommandParameter="ProxyPassword"
                                        Content="{Binding IsProxyPasswordHide, Converter={StaticResource BooleanToContentConverter}, ConverterParameter=ICON}"
                                        DockPanel.Dock="Right"
                                        Style="{DynamicResource ButtonIconStyle}" />

                                    <DockPanel.Visibility>
                                        <MultiBinding Converter="{StaticResource MultiProxyParam2VisibilityConverter}">
                                            <Binding Path="ProxyMethod" />
                                            <Binding Path="IsProxyAuthentication" />
                                        </MultiBinding>
                                    </DockPanel.Visibility>
                                </DockPanel>
                            </StackPanel>
                        </Border>
                    </Expander>
                    <!--#endregion-->

                    <!--#region 功能配置-->
                    <Expander>
                        <Expander.Header>
                            <TextBlock Style="{DynamicResource ExpanderHeaderTextBlock}" Text="{DynamicResource Function.Title}" />
                        </Expander.Header>
                        <Border>
                            <StackPanel>
                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Function.AdjustContentTranslate}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.AdjustContentTranslateTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsAdjustContentTranslate}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Function.ChangedLang2Execute}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.ChangedLang2ExecuteTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding ChangedLang2Execute}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Function.UsePasteOutput}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.UsePasteOutputTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding UsePasteOutput}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Function.UseFormsCopy}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.UseFormsCopyTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding UseFormsCopy}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Function.ScreenshotOcrAutoCopyText}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.ScreenshotOcrAutoCopyTextTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsScreenshotOcrAutoCopyText}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Function.LineBreakHandler}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.LineBreakHandlerTip}" />
                                    <ComboBox
                                        Height="30"
                                        HorizontalAlignment="Right"
                                        common:LangAwareSelector.IsLangAware="True"
                                        BorderThickness="1"
                                        DisplayMemberPath="Description"
                                        ItemsSource="{Binding Source={common:Enumeration {x:Type model:LineBreakHandlingMode}}}"
                                        SelectedValue="{Binding LineBreakHandler, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                        SelectedValuePath="Value" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Function.LineBreakOCRHandler}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.LineBreakOCRHandlerTip}" />
                                    <ComboBox
                                        Height="30"
                                        HorizontalAlignment="Right"
                                        common:LangAwareSelector.IsLangAware="True"
                                        BorderThickness="1"
                                        DisplayMemberPath="Description"
                                        ItemsSource="{Binding Source={common:Enumeration {x:Type model:LineBreakHandlingMode}}}"
                                        SelectedValue="{Binding LineBreakOCRHandler, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                        SelectedValuePath="Value" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Function.OcrAutoCopyText}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.OcrAutoCopyTextTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsOcrAutoCopyText}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Function.OcrChangedLang2Execute}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.OcrChangedLang2ExecuteTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding OcrChangedLang2Execute}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Function.IncrementalTranslation}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.IncrementalTranslationTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IncrementalTranslation}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Function.CopyResultAfterTranslate}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.CopyResultAfterTranslateTip}" />
                                    <ComboBox
                                        Height="30"
                                        HorizontalAlignment="Right"
                                        BorderThickness="1"
                                        SelectedIndex="{Binding CopyResultAfterTranslateIndex}">
                                        <ComboBoxItem Content="{DynamicResource Function.CopyResultAfterTranslate.NoAction}" />
                                        <ComboBoxItem Content="{DynamicResource Function.CopyResultAfterTranslate.First}" />
                                        <ComboBoxItem Content="{DynamicResource Function.CopyResultAfterTranslate.Second}" />
                                        <ComboBoxItem Content="{DynamicResource Function.CopyResultAfterTranslate.Third}" />
                                        <ComboBoxItem Content="{DynamicResource Function.CopyResultAfterTranslate.Fourth}" />
                                        <ComboBoxItem Content="{DynamicResource Function.CopyResultAfterTranslate.Fifth}" />
                                        <ComboBoxItem Content="{DynamicResource Function.CopyResultAfterTranslate.Sixth}" />
                                        <ComboBoxItem Content="{DynamicResource Function.CopyResultAfterTranslate.Seventh}" />
                                        <ComboBoxItem Content="{DynamicResource Function.CopyResultAfterTranslate.Eighth}" />
                                        <ComboBoxItem Content="{DynamicResource Function.CopyResultAfterTranslate.Last}" />
                                    </ComboBox>
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Function.HotkeyCopySuccessToast}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.HotkeyCopySuccessToastTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding HotkeyCopySuccessToast}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Function.OcrImageQuality}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.OcrImageQualityTip}" />
                                    <ComboBox
                                        Height="30"
                                        HorizontalAlignment="Right"
                                        common:LangAwareSelector.IsLangAware="True"
                                        BorderThickness="1"
                                        DisplayMemberPath="Description"
                                        ItemsSource="{Binding Source={common:Enumeration {x:Type model:OcrImageQualityEnum}}}"
                                        SelectedValue="{Binding OcrImageQuality, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                        SelectedValuePath="Value" />
                                </DockPanel>
                            </StackPanel>
                        </Border>
                    </Expander>
                    <!--#endregion-->

                    <!--#region 显示配置-->
                    <Expander>
                        <Expander.Header>
                            <TextBlock Style="{DynamicResource ExpanderHeaderTextBlock}" Text="{DynamicResource Display.Title}" />
                        </Expander.Header>
                        <Border>
                            <StackPanel>
                                <DockPanel Margin="20,10">
                                    <TextBlock HorizontalAlignment="Left" Text="{DynamicResource Display.HideOnStart}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.HideOnStartTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsHideOnStart}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock HorizontalAlignment="Left" Text="{DynamicResource Display.DisableNoticeOnStart}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.DisableNoticeOnStartTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsDisableNoticeOnStart}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Display.UseCacheLocation}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.UseCacheLocationTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding UseCacheLocation}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock HorizontalAlignment="Left" Text="{DynamicResource Display.AnimationSpeed}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.AnimationSpeedTip}" />
                                    <ComboBox
                                        Height="30"
                                        HorizontalAlignment="Right"
                                        common:LangAwareSelector.IsLangAware="True"
                                        BorderThickness="1"
                                        DisplayMemberPath="Description"
                                        ItemsSource="{Binding Source={common:Enumeration {x:Type model:AnimationSpeedEnum}}}"
                                        SelectedValue="{Binding AnimationSpeed, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                        SelectedValuePath="Value" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock HorizontalAlignment="Left" Text="{DynamicResource Display.ShowMainPlaceholder}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.ShowMainPlaceholderTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsShowMainPlaceholder}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Display.OnlyShowRet}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.OnlyShowRetTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsOnlyShowRet}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Display.HideLangWhenOnlyShowOutput}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.HideLangWhenOnlyShowOutputTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsHideLangWhenOnlyShowOutput}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Display.TriggerShowHide}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.TriggerShowHideTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsTriggerShowHide}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock HorizontalAlignment="Left" Text="{DynamicResource Display.MainViewMaxHeight}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.MainViewMaxHeightTip}" />
                                    <StackPanel
                                        HorizontalAlignment="Right"
                                        DockPanel.Dock="Right"
                                        FlowDirection="RightToLeft"
                                        Orientation="Horizontal">
                                        <TextBlock
                                            Width="75"
                                            Margin="10,0,0,0"
                                            FontWeight="Black"
                                            Text="{Binding MainViewMaxHeight, StringFormat={}{0:0}}" />
                                        <Slider
                                            Height="14"
                                            MinWidth="260"
                                            FlowDirection="LeftToRight"
                                            IsSnapToTickEnabled="True"
                                            Maximum="1080"
                                            Minimum="200"
                                            TickFrequency="10"
                                            TickPlacement="None"
                                            Value="{Binding MainViewMaxHeight}" />
                                    </StackPanel>
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock HorizontalAlignment="Left" Text="{DynamicResource Display.MainViewWidth}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.MainViewWidthTip}" />
                                    <StackPanel
                                        HorizontalAlignment="Right"
                                        DockPanel.Dock="Right"
                                        FlowDirection="RightToLeft"
                                        Orientation="Horizontal">
                                        <TextBlock
                                            Width="75"
                                            Margin="10,0,0,0"
                                            FontWeight="Black"
                                            Text="{Binding MainViewWidth, StringFormat={}{0:0}}" />
                                        <Slider
                                            Height="14"
                                            MinWidth="260"
                                            FlowDirection="LeftToRight"
                                            IsSnapToTickEnabled="True"
                                            Maximum="1920"
                                            Minimum="350"
                                            TickFrequency="10"
                                            TickPlacement="None"
                                            Value="{Binding MainViewWidth, Mode=TwoWay}" />
                                    </StackPanel>
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock HorizontalAlignment="Left" Text="{DynamicResource Display.InputViewMaxHeight}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.InputViewMaxHeightTip}" />
                                    <control:SilderArrange
                                        MinWidth="335"
                                        Margin="0,0,10,0"
                                        HorizontalAlignment="Right"
                                        DockPanel.Dock="Right"
                                        EndValue="{Binding InputViewMaxHeight}"
                                        FlowDirection="LeftToRight"
                                        Maximum="{Binding MainViewMaxHeight}"
                                        Minimum="20"
                                        SilderWidth="200"
                                        SliderTickFrequency="10"
                                        SliderTickPlacement="None"
                                        StartValue="{Binding InputViewMinHeight}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Display.FollowMouse}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.FollowMouseTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsFollowMouse}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock HorizontalAlignment="Left" Text="{DynamicResource Display.ShowAuxiliaryLine}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.ShowAuxiliaryLineTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding ShowAuxiliaryLine}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock HorizontalAlignment="Left" Text="{DynamicResource Display.MainViewShadow}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.MainViewShadowTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding MainViewShadow}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock HorizontalAlignment="Left" Text="{DynamicResource Display.PromptToggleVisible}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.PromptToggleVisibleTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsPromptToggleVisible}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Display.CloseUIOcrRetTranslate}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.CloseUIOcrRetTranslateTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding CloseUIOcrRetTranslate}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock HorizontalAlignment="Left" Text="{DynamicResource Display.KeepTopmostAfterMousehook}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.KeepTopmostAfterMousehookTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsKeepTopmostAfterMousehook}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock HorizontalAlignment="Left" Text="{DynamicResource Display.ShowCopyOnHeader}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.ShowCopyOnHeaderTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding ShowCopyOnHeader}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock HorizontalAlignment="Left" Text="{DynamicResource Display.CaretLast}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.CaretLastTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsCaretLast}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock HorizontalAlignment="Left" Text="{DynamicResource Display.TitleMaxWidth}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.TitleMaxWidthTip}" />
                                    <StackPanel
                                        HorizontalAlignment="Right"
                                        DockPanel.Dock="Right"
                                        FlowDirection="RightToLeft"
                                        Orientation="Horizontal">
                                        <TextBlock
                                            Width="75"
                                            Margin="10,0,0,0"
                                            FontWeight="Black"
                                            Text="{Binding TitleMaxWidth, StringFormat={}{0:0}}" />
                                        <Slider
                                            Height="14"
                                            MinWidth="260"
                                            FlowDirection="LeftToRight"
                                            IsSnapToTickEnabled="True"
                                            Maximum="1920"
                                            Minimum="10"
                                            TickFrequency="5"
                                            TickPlacement="None"
                                            Value="{Binding TitleMaxWidth, Mode=TwoWay}" />
                                    </StackPanel>
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock HorizontalAlignment="Left" Text="{DynamicResource Display.PromptMaxWidth}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.PromptMaxWidthTip}" />
                                    <StackPanel
                                        HorizontalAlignment="Right"
                                        DockPanel.Dock="Right"
                                        FlowDirection="RightToLeft"
                                        Orientation="Horizontal">
                                        <TextBlock
                                            Width="75"
                                            Margin="10,0,0,0"
                                            FontWeight="Black"
                                            Text="{Binding PromptMaxWidth, StringFormat={}{0:0}}" />
                                        <Slider
                                            Height="14"
                                            MinWidth="260"
                                            FlowDirection="LeftToRight"
                                            IsSnapToTickEnabled="True"
                                            Maximum="1920"
                                            Minimum="10"
                                            TickFrequency="5"
                                            TickPlacement="None"
                                            Value="{Binding PromptMaxWidth, Mode=TwoWay}" />
                                    </StackPanel>
                                </DockPanel>
                            </StackPanel>
                        </Border>
                    </Expander>
                    <!--#endregion-->

                    <!--#region 图标配置-->
                    <Expander>
                        <Expander.Header>
                            <TextBlock Style="{DynamicResource ExpanderHeaderTextBlock}" Text="{DynamicResource Icon.Title}" />
                        </Expander.Header>
                        <Border>
                            <StackPanel>
                                <DockPanel Margin="20,10">
                                    <TextBlock HorizontalAlignment="Left" Text="{DynamicResource Icon.ShowSnakeCopyBtn}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.ShowSnakeCopyBtnTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsShowSnakeCopyBtn}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock HorizontalAlignment="Left" Text="{DynamicResource Icon.ShowSmallHumpCopyBtn}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.ShowSmallHumpCopyBtnTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsShowSmallHumpCopyBtn}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock HorizontalAlignment="Left" Text="{DynamicResource Icon.ShowLargeHumpCopyBtn}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.ShowLargeHumpCopyBtnTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsShowLargeHumpCopyBtn}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock HorizontalAlignment="Left" Text="{DynamicResource Icon.ShowTranslateBackBtn}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.ShowTranslateBackBtnTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsShowTranslateBackBtn}" />
                                </DockPanel>

                                <Separator />

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Icon.ShowClose}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.ShowCloseTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsShowClose}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock HorizontalAlignment="Left" Text="{DynamicResource Icon.StayMainViewWhenLoseFocus}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.StayMainViewWhenLoseFocusTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding StayMainViewWhenLoseFocus}" />
                                </DockPanel>

                                <DockPanel Margin="20,10" Visibility="{Binding StayMainViewWhenLoseFocus, Converter={StaticResource BooleanToVisibilityConverter}}">
                                    <TextBlock Text="{DynamicResource Icon.ShowMinimalBtn}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.ShowMinimalBtnTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding ShowMinimalBtn}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Icon.ShowPreference}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.ShowPreferenceTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsShowPreference}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Icon.ShowConfigureService}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.ShowConfigureServiceTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsShowConfigureService}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Icon.ShowMainOcrLang}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.ShowMainOcrLangTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding ShowMainOcrLang}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Icon.ShowMousehook}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.ShowMousehookTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsShowMousehook}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Icon.ShowAutoTranslate}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.ShowAutoTranslateTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsShowAutoTranslate}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Icon.ShowIncrementalTranslation}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.ShowIncrementalTranslationTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsShowIncrementalTranslation}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Icon.ShowOnlyShowRet}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.ShowOnlyShowRetTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsShowOnlyShowRet}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Icon.ShowScreenshot}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.ShowScreenshotTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsShowScreenshot}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Icon.ShowOCR}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.ShowOCRTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsShowOCR}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Icon.ShowSilentOCR}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.ShowSilentOCRTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsShowSilentOCR}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Icon.ShowClipboardMonitor}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.ShowClipboardMonitorTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsShowClipboardMonitor}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Icon.ShowQRCode}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.ShowQRCodeTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsShowQRCode}" />
                                </DockPanel>

                                <DockPanel Margin="20,10">
                                    <TextBlock Text="{DynamicResource Icon.ShowHistory}" />
                                    <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Tooltip.ShowHistoryTip}" />
                                    <ToggleButton
                                        Height="26"
                                        HorizontalAlignment="Right"
                                        IsChecked="{Binding IsShowHistory}" />
                                </DockPanel>
                            </StackPanel>
                        </Border>
                    </Expander>
                    <!--#endregion-->
                </StackPanel>
            </ScrollViewer>

            <Grid Grid.Row="1" Margin="20,0">
                <Button
                    Width="60"
                    Margin="0,0,76,0"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Center"
                    HorizontalContentAlignment="Center"
                    VerticalContentAlignment="Center"
                    Command="{Binding ResetCommand}"
                    Content="{DynamicResource Preference.Reset}" />
                <Button
                    Width="60"
                    Padding="3"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Center"
                    Command="{Binding SaveCommand}"
                    Content="{DynamicResource Preference.Save}" />
            </Grid>
        </Grid>
    </Border>
</UserControl>