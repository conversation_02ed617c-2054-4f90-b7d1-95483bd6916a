@echo off
echo ========================================
echo STranslate AI Assistant 编译脚本
echo ========================================

:: 检查是否安装了 .NET SDK
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未找到 .NET SDK
    echo 请从以下地址下载安装 .NET 8.0 SDK:
    echo https://dotnet.microsoft.com/download/dotnet/8.0
    pause
    exit /b 1
)

echo 当前 .NET 版本：
dotnet --version

echo.
echo ========================================
echo 开始编译...
echo ========================================

:: 清理项目
echo 清理项目...
dotnet clean src/STranslate/STranslate.csproj

:: 还原依赖
echo 还原依赖...
dotnet restore src/STranslate/STranslate.csproj

:: 创建输出目录
if exist "output" rmdir /s /q "output"
mkdir "output"

:: 编译单文件版本（需要 .NET 运行时）
echo.
echo 编译单文件版本（需要 .NET 8.0 运行时）...
dotnet publish src/STranslate/STranslate.csproj -c Release -r win-x64 --self-contained false -p:PublishSingleFile=true -o "output/framework-dependent"

:: 编译独立版本（包含运行时）
echo.
echo 编译独立版本（包含完整运行时）...
dotnet publish src/STranslate/STranslate.csproj -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -o "output/self-contained"

echo.
echo ========================================
echo 编译完成！
echo ========================================
echo.
echo 输出文件位置：
echo - 单文件版本：output/framework-dependent/STranslate.exe
echo - 独立版本：  output/self-contained/STranslate.exe
echo.
echo 单文件版本更小但需要安装 .NET 8.0 运行时
echo 独立版本更大但可以直接运行
echo.
pause
