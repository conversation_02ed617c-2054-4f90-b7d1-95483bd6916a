﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <Style x:Key="Page_Style" TargetType="{x:Type UserControl}">

        <!--  // 设置内容字体 //  -->
        <Setter Property="FontSize" Value="{DynamicResource FontSize18}" />

        <!--  // 设置背景色 //  -->
        <Setter Property="Background" Value="Transparent" />

        <Setter Property="RenderTransform">
            <Setter.Value>
                <TranslateTransform X="0" Y="10" />
            </Setter.Value>
        </Setter>

        <Style.Triggers>

            <Trigger Property="Visibility" Value="Collapsed">

                <Setter Property="Opacity" Value="0" />

            </Trigger>

            <EventTrigger RoutedEvent="Loaded">
                <BeginStoryboard>
                    <Storyboard>
                        <!--  交叉淡入动画  -->
                        <DoubleAnimation
                            Storyboard.TargetProperty="Opacity"
                            From="1"
                            To="0"
                            Duration="0:0:0.3" />
                        <DoubleAnimation
                            Storyboard.TargetProperty="Opacity"
                            From="0"
                            To="1"
                            Duration="0:0:0.3" />

                        <!--  主要移动动画，设置一个较大的初始速度，然后在结束时减慢速度，模拟反弹效果  -->
                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)">
                            <EasingDoubleKeyFrame KeyTime="0:0:0" Value="50">
                                <EasingDoubleKeyFrame.EasingFunction>
                                    <BackEase Amplitude="0.3" EasingMode="EaseOut" />
                                </EasingDoubleKeyFrame.EasingFunction>
                            </EasingDoubleKeyFrame>
                            <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="0">
                                <EasingDoubleKeyFrame.EasingFunction>
                                    <BackEase Amplitude="0.3" EasingMode="EaseOut" />
                                </EasingDoubleKeyFrame.EasingFunction>
                            </EasingDoubleKeyFrame>
                        </DoubleAnimationUsingKeyFrames>
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>

        </Style.Triggers>

    </Style>

</ResourceDictionary>