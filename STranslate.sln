﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.7.34024.191
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "STranslate", "src\STranslate\STranslate.csproj", "{054D2232-9544-4461-BEB7-B0565A66D27C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "items", "items", "{8B472084-6B63-44BA-9D4A-B97576810AD2}"
	ProjectSection(SolutionItems) = preProject
		.gitignore = .gitignore
		.github\ISSUE_TEMPLATE\01_bug_report.yml = .github\ISSUE_TEMPLATE\01_bug_report.yml
		.github\ISSUE_TEMPLATE\02_feature_request.yml = .github\ISSUE_TEMPLATE\02_feature_request.yml
		7z.ps1 = 7z.ps1
		CHANGELOG.md = CHANGELOG.md
		cleanocr.ps1 = cleanocr.ps1
		Directory.Build.props = Directory.Build.props
		Directory.Packages.props = Directory.Packages.props
		.github\workflows\dotnet.yml = .github\workflows\dotnet.yml
		LICENSE = LICENSE
		nuget.config = nuget.config
		publish.ps1 = publish.ps1
		README.md = README.md
		README_ZH.md = README_ZH.md
		Sponsor.md = Sponsor.md
		version-info.json = version-info.json
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "STranslate.Log", "src\STranslate.Log\STranslate.Log.csproj", "{*************-40FF-941F-C617A9CC8739}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "STranslate.Model", "src\STranslate.Model\STranslate.Model.csproj", "{B16A7D67-D3A5-418B-8E49-2C85269566D8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "STranslate.Style", "src\STranslate.Style\STranslate.Style.csproj", "{420EAA87-5CA1-4117-94E0-B070FCCD06C2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "STranslate.Util", "src\STranslate.Util\STranslate.Util.csproj", "{74E3C5AB-6702-4AAC-8D56-2A72EFD437D2}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{FE39D4E9-A735-41ED-9EBE-F434187F31FA}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{054D2232-9544-4461-BEB7-B0565A66D27C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{054D2232-9544-4461-BEB7-B0565A66D27C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{054D2232-9544-4461-BEB7-B0565A66D27C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{054D2232-9544-4461-BEB7-B0565A66D27C}.Release|Any CPU.Build.0 = Release|Any CPU
		{*************-40FF-941F-C617A9CC8739}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{*************-40FF-941F-C617A9CC8739}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{*************-40FF-941F-C617A9CC8739}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{*************-40FF-941F-C617A9CC8739}.Release|Any CPU.Build.0 = Release|Any CPU
		{B16A7D67-D3A5-418B-8E49-2C85269566D8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B16A7D67-D3A5-418B-8E49-2C85269566D8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B16A7D67-D3A5-418B-8E49-2C85269566D8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B16A7D67-D3A5-418B-8E49-2C85269566D8}.Release|Any CPU.Build.0 = Release|Any CPU
		{420EAA87-5CA1-4117-94E0-B070FCCD06C2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{420EAA87-5CA1-4117-94E0-B070FCCD06C2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{420EAA87-5CA1-4117-94E0-B070FCCD06C2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{420EAA87-5CA1-4117-94E0-B070FCCD06C2}.Release|Any CPU.Build.0 = Release|Any CPU
		{74E3C5AB-6702-4AAC-8D56-2A72EFD437D2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{74E3C5AB-6702-4AAC-8D56-2A72EFD437D2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{74E3C5AB-6702-4AAC-8D56-2A72EFD437D2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{74E3C5AB-6702-4AAC-8D56-2A72EFD437D2}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{054D2232-9544-4461-BEB7-B0565A66D27C} = {FE39D4E9-A735-41ED-9EBE-F434187F31FA}
		{*************-40FF-941F-C617A9CC8739} = {FE39D4E9-A735-41ED-9EBE-F434187F31FA}
		{B16A7D67-D3A5-418B-8E49-2C85269566D8} = {FE39D4E9-A735-41ED-9EBE-F434187F31FA}
		{420EAA87-5CA1-4117-94E0-B070FCCD06C2} = {FE39D4E9-A735-41ED-9EBE-F434187F31FA}
		{74E3C5AB-6702-4AAC-8D56-2A72EFD437D2} = {FE39D4E9-A735-41ED-9EBE-F434187F31FA}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {BC3F2BB2-F582-4F51-95DF-661E334363FD}
	EndGlobalSection
EndGlobal
