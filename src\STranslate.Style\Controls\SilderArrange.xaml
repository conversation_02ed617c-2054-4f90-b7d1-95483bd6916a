<UserControl
    x:Class="STranslate.Style.Controls.SilderArrange"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    Name="UC_Arrange"
    Loaded="UC_Arrange_Loaded">

    <UserControl.Resources>
        <Style x:Key="RangeRepeatButton" TargetType="{x:Type RepeatButton}">
            <Setter Property="OverridesDefaultStyle" Value="true" />
            <Setter Property="Focusable" Value="false" />
            <Setter Property="IsTabStop" Value="false" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type RepeatButton}">
                        <Border
                            Width="{TemplateBinding Width}"
                            Height="4"
                            Background="{TemplateBinding Background}"
                            CornerRadius="2" />
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <ControlTemplate x:Key="RangeSliderThumb" TargetType="{x:Type Thumb}">
            <Grid
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                UseLayoutRounding="True">
                <Ellipse
                    Width="16"
                    Height="16"
                    Fill="{DynamicResource ToggleBtnForeground}" />
            </Grid>
        </ControlTemplate>

        <Style x:Key="RangeSlider" TargetType="{x:Type Slider}">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type Slider}">
                        <Grid>
                            <Border
                                x:Name="TrackBackground"
                                Height="4"
                                VerticalAlignment="Center"
                                Panel.ZIndex="1"
                                Background="{DynamicResource ThumbBackground}"
                                CornerRadius="2" />

                            <Track x:Name="PART_Track" Panel.ZIndex="2">
                                <Track.DecreaseRepeatButton>
                                    <RepeatButton Background="{DynamicResource ThumbBackground}" Style="{StaticResource RangeRepeatButton}" />
                                </Track.DecreaseRepeatButton>
                                <Track.IncreaseRepeatButton>
                                    <RepeatButton Background="{DynamicResource ToggleBtnForeground}" Style="{StaticResource RangeRepeatButton}" />
                                </Track.IncreaseRepeatButton>
                                <Track.Thumb>
                                    <Thumb
                                        Width="16"
                                        Height="16"
                                        Template="{StaticResource RangeSliderThumb}" />
                                </Track.Thumb>
                            </Track>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="RangeSliderL" TargetType="{x:Type Slider}">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type Slider}">
                        <Grid>
                            <Border
                                x:Name="TrackBackground"
                                Height="4"
                                VerticalAlignment="Center"
                                Background="{DynamicResource ThumbBackground}"
                                CornerRadius="2" />

                            <Track x:Name="PART_Track">
                                <Track.DecreaseRepeatButton>
                                    <RepeatButton Background="{DynamicResource ToggleBtnForeground}" Style="{StaticResource RangeRepeatButton}" />
                                </Track.DecreaseRepeatButton>
                                <Track.IncreaseRepeatButton>
                                    <RepeatButton Background="{DynamicResource ThumbBackground}" Style="{StaticResource RangeRepeatButton}" />
                                </Track.IncreaseRepeatButton>
                                <Track.Thumb>
                                    <Thumb
                                        Width="16"
                                        Height="16"
                                        Template="{StaticResource RangeSliderThumb}" />
                                </Track.Thumb>
                            </Track>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <StackPanel Height="{Binding ElementName=UC_Arrange, Path=SilderHeight}" Orientation="Horizontal">
        <TextBlock
            Width="35"
            Margin="0,3"
            FontWeight="Bold"
            Text="{Binding ElementName=SL_Bat1, Path=Value}" />

        <Canvas Width="{Binding ElementName=UC_Arrange, Path=SilderWidth}" Margin="0,0,5,0">
            <Slider
                x:Name="SL_Bat1"
                Width="{Binding ElementName=UC_Arrange, Path=SilderWidth}"
                Margin="2"
                VerticalAlignment="Center"
                Panel.ZIndex="2"
                Clip="{Binding ElementName=UC_Arrange, Path=StartRect}"
                IsSnapToTickEnabled="True"
                Maximum="{Binding ElementName=UC_Arrange, Path=Maximum}"
                Minimum="{Binding ElementName=UC_Arrange, Path=Minimum}"
                Style="{StaticResource RangeSlider}"
                TickFrequency="{Binding ElementName=UC_Arrange, Path=SliderTickFrequency}"
                ValueChanged="SL_Bat1_ValueChanged"
                Value="{Binding ElementName=UC_Arrange, Path=StartValue}" />

            <Slider
                x:Name="SL_Bat2"
                Width="{Binding ElementName=UC_Arrange, Path=SilderWidth}"
                Margin="2"
                VerticalAlignment="Center"
                Panel.ZIndex="1"
                Clip="{Binding ElementName=UC_Arrange, Path=EndRect}"
                IsSnapToTickEnabled="True"
                Maximum="{Binding ElementName=UC_Arrange, Path=Maximum}"
                Minimum="{Binding ElementName=UC_Arrange, Path=Minimum}"
                Style="{StaticResource RangeSliderL}"
                TickFrequency="{Binding ElementName=UC_Arrange, Path=SliderTickFrequency}"
                ValueChanged="SL_Bat2_ValueChanged"
                Value="{Binding ElementName=UC_Arrange, Path=EndValue}" />
        </Canvas>

        <TextBlock
            Width="35"
            Margin="0,3"
            FontWeight="Bold"
            Text="{Binding ElementName=SL_Bat2, Path=Value}" />
    </StackPanel>
</UserControl>