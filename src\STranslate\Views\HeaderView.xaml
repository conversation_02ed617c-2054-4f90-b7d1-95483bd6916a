﻿<UserControl
    x:Class="STranslate.Views.HeaderView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:model="clr-namespace:STranslate.Model;assembly=STranslate.Model"
    xmlns:props="clr-namespace:STranslate.Style.Themes;assembly=STranslate.Style"
    xmlns:proxy="clr-namespace:STranslate.Style.Commons;assembly=STranslate.Style"
    xmlns:vm="clr-namespace:STranslate.ViewModels"
    d:DataContext="{d:DesignInstance Type=vm:MainViewModel}"
    d:DesignHeight="450"
    d:DesignWidth="800"
    mc:Ignorable="d">
    <UserControl.Resources>
        <proxy:BindingProxy x:Key="Vm" Data="{Binding}" />
        <proxy:BindingProxy x:Key="translator" Data="{Binding OutputVM.Translators}" />
        <proxy:BindingProxy x:Key="ocr" Data="{Binding OCRVM.CurOCRServiceList}" />
        <proxy:BindingProxy x:Key="tts" Data="{Binding TTSVM.CurTTSServiceList}" />
        <DataTemplate x:Key="ITranslatorTemplate" DataType="{x:Type model:ITranslator}">
            <Border
                x:Name="ServiceControl"
                Margin="5"
                HorizontalAlignment="Center"
                Background="{DynamicResource BorderBackground}"
                BorderBrush="{x:Null}"
                CornerRadius="5">
                <Grid Margin="0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="30" />
                        <ColumnDefinition Width="20" />
                        <ColumnDefinition Width="80" />
                        <ColumnDefinition Width="auto" MaxWidth="48" />
                    </Grid.ColumnDefinitions>

                    <Label
                        HorizontalAlignment="Left"
                        Content="&#xec9e;"
                        FontFamily="{DynamicResource IconFont}"
                        FontSize="{DynamicResource FontSize14}"
                        FontWeight="Bold"
                        Visibility="{Binding IsEnabled, Converter={StaticResource BooleanToVisibilityConverter}}" />

                    <Image
                        Grid.Column="1"
                        Width="14"
                        HorizontalAlignment="Left"
                        Source="{Binding Icon, Converter={StaticResource String2IconConverter}}" />

                    <TextBlock
                        Grid.Column="2"
                        HorizontalAlignment="Left"
                        FontSize="{DynamicResource FontSize14}"
                        Text="{Binding Name}"
                        TextTrimming="CharacterEllipsis" />

                    <Border
                        Grid.Column="3"
                        Margin="0,0,3,0"
                        Padding="1"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        Background="{Binding Type, Converter={StaticResource ServiceType2BrushConverter}}"
                        CornerRadius="6">
                        <TextBlock
                            x:Name="ServiceTypeText"
                            Padding="2,0"
                            props:ThemeProps.Foreground="{DynamicResource ServiceTypeForeground}"
                            FontSize="{DynamicResource FontSize12}">
                            <TextBlock.Text>
                                <MultiBinding Converter="{StaticResource MultiServiceType2StringConverter}">
                                    <Binding ElementName="ServiceTypeText" />
                                    <Binding Path="Type" />
                                </MultiBinding>
                            </TextBlock.Text>
                        </TextBlock>
                    </Border>
                </Grid>
            </Border>
            <DataTemplate.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter TargetName="ServiceControl" Property="Background" Value="{DynamicResource BtnMouseOverBackground}" />
                </Trigger>
            </DataTemplate.Triggers>
        </DataTemplate>
        <DataTemplate x:Key="IOCRTemplate" DataType="{x:Type model:IOCR}">
            <Border
                x:Name="OcrControl"
                Margin="5"
                HorizontalAlignment="Center"
                Background="{DynamicResource BorderBackground}"
                BorderBrush="{x:Null}"
                CornerRadius="5">
                <Grid Margin="0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="30" />
                        <ColumnDefinition Width="20" />
                        <ColumnDefinition Width="80" />
                        <ColumnDefinition Width="auto" MaxWidth="48" />
                    </Grid.ColumnDefinitions>

                    <Label
                        HorizontalAlignment="Left"
                        Content="&#xec9e;"
                        FontFamily="{DynamicResource IconFont}"
                        FontSize="{DynamicResource FontSize14}"
                        FontWeight="Bold"
                        Visibility="{Binding IsEnabled, Converter={StaticResource BooleanToVisibilityConverter}}" />

                    <Image
                        Grid.Column="1"
                        Width="14"
                        HorizontalAlignment="Left"
                        Source="{Binding Icon, Converter={StaticResource String2IconConverter}}" />

                    <TextBlock
                        Grid.Column="2"
                        HorizontalAlignment="Left"
                        FontSize="{DynamicResource FontSize14}"
                        Text="{Binding Name}"
                        TextTrimming="CharacterEllipsis" />

                    <Border
                        Grid.Column="3"
                        Margin="0,0,3,0"
                        Padding="1"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        Background="{Binding Type, Converter={StaticResource ServiceType2BrushConverter}}"
                        CornerRadius="6">
                        <TextBlock
                            x:Name="ServiceTypeText"
                            Padding="2,0"
                            props:ThemeProps.Foreground="{DynamicResource ServiceTypeForeground}"
                            FontSize="{DynamicResource FontSize12}">
                            <TextBlock.Text>
                                <MultiBinding Converter="{StaticResource MultiServiceType2StringConverter}">
                                    <Binding ElementName="ServiceTypeText" />
                                    <Binding Path="Type" />
                                </MultiBinding>
                            </TextBlock.Text>
                        </TextBlock>
                        <!--  Text="{Binding Type, Converter={StaticResource ServiceType2StringConverter}}" />  -->
                    </Border>
                </Grid>
            </Border>
            <DataTemplate.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter TargetName="OcrControl" Property="Background" Value="{DynamicResource BtnMouseOverBackground}" />
                </Trigger>
            </DataTemplate.Triggers>
        </DataTemplate>
        <DataTemplate x:Key="ITTSTemplate" DataType="{x:Type model:ITTS}">
            <Border
                x:Name="OcrControl"
                Margin="5"
                HorizontalAlignment="Center"
                Background="{DynamicResource BorderBackground}"
                BorderBrush="{x:Null}"
                CornerRadius="5">
                <Grid Margin="0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="30" />
                        <ColumnDefinition Width="20" />
                        <ColumnDefinition Width="80" />
                        <ColumnDefinition Width="auto" MaxWidth="48" />
                    </Grid.ColumnDefinitions>

                    <Label
                        HorizontalAlignment="Left"
                        Content="&#xec9e;"
                        FontFamily="{DynamicResource IconFont}"
                        FontSize="{DynamicResource FontSize14}"
                        FontWeight="Bold"
                        Visibility="{Binding IsEnabled, Converter={StaticResource BooleanToVisibilityConverter}}" />

                    <Image
                        Grid.Column="1"
                        Width="14"
                        HorizontalAlignment="Left"
                        Source="{Binding Icon, Converter={StaticResource String2IconConverter}}" />

                    <TextBlock
                        Grid.Column="2"
                        HorizontalAlignment="Left"
                        FontSize="{DynamicResource FontSize14}"
                        Text="{Binding Name}"
                        TextTrimming="CharacterEllipsis" />

                    <Border
                        Grid.Column="3"
                        Margin="0,0,3,0"
                        Padding="1"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        Background="{Binding Type, Converter={StaticResource ServiceType2BrushConverter}}"
                        CornerRadius="6">
                        <TextBlock
                            x:Name="ServiceTypeText"
                            Padding="2,0"
                            props:ThemeProps.Foreground="{DynamicResource ServiceTypeForeground}"
                            FontSize="{DynamicResource FontSize12}">
                            <TextBlock.Text>
                                <MultiBinding Converter="{StaticResource MultiServiceType2StringConverter}">
                                    <Binding ElementName="ServiceTypeText" />
                                    <Binding Path="Type" />
                                </MultiBinding>
                            </TextBlock.Text>
                        </TextBlock>
                    </Border>
                </Grid>
            </Border>
            <DataTemplate.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter TargetName="OcrControl" Property="Background" Value="{DynamicResource BtnMouseOverBackground}" />
                </Trigger>
            </DataTemplate.Triggers>
        </DataTemplate>
        <!--  // 直接写模板，无法自动获取，使用模板选择器 //  -->
        <proxy:HeaderTemplateSelector
            x:Key="HeaderTemplateSelector"
            IOCRTemplate="{StaticResource IOCRTemplate}"
            ITTSTemplate="{StaticResource ITTSTemplate}"
            ITranslatorTemplate="{StaticResource ITranslatorTemplate}" />
    </UserControl.Resources>
    <Grid>
        <Button
            x:Name="TopmostBtn"
            Margin="10,5,0,0"
            HorizontalAlignment="Left"
            Command="{Binding StickyCommand}"
            CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
            Content="{Binding TopMostContent}"
            FontSize="{DynamicResource FontSize21}"
            Style="{DynamicResource ButtonStickyIconStyle}"
            Tag="{Binding IsTopMost}"
            ToolTip="{DynamicResource Topmost}" />
        <StackPanel
            Margin="0,0,10,0"
            FlowDirection="RightToLeft"
            Orientation="Horizontal">
            <Button
                Width="28"
                Height="28"
                HorizontalAlignment="Right"
                Command="{Binding EscCommand}"
                CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                Content="&#xe660;"
                FontSize="{DynamicResource FontSize21}"
                FontWeight="Bold"
                Style="{DynamicResource ButtonCloseStyle}"
                ToolTip="{DynamicResource WindowClose}"
                Visibility="{Binding IsShowClose, Converter={StaticResource BooleanToVisibilityConverter}}" />
            <Button
                Width="28"
                Height="28"
                HorizontalAlignment="Right"
                Command="{Binding MinimalCommand}"
                CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                Content="&#xe676;"
                FontSize="{DynamicResource FontSize21}"
                FontWeight="Bold"
                Style="{DynamicResource ButtonIconStyle}"
                ToolTip="{DynamicResource WindowMinimized}"
                Visibility="{Binding ShowMinimalBtn, Converter={StaticResource BooleanToVisibilityConverter}}" />
            <Button
                HorizontalAlignment="Right"
                Command="{Binding NotifyIconVM.OpenPreferenceCommand}"
                Content="&#xe656;"
                Style="{DynamicResource ButtonIconStyle}"
                ToolTip="{DynamicResource Preference}"
                Visibility="{Binding IsShowPreference, Converter={StaticResource BooleanToVisibilityConverter}}" />
            <ToggleButton
                x:Name="BTN_Service"
                HorizontalAlignment="Right"
                Content="&#xe726;"
                FontSize="{DynamicResource FontSize19}"
                Style="{DynamicResource ToggleButtonIconStyle}"
                ToolTip="{DynamicResource ConfigureService}"
                Visibility="{Binding IsShowConfigureService, Converter={StaticResource BooleanToVisibilityConverter}}">
                <i:Interaction.Triggers>
                    <i:EventTrigger EventName="Checked">
                        <i:ChangePropertyAction
                            PropertyName="IsOpen"
                            TargetObject="{Binding ElementName=PART_Popup_Svc}"
                            Value="True" />
                    </i:EventTrigger>
                </i:Interaction.Triggers>
            </ToggleButton>
            <Popup
                x:Name="PART_Popup_Svc"
                MinWidth="80"
                MinHeight="30"
                MaxHeight="400"
                AllowsTransparency="True"
                Placement="Bottom"
                PlacementTarget="{Binding ElementName=BTN_Service}"
                PopupAnimation="Slide"
                StaysOpen="False">
                <i:Interaction.Triggers>
                    <i:EventTrigger EventName="Closed">
                        <i:ChangePropertyAction
                            PropertyName="IsChecked"
                            TargetObject="{Binding ElementName=BTN_Service}"
                            Value="{Binding ElementName=BTN_Service, Path=IsMouseOver}" />
                    </i:EventTrigger>
                </i:Interaction.Triggers>
                <Border Style="{DynamicResource BorderStyle}">
                    <ScrollViewer
                        Margin="0,5"
                        FlowDirection="LeftToRight"
                        HorizontalScrollBarVisibility="Disabled"
                        VerticalScrollBarVisibility="Auto">
                        <ListBox
                            Name="ServiceListBox"
                            Margin="5"
                            Background="Transparent"
                            BorderThickness="0"
                            ItemTemplateSelector="{StaticResource HeaderTemplateSelector}"
                            PreviewMouseWheel="ServiceListBox_PreviewMouseWheel">
                            <ListBox.ItemsSource>
                                <CompositeCollection>
                                    <CollectionContainer Collection="{Binding Source={StaticResource translator}, Path=Data}" />
                                    <Separator />
                                    <CollectionContainer Collection="{Binding Source={StaticResource ocr}, Path=Data}" />
                                    <Separator />
                                    <CollectionContainer Collection="{Binding Source={StaticResource tts}, Path=Data}" />
                                </CompositeCollection>
                            </ListBox.ItemsSource>
                            <i:Interaction.Triggers>
                                <i:EventTrigger EventName="PreviewMouseLeftButtonUp">
                                    <i:InvokeCommandAction Command="{Binding SelectedServiceCommand}">
                                        <i:InvokeCommandAction.CommandParameter>
                                            <MultiBinding Converter="{StaticResource MultiValue2ListConverter}">
                                                <Binding ElementName="ServiceListBox" Path="SelectedItem" />
                                                <Binding ElementName="PART_Popup_Svc" />
                                            </MultiBinding>
                                        </i:InvokeCommandAction.CommandParameter>
                                    </i:InvokeCommandAction>
                                </i:EventTrigger>
                            </i:Interaction.Triggers>
                        </ListBox>
                    </ScrollViewer>
                </Border>
            </Popup>
            <ToggleButton
                x:Name="MainOcrLangTb"
                HorizontalAlignment="Right"
                Content="&#xe62f;"
                Style="{DynamicResource ToggleButtonIconStyle}"
                ToolTip="{DynamicResource SilentOCRLang}"
                Visibility="{Binding ShowMainOcrLang, Converter={StaticResource BooleanToVisibilityConverter}}">
                <i:Interaction.Triggers>
                    <i:EventTrigger EventName="Checked">
                        <i:ChangePropertyAction
                            PropertyName="IsOpen"
                            TargetObject="{Binding ElementName=PART_Popup_OcrLang}"
                            Value="True" />
                    </i:EventTrigger>
                </i:Interaction.Triggers>
            </ToggleButton>
            <Popup
                x:Name="PART_Popup_OcrLang"
                MinWidth="80"
                MinHeight="30"
                MaxHeight="400"
                AllowsTransparency="True"
                Placement="Bottom"
                PlacementTarget="{Binding ElementName=MainOcrLangTb}"
                PopupAnimation="Slide"
                StaysOpen="False">
                <i:Interaction.Triggers>
                    <i:EventTrigger EventName="Closed">
                        <i:ChangePropertyAction
                            PropertyName="IsChecked"
                            TargetObject="{Binding ElementName=MainOcrLangTb}"
                            Value="{Binding ElementName=MainOcrLangTb, Path=IsMouseOver}" />
                    </i:EventTrigger>
                </i:Interaction.Triggers>
                <Border Style="{DynamicResource BorderStyle}">
                    <ScrollViewer
                        MaxHeight="400"
                        FlowDirection="LeftToRight"
                        HorizontalScrollBarVisibility="Disabled">
                        <ListBox
                            x:Name="MainOcrLangLb"
                            proxy:LangAwareSelector.IsLangAware="True"
                            Background="Transparent"
                            BorderThickness="0"
                            PreviewMouseWheel="ServiceListBox_PreviewMouseWheel"
                            ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                            SelectedValue="{Binding CommonSettingVM.MainOcrLang, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                            SelectedValuePath="Value">
                            <ListBox.ItemsSource>
                                <MultiBinding Converter="{StaticResource MultiLangFilterConverter}">
                                    <Binding Source="{proxy:LangEnumeration {x:Type model:LangEnum}}" />
                                    <Binding Path="InputVM.OftenUsedLang" />
                                </MultiBinding>
                            </ListBox.ItemsSource>
                            <ListBox.ItemTemplate>
                                <DataTemplate DataType="{x:Type model:LangEnum}">
                                    <Border
                                        x:Name="MainOcrLangBorder"
                                        Margin="3"
                                        Background="{DynamicResource BorderBackground}"
                                        BorderBrush="{x:Null}"
                                        CornerRadius="5">
                                        <TextBlock
                                            Margin="3,5"
                                            HorizontalAlignment="Left"
                                            FontSize="{DynamicResource FontSize14}"
                                            Text="{Binding Description}"
                                            TextTrimming="CharacterEllipsis" />
                                    </Border>
                                    <DataTemplate.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter TargetName="MainOcrLangBorder" Property="Background" Value="{DynamicResource BtnMouseOverBackground}" />
                                        </Trigger>
                                        <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=ListBoxItem}, Path=IsSelected}" Value="True">
                                            <Setter TargetName="MainOcrLangBorder" Property="Background" Value="{DynamicResource BtnMouseOverBackground}" />
                                        </DataTrigger>
                                    </DataTemplate.Triggers>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                            <i:Interaction.Triggers>
                                <i:EventTrigger EventName="PreviewMouseLeftButtonUp">
                                    <i:InvokeCommandAction Command="{Binding Source={StaticResource Vm}, Path=Data.SelectedMainOcrLanguageCommand}">
                                        <i:InvokeCommandAction.CommandParameter>
                                            <MultiBinding Converter="{StaticResource MultiValue2ListConverter}">
                                                <Binding ElementName="MainOcrLangLb" Path="SelectedItem" />
                                                <Binding ElementName="PART_Popup_OcrLang" />
                                            </MultiBinding>
                                        </i:InvokeCommandAction.CommandParameter>
                                    </i:InvokeCommandAction>
                                </i:EventTrigger>
                            </i:Interaction.Triggers>
                        </ListBox>
                    </ScrollViewer>
                </Border>
            </Popup>
            <Button
                Width="30"
                Height="30"
                HorizontalAlignment="Right"
                Command="{Binding MouseHookCommand}"
                CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                Content="&#xeb94;"
                Style="{DynamicResource ButtonCrosswordsIconStyle}"
                Tag="{Binding IsEnableMosehook}"
                ToolTip="{DynamicResource MouseHook}"
                Visibility="{Binding IsShowMousehook, Converter={StaticResource BooleanToVisibilityConverter}}" />
            <Button
                Width="30"
                Height="30"
                HorizontalAlignment="Right"
                Command="{Binding AutoTranslateCommand}"
                CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                Content="&#xe6f6;"
                FontSize="{DynamicResource FontSize19}"
                Style="{DynamicResource ButtonCrosswordsIconStyle}"
                Tag="{Binding IsAutoTranslate}"
                ToolTip="{DynamicResource AutoTranslate}"
                Visibility="{Binding IsShowAutoTranslate, Converter={StaticResource BooleanToVisibilityConverter}}" />
            <Button
                Width="30"
                Height="30"
                HorizontalAlignment="Right"
                Command="{Binding IncrementalTranslationCommand}"
                CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                Content="&#xeb78;"
                Style="{DynamicResource ButtonCrosswordsIconStyle}"
                Tag="{Binding IsEnableIncrementalTranslation}"
                ToolTip="{DynamicResource IncrementalTranslation}"
                Visibility="{Binding IsShowIncrementalTranslation, Converter={StaticResource BooleanToVisibilityConverter}}" />
            <Button
                Width="30"
                Height="30"
                HorizontalAlignment="Right"
                Command="{Binding OnlyShowRetCommand}"
                CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                Content="&#xe65b;"
                FontSize="{DynamicResource FontSize21}"
                Style="{DynamicResource ButtonCrosswordsIconStyle}"
                Tag="{Binding IsEnableOnlyShowRet}"
                ToolTip="{DynamicResource OnlyShowOutput}"
                Visibility="{Binding IsShowOnlyShowRet, Converter={StaticResource BooleanToVisibilityConverter}}" />
            <Button
                HorizontalAlignment="Right"
                Command="{Binding NotifyIconVM.ScreenShotTranslateCommand}"
                CommandParameter="header"
                Content="&#xe679;"
                Style="{DynamicResource ButtonIconStyle}"
                ToolTip="{DynamicResource ScreenshotTranslate}"
                Visibility="{Binding IsShowScreenshot, Converter={StaticResource BooleanToVisibilityConverter}}" />
            <Button
                HorizontalAlignment="Right"
                Command="{Binding NotifyIconVM.SilentOCRCommand}"
                CommandParameter="header"
                Content="&#xe861;"
                Style="{DynamicResource ButtonIconStyle}"
                ToolTip="{DynamicResource SilentOCR}"
                Visibility="{Binding IsShowSilentOCR, Converter={StaticResource BooleanToVisibilityConverter}}" />
            <Button
                HorizontalAlignment="Right"
                Command="{Binding NotifyIconVM.OCRCommand}"
                CommandParameter="header"
                Content="&#xe695;"
                Style="{DynamicResource ButtonIconStyle}"
                ToolTip="{DynamicResource OCR}"
                Visibility="{Binding IsShowOCR, Converter={StaticResource BooleanToVisibilityConverter}}" />
            <Button
                Width="30"
                Height="30"
                HorizontalAlignment="Right"
                Command="{Binding NotifyIconVM.ClipboardMonitorCommand}"
                CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                Content="&#xeabc;"
                FontWeight="Bold"
                Style="{DynamicResource ButtonCrosswordsIconStyle}"
                Tag="{Binding NotifyIconVM.IsEnabledClipboardMonitor}"
                ToolTip="{DynamicResource ClipboardMonitor}"
                Visibility="{Binding IsShowClipboardMonitor, Converter={StaticResource BooleanToVisibilityConverter}}" />
            <Button
                HorizontalAlignment="Right"
                Command="{Binding NotifyIconVM.QRCodeCommand}"
                CommandParameter="header"
                Content="&#xe642;"
                Style="{DynamicResource ButtonIconStyle}"
                ToolTip="{DynamicResource QRCode}"
                Visibility="{Binding IsShowQRCode, Converter={StaticResource BooleanToVisibilityConverter}}" />
            <Button
                HorizontalAlignment="Right"
                Command="{Binding NotifyIconVM.OpenHistoryCommand}"
                CommandParameter="header"
                Content="&#xe63f;"
                FontSize="{DynamicResource FontSize20}"
                Style="{DynamicResource ButtonIconStyle}"
                ToolTip="{DynamicResource History}"
                Visibility="{Binding IsShowHistory, Converter={StaticResource BooleanToVisibilityConverter}}" />
            <TextBlock
                props:ThemeProps.Foreground="Red"
                FontWeight="Bold"
                Text="{DynamicResource DevelopmentVersion}"
                Visibility="{Binding IsDebug, Converter={StaticResource BooleanToVisibilityConverter}}" />
        </StackPanel>
    </Grid>
</UserControl>