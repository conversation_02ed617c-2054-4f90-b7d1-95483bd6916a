﻿<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="clr-namespace:STranslate.Style.Controls"
    xmlns:props="clr-namespace:STranslate.Style.Themes">

    <!--  // 普通图标按钮 //  -->
    <Style x:Key="ButtonIconStyle" TargetType="Button">
        <Setter Property="FontFamily" Value="{DynamicResource IconFont}" />
        <Setter Property="FontSize" Value="{DynamicResource FontSize18}" />
        <Setter Property="props:ThemeProps.Background" Value="{DynamicResource BorderBackground}" />
        <Setter Property="props:ThemeProps.Foreground" Value="{DynamicResource BtnForeground}" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="Height" Value="30" />
        <Setter Property="Focusable" Value="False" />
        <Setter Property="Width" Value="30" />
        <Setter Property="Margin" Value="3" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border
                        x:Name="border"
                        Width="{TemplateBinding Width}"
                        Height="{TemplateBinding Height}"
                        Padding="{TemplateBinding Padding}"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="5"
                        SnapsToDevicePixels="true">
                        <TextBlock
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            props:ThemeProps.Foreground="{TemplateBinding Foreground}"
                            FontFamily="{TemplateBinding FontFamily}"
                            FontSize="{TemplateBinding FontSize}"
                            Text="{TemplateBinding Content}" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnMouseOverBackground}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="true">
                            <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnPressedBackground}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnDisabledBackground}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  // 复制按钮 //  -->
    <Style x:Key="ButtonCopyIconStyle" TargetType="Button">
        <Setter Property="FontFamily" Value="{DynamicResource IconFont}" />
        <Setter Property="FontSize" Value="{DynamicResource FontSize18}" />
        <Setter Property="Focusable" Value="False" />
        <Setter Property="props:ThemeProps.Foreground" Value="{DynamicResource BtnForeground}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border
                        x:Name="border"
                        Width="{TemplateBinding Width}"
                        Height="{TemplateBinding Height}"
                        Padding="4"
                        CornerRadius="5"
                        SnapsToDevicePixels="true">
                        <TextBlock
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            props:ThemeProps.Foreground="{TemplateBinding Foreground}"
                            FontFamily="{TemplateBinding FontFamily}"
                            FontSize="{TemplateBinding FontSize}"
                            Text="{TemplateBinding Content}" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnMouseOverBackground}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="true">
                            <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnPressedBackground}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnDisabledBackground}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  // 划词按钮 //  -->
    <Style x:Key="ButtonCrosswordsIconStyle" TargetType="Button">
        <Setter Property="FontFamily" Value="{DynamicResource IconFont}" />
        <Setter Property="Focusable" Value="False" />
        <Setter Property="FontSize" Value="{DynamicResource FontSize18}" />
        <Setter Property="props:ThemeProps.Foreground" Value="{DynamicResource BtnForeground}" />
        <Setter Property="Height" Value="35" />
        <Setter Property="Width" Value="35" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border
                        x:Name="border"
                        Width="{TemplateBinding Width}"
                        Height="{TemplateBinding Height}"
                        CornerRadius="5"
                        SnapsToDevicePixels="true">
                        <TextBlock
                            x:Name="textblock"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            FontFamily="{TemplateBinding FontFamily}"
                            FontSize="{TemplateBinding FontSize}"
                            FontWeight="{TemplateBinding FontWeight}"
                            Tag="{TemplateBinding Tag}"
                            Text="{TemplateBinding Content}" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="Tag" Value="False">
                            <Setter TargetName="textblock" Property="props:ThemeProps.Foreground" Value="{DynamicResource BtnForeground}" />
                        </Trigger>
                        <Trigger Property="Tag" Value="True">
                            <Setter TargetName="textblock" Property="props:ThemeProps.Foreground" Value="{DynamicResource ThemeAccentColor}" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnMouseOverBackground}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="true">
                            <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnPressedBackground}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnDisabledBackground}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  // 置顶Style //  -->
    <Style x:Key="ButtonStickyIconStyle" TargetType="Button">
        <Setter Property="FontFamily" Value="{DynamicResource IconFont}" />
        <Setter Property="Focusable" Value="False" />
        <Setter Property="FontSize" Value="{DynamicResource FontSize18}" />
        <Setter Property="props:ThemeProps.Foreground" Value="{DynamicResource BtnForeground}" />
        <Setter Property="props:ThemeProps.Background" Value="{DynamicResource BorderBackground}" />
        <Setter Property="Height" Value="30" />
        <Setter Property="Width" Value="30" />
        <Setter Property="Margin" Value="3" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border
                        x:Name="border"
                        Width="{TemplateBinding Width}"
                        Height="{TemplateBinding Height}"
                        Padding="{TemplateBinding Padding}"
                        Background="{TemplateBinding Background}"
                        CornerRadius="5"
                        SnapsToDevicePixels="true">
                        <TextBlock
                            x:Name="textblock"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            props:ThemeProps.Foreground="{TemplateBinding Foreground}"
                            FontFamily="{TemplateBinding FontFamily}"
                            FontSize="{TemplateBinding FontSize}"
                            Tag="{TemplateBinding Tag}"
                            Text="{TemplateBinding Content}" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="Tag" Value="False">
                            <Setter TargetName="textblock" Property="props:ThemeProps.Foreground" Value="{DynamicResource BtnForeground}" />
                        </Trigger>
                        <Trigger Property="Tag" Value="True">
                            <Setter TargetName="textblock" Property="props:ThemeProps.Foreground" Value="{DynamicResource ThemeAccentColor}" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnMouseOverBackground}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="true">
                            <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnPressedBackground}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnDisabledBackground}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  // 关闭按钮 //  -->
    <Style x:Key="ButtonCloseStyle" TargetType="Button">
        <Setter Property="FontFamily" Value="{DynamicResource IconFont}" />
        <Setter Property="FontSize" Value="{DynamicResource FontSize18}" />
        <Setter Property="props:ThemeProps.Background" Value="{DynamicResource BorderBackground}" />
        <Setter Property="props:ThemeProps.Foreground" Value="{DynamicResource BtnForeground}" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Height" Value="30" />
        <Setter Property="Width" Value="30" />
        <Setter Property="Margin" Value="3" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border
                        x:Name="border"
                        Width="{TemplateBinding Width}"
                        Height="{TemplateBinding Height}"
                        Padding="{TemplateBinding Padding}"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="5"
                        SnapsToDevicePixels="true">
                        <TextBlock
                            x:Name="textblock"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            props:ThemeProps.Foreground="{TemplateBinding Foreground}"
                            FontFamily="{TemplateBinding FontFamily}"
                            FontSize="{TemplateBinding FontSize}"
                            Text="{TemplateBinding Content}" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnClosedMouseOverBackground}" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="textblock" Property="Foreground" Value="{DynamicResource BorderBackground}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="true">
                            <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnClosedPressedBackground}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnDisabledBackground}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  // 默认Style //  -->
    <Style TargetType="Button">
        <Setter Property="FontSize" Value="{DynamicResource FontSize18}" />
        <Setter Property="props:ThemeProps.Background" Value="{DynamicResource BtnBackground}" />
        <Setter Property="props:ThemeProps.Foreground" Value="{DynamicResource TextForeground}" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Margin" Value="3" />
        <Setter Property="Padding" Value="3" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border
                        x:Name="border"
                        MinHeight="20"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="5"
                        SnapsToDevicePixels="true">
                        <TextBlock
                            x:Name="tb"
                            Margin="3,0"
                            Padding="3"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            FontFamily="{TemplateBinding FontFamily}"
                            FontSize="{TemplateBinding FontSize}"
                            Foreground="{TemplateBinding Foreground}"
                            Text="{TemplateBinding Content}" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnMouseOverBackground}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="true">
                            <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnPressedBackground}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnDisabledBackground}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="tb" Property="Foreground" Value="{DynamicResource TextBlockDisabledForeground}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type controls:BtnContentMore}">
        <Setter Property="FontSize" Value="{DynamicResource FontSize18}" />
        <Setter Property="props:ThemeProps.Background" Value="{DynamicResource BtnBackground}" />
        <Setter Property="props:ThemeProps.Foreground" Value="{DynamicResource TextForeground}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type controls:BtnContentMore}">
                    <Border
                        x:Name="border"
                        MinHeight="20"
                        Background="{TemplateBinding Background}"
                        CornerRadius="5"
                        SnapsToDevicePixels="true">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock
                                x:Name="tb"
                                Margin="3,0,0,0"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                FontFamily="{TemplateBinding FontFamily}"
                                FontSize="{TemplateBinding FontSize}"
                                Foreground="{TemplateBinding Foreground}"
                                Text="{TemplateBinding Content}" />
                            <TextBlock Text="(" />
                            <TextBlock
                                x:Name="tbMore"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                FontFamily="{TemplateBinding FontFamily}"
                                FontSize="{TemplateBinding FontSize}"
                                Foreground="{TemplateBinding Foreground}"
                                Text="{TemplateBinding ContentMore}" />
                            <TextBlock Text=") " />
                        </StackPanel>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnMouseOverBackground}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="true">
                            <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnPressedBackground}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnDisabledBackground}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="tb" Property="Foreground" Value="{DynamicResource TextBlockDisabledForeground}" />
                            <Setter TargetName="tbMore" Property="Foreground" Value="{DynamicResource TextBlockDisabledForeground}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--#region // Preset Prompt Start //-->
    <ControlTemplate x:Key="LeftBtnTemplate" TargetType="Button">
        <Border
            x:Name="border"
            MinHeight="20"
            Background="{TemplateBinding Background}"
            CornerRadius="5,0,0,5"
            SnapsToDevicePixels="true">
            <TextBlock
                x:Name="tb"
                Padding="3"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontFamily="{TemplateBinding FontFamily}"
                FontSize="{TemplateBinding FontSize}"
                Foreground="{TemplateBinding Foreground}"
                Text="{TemplateBinding Content}" />
        </Border>
        <ControlTemplate.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnMouseOverBackground}" />
            </Trigger>
            <Trigger Property="IsPressed" Value="true">
                <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnPressedBackground}" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnDisabledBackground}" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter TargetName="tb" Property="Foreground" Value="{DynamicResource TextBlockDisabledForeground}" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>
    <ControlTemplate x:Key="RightBtnTemplate" TargetType="Button">
        <Border
            x:Name="border"
            MinHeight="20"
            Background="{TemplateBinding Background}"
            CornerRadius="0,5,5,0"
            SnapsToDevicePixels="true">
            <TextBlock
                x:Name="tb"
                Padding="3"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontFamily="{TemplateBinding FontFamily}"
                FontSize="{TemplateBinding FontSize}"
                Foreground="{TemplateBinding Foreground}"
                Text="{TemplateBinding Content}" />
        </Border>
        <ControlTemplate.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnMouseOverBackground}" />
            </Trigger>
            <Trigger Property="IsPressed" Value="true">
                <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnPressedBackground}" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnDisabledBackground}" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter TargetName="tb" Property="Foreground" Value="{DynamicResource TextBlockDisabledForeground}" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>
    <ControlTemplate x:Key="BtnTemplate" TargetType="Button">
        <Border
            x:Name="border"
            MinHeight="20"
            Background="{TemplateBinding Background}"
            SnapsToDevicePixels="true">
            <TextBlock
                x:Name="tb"
                Padding="3"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontFamily="{TemplateBinding FontFamily}"
                FontSize="{TemplateBinding FontSize}"
                Foreground="{TemplateBinding Foreground}"
                Text="{TemplateBinding Content}" />
        </Border>
        <ControlTemplate.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnMouseOverBackground}" />
            </Trigger>
            <Trigger Property="IsPressed" Value="true">
                <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnPressedBackground}" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnDisabledBackground}" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter TargetName="tb" Property="Foreground" Value="{DynamicResource TextBlockDisabledForeground}" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>
    <Style x:Key="PromptBaseStyle" TargetType="Button">
        <Setter Property="props:ThemeProps.Background" Value="{DynamicResource BtnBackground}" />
        <Setter Property="props:ThemeProps.Foreground" Value="{DynamicResource TextForeground}" />
        <Setter Property="BorderThickness" Value="0" />
    </Style>
    <Style
        x:Key="PromptBtnStyle"
        BasedOn="{StaticResource PromptBaseStyle}"
        TargetType="Button">
        <Setter Property="Template" Value="{DynamicResource BtnTemplate}" />
    </Style>
    <Style
        x:Key="PromptLeftBtnStyle"
        BasedOn="{StaticResource PromptBaseStyle}"
        TargetType="Button">
        <Setter Property="Template" Value="{DynamicResource LeftBtnTemplate}" />
    </Style>
    <Style
        x:Key="PromptRightBtnStyle"
        BasedOn="{StaticResource PromptBaseStyle}"
        TargetType="Button">
        <Setter Property="Template" Value="{DynamicResource RightBtnTemplate}" />
    </Style>
    <Style x:Key="VerticalSeparatorStyle" TargetType="Separator">
        <Setter Property="HorizontalAlignment" Value="Left" />
        <Setter Property="props:ThemeProps.BorderBrush" Value="{DynamicResource BorderBrushColor}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Width" Value="16" />
        <Setter Property="FontWeight" Value="Bold" />
        <Setter Property="LayoutTransform">
            <Setter.Value>
                <RotateTransform Angle="90" />
            </Setter.Value>
        </Setter>
    </Style>
    <!--#endregion // Preset Prompt End //-->
</ResourceDictionary>