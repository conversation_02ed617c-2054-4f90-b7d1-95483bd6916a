﻿<Window
    x:Class="STranslate.Views.Preference.TranslatorSelectDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:common="clr-namespace:STranslate.Style.Commons;assembly=STranslate.Style"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:model="clr-namespace:STranslate.Model;assembly=STranslate.Model"
    xmlns:props="clr-namespace:STranslate.Style.Themes;assembly=STranslate.Style"
    xmlns:vm="clr-namespace:STranslate.ViewModels.Preference"
    Name="DialogWindow"
    Title="TranslatorSelectDialog"
    Width="650"
    MaxHeight="500"
    d:DataContext="{d:DesignInstance Type=vm:TranslatorSelectViewModel}"
    props:ThemeProps.Background="{DynamicResource NavigationBackground}"
    props:ThemeProps.BorderBrush="{DynamicResource BorderBrushColor}"
    BorderThickness="1"
    FontFamily="{DynamicResource UserFont}"
    Icon="{DynamicResource Icon}"
    SizeToContent="Height"
    Topmost="True"
    WindowStartupLocation="CenterScreen"
    mc:Ignorable="d">
    <WindowChrome.WindowChrome>
        <WindowChrome />
    </WindowChrome.WindowChrome>
    <Window.InputBindings>
        <KeyBinding
            Key="Esc"
            Command="{Binding CloseCommand}"
            CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}" />
    </Window.InputBindings>
    <Window.Resources>
        <DataTemplate x:Key="TranslatorTemplate" DataType="{x:Type model:ITranslator}">
            <Border
                x:Name="ServiceControl"
                MinWidth="200"
                MinHeight="40"
                props:ThemeProps.Background="{DynamicResource BorderContentBackground}"
                Style="{DynamicResource BorderPopupStyle}">
                <Grid Margin="5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="40" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="40" />
                    </Grid.ColumnDefinitions>

                    <Image Width="20" Source="{Binding Icon, Converter={StaticResource String2IconConverter}}" />

                    <TextBlock
                        Grid.Column="1"
                        VerticalAlignment="Center"
                        Text="{Binding Name}" />

                    <Border
                        Grid.Column="2"
                        Padding="1"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Background="{Binding Type, Converter={StaticResource ServiceType2BrushConverter}}"
                        CornerRadius="6">
                        <TextBlock
                            Padding="2,0"
                            props:ThemeProps.Foreground="{DynamicResource ServiceTypeForeground}"
                            FontSize="{DynamicResource FontSize12}"
                            Text="{Binding Type, Converter={StaticResource ServiceType2StringConverter}}" />
                    </Border>
                </Grid>
                <i:Interaction.Triggers>
                    <i:EventTrigger EventName="PreviewMouseLeftButtonUp">
                        <i:InvokeCommandAction Command="{Binding DataContext.SelectItemCommand, RelativeSource={RelativeSource AncestorType=ItemsControl}}">
                            <i:InvokeCommandAction.CommandParameter>
                                <MultiBinding Converter="{StaticResource MultiValue2ListConverter}">
                                    <Binding />
                                    <Binding ElementName="DialogWindow" />
                                </MultiBinding>
                            </i:InvokeCommandAction.CommandParameter>
                        </i:InvokeCommandAction>
                    </i:EventTrigger>
                </i:Interaction.Triggers>
            </Border>
            <DataTemplate.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter TargetName="ServiceControl" Property="props:ThemeProps.Background" Value="{DynamicResource BtnMouseOverBackground}" />
                    <Setter TargetName="ServiceControl" Property="Cursor" Value="Hand" />
                </Trigger>
            </DataTemplate.Triggers>
        </DataTemplate>
    </Window.Resources>

    <Grid Margin="5">
        <Grid.RowDefinitions>
            <RowDefinition Height="35" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>
        <!--  // Header //  -->
        <Border
            Name="Header"
            props:ThemeProps.Background="{DynamicResource BorderBackground}"
            CornerRadius="5"
            MouseDown="Header_MouseDown"
            WindowChrome.IsHitTestVisibleInChrome="True">

            <Grid>
                <!--  // Title //  -->
                <StackPanel
                    Margin="20,0"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center"
                    Orientation="Horizontal">
                    <Image Height="24" Source="{DynamicResource STranslate}" />
                    <TextBlock
                        Margin="16,0,0,0"
                        VerticalAlignment="Center"
                        props:ThemeProps.Foreground="{DynamicResource NavigationForeground}"
                        FontSize="{DynamicResource FontSize20}"
                        FontWeight="Bold"
                        Text="{DynamicResource TranslatorSelector.Title}" />
                </StackPanel>

                <!--  // Button //  -->
                <StackPanel
                    Margin="15,0"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Center"
                    Orientation="Horizontal">
                    <Button
                        Command="{Binding MinimizeCommand}"
                        CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                        Content="&#xe676;"
                        FontSize="{DynamicResource FontSize20}"
                        Style="{DynamicResource ButtonIconStyle}"
                        Visibility="Collapsed" />
                    <Button
                        Command="{Binding MaximizeCommand}"
                        CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                        Content="{Binding MaximizeContent}"
                        FontWeight="Bold"
                        Style="{DynamicResource ButtonIconStyle}"
                        Visibility="Collapsed" />
                    <Button
                        Command="{Binding CloseCommand}"
                        CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                        Content="&#xe64d;"
                        FontSize="{DynamicResource FontSize16}"
                        Style="{DynamicResource ButtonCloseStyle}" />
                </StackPanel>
            </Grid>
        </Border>

        <ScrollViewer Grid.Row="1">
            <StackPanel>
                <TextBlock
                    Margin="10,5,0,0"
                    props:ThemeProps.Foreground="{DynamicResource NavigationForeground}"
                    FontSize="{DynamicResource FontSize16}"
                    FontWeight="Bold"
                    Text="{DynamicResource TranslatorSelector.SelfBuild}"
                    ToolTip="{DynamicResource TranslatorSelector.SelfBuild.Tooltip}" />
                <!--  自建翻译服务  -->
                <ItemsControl
                    Background="Transparent"
                    FontSize="{DynamicResource FontSize18}"
                    ItemTemplate="{DynamicResource TranslatorTemplate}"
                    ItemsSource="{Binding Translators, Converter={StaticResource ServiceTypeFilterConverter}, ConverterParameter=selfBuild}">
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <WrapPanel />
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>
                    <ItemsControl.ItemContainerStyle>
                        <Style>
                            <Setter Property="Control.VerticalAlignment" Value="Center" />
                        </Style>
                    </ItemsControl.ItemContainerStyle>
                </ItemsControl>

                <TextBlock
                    Margin="10,5,0,0"
                    props:ThemeProps.Foreground="{DynamicResource NavigationForeground}"
                    FontSize="{DynamicResource FontSize16}"
                    FontWeight="Bold"
                    Text="{DynamicResource TranslatorSelector.BuiltIn}"
                    ToolTip="{DynamicResource TranslatorSelector.BuiltIn.Tooltip}" />
                <!--  本地翻译服务  -->
                <ItemsControl
                    Background="Transparent"
                    FontSize="{DynamicResource FontSize18}"
                    ItemTemplate="{DynamicResource TranslatorTemplate}"
                    ItemsSource="{Binding Translators, Converter={StaticResource ServiceTypeFilterConverter}, ConverterParameter=local}">
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <WrapPanel />
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>
                    <ItemsControl.ItemContainerStyle>
                        <Style>
                            <Setter Property="Control.VerticalAlignment" Value="Center" />
                        </Style>
                    </ItemsControl.ItemContainerStyle>
                </ItemsControl>

                <TextBlock
                    Margin="10,5,0,0"
                    props:ThemeProps.Foreground="{DynamicResource NavigationForeground}"
                    FontSize="{DynamicResource FontSize16}"
                    FontWeight="Bold"
                    Text="{DynamicResource TranslatorSelector.Official}"
                    ToolTip="{DynamicResource TranslatorSelector.Official.Tooltip}" />
                <!--  官方翻译服务  -->
                <ItemsControl
                    Background="Transparent"
                    FontSize="{DynamicResource FontSize18}"
                    ItemTemplate="{DynamicResource TranslatorTemplate}"
                    ItemsSource="{Binding Translators, Converter={StaticResource ServiceTypeFilterConverter}, ConverterParameter=official}">
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <WrapPanel />
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>
                    <ItemsControl.ItemContainerStyle>
                        <Style>
                            <Setter Property="Control.VerticalAlignment" Value="Center" />
                        </Style>
                    </ItemsControl.ItemContainerStyle>
                </ItemsControl>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</Window>