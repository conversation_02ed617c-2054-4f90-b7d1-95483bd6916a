﻿<UserControl
    x:Class="STranslate.Views.Preference.OCR.GeminiOCRPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:common="clr-namespace:STranslate.Style.Commons;assembly=STranslate.Style"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:model="clr-namespace:STranslate.Model;assembly=STranslate.Model"
    xmlns:props="clr-namespace:STranslate.Style.Themes;assembly=STranslate.Style"
    xmlns:vm="clr-namespace:STranslate.ViewModels.Preference.OCR"
    d:DataContext="{d:DesignInstance Type=vm:GeminiOCR}"
    d:DesignHeight="450"
    d:DesignWidth="800"
    Background="Transparent"
    FontSize="{DynamicResource FontSize18}"
    mc:Ignorable="d">
    <Border
        Padding="10,0,0,0"
        props:ThemeProps.BorderBrush="{DynamicResource BorderBrushColor}"
        BorderThickness="1"
        CornerRadius="4">
        <ScrollViewer>
            <StackPanel>
                <Grid Margin="0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="80" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    <TextBlock Text="{DynamicResource Service.Name}" />

                    <common:PlaceholderTextBox
                        Grid.Column="1"
                        MinWidth="160"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        Placeholder="GeminiOCR"
                        Text="{Binding Name, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" />
                </Grid>

                <Grid Margin="0,10" Visibility="Collapsed">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="80" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    <TextBlock Text="{DynamicResource Service.Icon}" />

                    <ComboBox
                        Grid.Column="1"
                        HorizontalAlignment="Left"
                        BorderThickness="1"
                        ItemsSource="{Binding Icons}"
                        SelectedValue="{Binding Icon}"
                        SelectedValuePath="Key"
                        Style="{DynamicResource IconComboBoxStyle}" />
                </Grid>

                <Grid Margin="0,10,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="80" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    <TextBlock Text="{DynamicResource Service.Api}" />

                    <common:PlaceholderTextBox
                        Grid.Column="1"
                        MinWidth="30"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        Placeholder="https://generativelanguage.googleapis.com"
                        Text="{Binding Url, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" />
                </Grid>

                <Grid Margin="0,0,0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="80" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    <TextBlock
                        Grid.Column="1"
                        FontSize="{DynamicResource FontSize14}"
                        Text="{DynamicResource Service.OCR.GeminiApi}"
                        TextWrapping="WrapWithOverflow" />
                </Grid>

                <Grid Margin="0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="80" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    <TextBlock Text="{DynamicResource Service.Model}" />

                    <common:PlaceholderTextBox
                        Grid.Column="1"
                        MinWidth="206"
                        HorizontalAlignment="Left"
                        Placeholder="gemini-1.5-flash"
                        Text="{Binding Model, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" />
                </Grid>

                <Grid Margin="0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="80" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="60" />
                    </Grid.ColumnDefinitions>
                    <TextBlock Text="{DynamicResource Service.Temperature}" ToolTip="{DynamicResource Service.Temperature.Tooltip}" />
                    <Slider
                        Grid.Column="1"
                        Height="14"
                        MinWidth="200"
                        HorizontalAlignment="Left"
                        IsSnapToTickEnabled="True"
                        Maximum="2"
                        Minimum="0"
                        TickFrequency="0.01"
                        TickPlacement="None"
                        Value="{Binding Temperature, Mode=TwoWay}" />
                    <TextBlock Grid.Column="2" Text="{Binding Temperature, StringFormat={}{0:F2}}" />
                </Grid>

                <Grid Margin="0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="80" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="38" />
                    </Grid.ColumnDefinitions>
                    <TextBlock Text="ApiKey: " />

                    <common:PlaceholderTextBox
                        Grid.Column="1"
                        MinWidth="160"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        Placeholder="ApiKey"
                        Text="{Binding AppKey, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                        Visibility="{Binding KeyHide, Converter={StaticResource BooleanToVisibilityReverseConverter}}" />
                    <PasswordBox
                        Grid.Column="1"
                        MinWidth="160"
                        HorizontalAlignment="Left"
                        common:BoundPasswordBox.Attach="True"
                        common:BoundPasswordBox.Password="{Binding AppKey, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                        Tag="ApiKey"
                        ToolTip="{Binding AppKey}"
                        Visibility="{Binding KeyHide, Converter={StaticResource BooleanToVisibilityConverter}}" />

                    <Button
                        Grid.Column="2"
                        Command="{Binding ShowEncryptInfoCommand}"
                        CommandParameter="AppKey"
                        Content="{Binding KeyHide, Converter={StaticResource BooleanToContentConverter}, ConverterParameter=ICON}"
                        Style="{DynamicResource ButtonIconStyle}" />
                </Grid>

                <Grid Margin="0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="80" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    <TextBlock VerticalAlignment="Top" Text="Prompts: " />
                    <TextBlock
                        Grid.Column="1"
                        FontSize="{DynamicResource FontSize14}"
                        Text="{DynamicResource Service.Prompt.Info}"
                        TextWrapping="Wrap" />
                </Grid>

                <!--  // Preset Prompts //  -->
                <Border
                    Margin="0,10"
                    AllowDrop="True"
                    Style="{DynamicResource BorderStyle}">
                    <i:Interaction.Triggers>
                        <i:EventTrigger EventName="DragEnter">
                            <i:ChangePropertyAction
                                PropertyName="Visibility"
                                TargetObject="{Binding ElementName=DropFilePanel}"
                                Value="Visible" />
                        </i:EventTrigger>
                        <i:EventTrigger EventName="DragLeave">
                            <i:ChangePropertyAction
                                PropertyName="Visibility"
                                TargetObject="{Binding ElementName=DropFilePanel}"
                                Value="Hidden" />
                        </i:EventTrigger>
                        <i:EventTrigger EventName="Drop">
                            <i:ChangePropertyAction
                                PropertyName="Visibility"
                                TargetObject="{Binding ElementName=DropFilePanel}"
                                Value="Hidden" />
                            <i:InvokeCommandAction Command="{Binding AddPromptFromDropCommand}" PassEventArgsToCommand="True" />
                        </i:EventTrigger>
                    </i:Interaction.Triggers>
                    <Grid>
                        <ListBox
                            Background="Transparent"
                            BorderThickness="0"
                            ItemsPanel="{StaticResource PromptTemplate}"
                            PreviewMouseWheel="ListBox_PreviewMouseWheel"
                            ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                            <ListBox.Resources>
                                <CollectionViewSource x:Key="UdpCollectionViewSource" Source="{Binding UserDefinePrompts}" />
                            </ListBox.Resources>
                            <ListBox.ItemsSource>
                                <CompositeCollection>
                                    <CollectionContainer Collection="{Binding Source={StaticResource UdpCollectionViewSource}}" />
                                    <ListBoxItem>
                                        <Button
                                            Command="{Binding AddPromptCommand}"
                                            Content="&#xe710;"
                                            Style="{DynamicResource ButtonIconStyle}"
                                            ToolTip="{DynamicResource Prompt.Add}" />
                                    </ListBoxItem>
                                    <ListBoxItem>
                                        <Button
                                            Command="{Binding AddPromptFromFileCommand}"
                                            Content="&#xe664;"
                                            FontSize="{DynamicResource FontSize16}"
                                            FontWeight="Bold"
                                            Style="{DynamicResource ButtonIconStyle}"
                                            ToolTip="{DynamicResource Prompt.Import}" />
                                    </ListBoxItem>
                                    <ListBoxItem>
                                        <Button
                                            Command="{Binding ExportCommand}"
                                            Content="&#xe6a2;"
                                            FontSize="{DynamicResource FontSize16}"
                                            FontWeight="Bold"
                                            Style="{DynamicResource ButtonIconStyle}"
                                            ToolTip="{DynamicResource Prompt.Export}" />
                                    </ListBoxItem>
                                </CompositeCollection>
                            </ListBox.ItemsSource>
                        </ListBox>
                        <Grid Name="DropFilePanel" Visibility="Hidden">
                            <Border Background="{DynamicResource BorderContentBackground}" Opacity="0.8" />
                            <TextBlock
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Text="{DynamicResource Prompt.DropImport}" />
                            <Rectangle
                                Width="250"
                                MaxHeight="80"
                                RadiusX="10"
                                RadiusY="10"
                                Stroke="{DynamicResource TextForeground}"
                                StrokeDashArray="3,4"
                                StrokeThickness="2" />
                        </Grid>
                    </Grid>
                </Border>

                <Grid Margin="0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="80" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    <TextBlock Text="Other: " />
                    <TextBlock
                        Grid.Column="1"
                        Margin="10,0"
                        ToolTip="{DynamicResource Service.OpenInBrower}">
                        <Hyperlink Click="Hyperlink_Click">
                            <ContentControl Content="{DynamicResource Service.EnterOfficialWebsite}" />
                        </Hyperlink>
                    </TextBlock>
                </Grid>

            </StackPanel>
        </ScrollViewer>
    </Border>
</UserControl>