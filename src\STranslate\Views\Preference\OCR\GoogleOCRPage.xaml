﻿<UserControl
    x:Class="STranslate.Views.Preference.OCR.GoogleOCRPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:common="clr-namespace:STranslate.Style.Commons;assembly=STranslate.Style"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:props="clr-namespace:STranslate.Style.Themes;assembly=STranslate.Style"
    xmlns:vm="clr-namespace:STranslate.ViewModels.Preference.OCR"
    d:DataContext="{d:DesignInstance Type=vm:GoogleOCR}"
    d:DesignHeight="450"
    d:DesignWidth="800"
    Background="Transparent"
    FontSize="{DynamicResource FontSize18}"
    mc:Ignorable="d">
    <Border
        Padding="10,0,0,0"
        props:ThemeProps.BorderBrush="{DynamicResource BorderBrushColor}"
        BorderThickness="1"
        CornerRadius="4">
        <StackPanel>
            <Grid Margin="0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="100" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <TextBlock Text="{DynamicResource Service.Name}" />

                <common:PlaceholderTextBox
                    Grid.Column="1"
                    MinWidth="160"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center"
                    Placeholder="GoogleOCR"
                    Text="{Binding Name, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" />
            </Grid>

            <Grid Margin="0,10" Visibility="Collapsed">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="100" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <TextBlock Text="{DynamicResource Service.Icon}" />

                <ComboBox
                    Grid.Column="1"
                    HorizontalAlignment="Left"
                    BorderThickness="1"
                    ItemsSource="{Binding Icons}"
                    SelectedValue="{Binding Icon}"
                    SelectedValuePath="Key"
                    Style="{DynamicResource IconComboBoxStyle}" />
            </Grid>

            <Grid Margin="0,10" Visibility="Collapsed">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="100" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <TextBlock Text="{DynamicResource Service.Api}" />

                <common:PlaceholderTextBox
                    Grid.Column="1"
                    MinWidth="304"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center"
                    Placeholder="https://vision.googleapis.com"
                    Text="{Binding Url, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" />
            </Grid>

            <Grid Margin="0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="100" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="38" />
                </Grid.ColumnDefinitions>
                <TextBlock Text="ApiKey: " />

                <common:PlaceholderTextBox
                    Grid.Column="1"
                    MinWidth="160"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center"
                    Placeholder="ApiKey"
                    Text="{Binding AppKey, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                    Visibility="{Binding KeyHide, Converter={StaticResource BooleanToVisibilityReverseConverter}}" />
                <PasswordBox
                    Grid.Column="1"
                    MinWidth="160"
                    HorizontalAlignment="Left"
                    common:BoundPasswordBox.Attach="True"
                    common:BoundPasswordBox.Password="{Binding AppKey, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                    Tag="ApiKey"
                    ToolTip="{Binding AppKey}"
                    Visibility="{Binding KeyHide, Converter={StaticResource BooleanToVisibilityConverter}}" />

                <Button
                    Grid.Column="2"
                    Command="{Binding ShowEncryptInfoCommand}"
                    CommandParameter="AppKey"
                    Content="{Binding KeyHide, Converter={StaticResource BooleanToContentConverter}, ConverterParameter=ICON}"
                    Style="{DynamicResource ButtonIconStyle}" />
            </Grid>

            <Grid Margin="0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="100" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <TextBlock Text="{DynamicResource Service.OCR.GoogleLocation}" ToolTip="{DynamicResource Service.OCR.GoogleLocation.Tooltip}" />

                <ToggleButton
                    Grid.Column="1"
                    HorizontalAlignment="Left"
                    IsChecked="{Binding UseWordPosition}" />
            </Grid>

            <Grid Margin="0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="100" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <TextBlock Text="Other: " />
                <TextBlock
                    Grid.Column="1"
                    Margin="10,0"
                    ToolTip="{DynamicResource Service.OpenInBrower}">
                    <Hyperlink Click="Hyperlink_Click">
                        <ContentControl Content="{DynamicResource Service.EnterOfficialWebsite}" />
                    </Hyperlink>
                </TextBlock>
            </Grid>
        </StackPanel>
    </Border>
</UserControl>