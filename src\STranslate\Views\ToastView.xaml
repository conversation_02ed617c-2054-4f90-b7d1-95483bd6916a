﻿<UserControl
    x:Class="STranslate.Views.ToastView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:props="clr-namespace:STranslate.Style.Themes;assembly=STranslate.Style"
    Width="220"
    Height="40"
    mc:Ignorable="d">
    <UserControl.RenderTransform>
        <TranslateTransform />
    </UserControl.RenderTransform>

    <Border
        Background="{DynamicResource ThemeAccentColor}"
        CornerRadius="10"
        PreviewMouseLeftButtonDown="MouseClick">
        <TextBlock
            Name="ToastTb"
            Margin="0,5"
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            props:ThemeProps.Foreground="{DynamicResource TextBlockToastViewForeground}"
            FontSize="{DynamicResource FontSize18}"
            FontWeight="Bold" />
    </Border>
</UserControl>