&lt;UserControl
    x:Class="STranslate.Views.Preference.AIAssistant.AIAssistantOpenAIPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:common="clr-namespace:STranslate.Style.Commons;assembly=STranslate.Style"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:props="clr-namespace:STranslate.Style.Themes;assembly=STranslate.Style"
    xmlns:vm="clr-namespace:STranslate.ViewModels.Preference.AIAssistant"
    d:DataContext="{d:DesignInstance Type=vm:AIAssistantOpenAI}"
    d:DesignHeight="850"
    d:DesignWidth="800"
    Background="Transparent"
    FontSize="{DynamicResource FontSize18}"
    mc:Ignorable="d"&gt;
    &lt;Border
        Padding="10,0,0,0"
        props:ThemeProps.BorderBrush="{DynamicResource BorderBrushColor}"
        BorderThickness="1"
        CornerRadius="4"&gt;
        &lt;ScrollViewer&gt;
            &lt;StackPanel&gt;
                &lt;Grid Margin="0,10"&gt;
                    &lt;Grid.ColumnDefinitions&gt;
                        &lt;ColumnDefinition Width="80" /&gt;
                        &lt;ColumnDefinition Width="*" /&gt;
                    &lt;/Grid.ColumnDefinitions&gt;
                    &lt;TextBlock Text="{DynamicResource Service.Name}" /&gt;

                    &lt;common:PlaceholderTextBox
                        Grid.Column="1"
                        MinWidth="160"
                        HorizontalAlignment="Left"
                        Placeholder="OpenAI助手"
                        Text="{Binding Name, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" /&gt;
                &lt;/Grid&gt;

                &lt;Grid Margin="0,10"&gt;
                    &lt;Grid.ColumnDefinitions&gt;
                        &lt;ColumnDefinition Width="80" /&gt;
                        &lt;ColumnDefinition Width="*" /&gt;
                    &lt;/Grid.ColumnDefinitions&gt;
                    &lt;TextBlock Text="{DynamicResource Service.Url}" /&gt;

                    &lt;common:PlaceholderTextBox
                        Grid.Column="1"
                        MinWidth="160"
                        HorizontalAlignment="Left"
                        Placeholder="https://api.openai.com"
                        Text="{Binding Url, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" /&gt;
                &lt;/Grid&gt;

                &lt;Grid Margin="0,10"&gt;
                    &lt;Grid.ColumnDefinitions&gt;
                        &lt;ColumnDefinition Width="80" /&gt;
                        &lt;ColumnDefinition Width="*" /&gt;
                    &lt;/Grid.ColumnDefinitions&gt;
                    &lt;TextBlock Text="{DynamicResource Service.AppKey}" /&gt;

                    &lt;common:PlaceholderTextBox
                        Grid.Column="1"
                        MinWidth="160"
                        HorizontalAlignment="Left"
                        Placeholder="sk-..."
                        Text="{Binding AppKey, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                        Visibility="{Binding KeyHide, Converter={StaticResource BooleanToVisibilityReverseConverter}}" /&gt;

                    &lt;PasswordBox
                        x:Name="AppKeyPasswordBox"
                        Grid.Column="1"
                        MinWidth="160"
                        HorizontalAlignment="Left"
                        PasswordChanged="AppKeyPasswordBox_PasswordChanged"
                        Visibility="{Binding KeyHide, Converter={StaticResource BooleanToVisibilityConverter}}" /&gt;
                &lt;/Grid&gt;

                &lt;Grid Margin="0,10"&gt;
                    &lt;Grid.ColumnDefinitions&gt;
                        &lt;ColumnDefinition Width="80" /&gt;
                        &lt;ColumnDefinition Width="*" /&gt;
                    &lt;/Grid.ColumnDefinitions&gt;
                    &lt;TextBlock Text="模型" /&gt;

                    &lt;ComboBox
                        Grid.Column="1"
                        MinWidth="160"
                        HorizontalAlignment="Left"
                        IsEditable="True"
                        ItemsSource="{Binding Models}"
                        SelectedItem="{Binding Model, Mode=TwoWay}" /&gt;
                &lt;/Grid&gt;

                &lt;Grid Margin="0,10"&gt;
                    &lt;Grid.ColumnDefinitions&gt;
                        &lt;ColumnDefinition Width="80" /&gt;
                        &lt;ColumnDefinition Width="*" /&gt;
                    &lt;/Grid.ColumnDefinitions&gt;
                    &lt;TextBlock Text="温度" /&gt;

                    &lt;Slider
                        Grid.Column="1"
                        MinWidth="160"
                        HorizontalAlignment="Left"
                        Maximum="2"
                        Minimum="0"
                        TickFrequency="0.1"
                        TickPlacement="BottomRight"
                        Value="{Binding Temperature, Mode=TwoWay}" /&gt;
                &lt;/Grid&gt;

                &lt;Grid Margin="0,10"&gt;
                    &lt;Grid.ColumnDefinitions&gt;
                        &lt;ColumnDefinition Width="80" /&gt;
                        &lt;ColumnDefinition Width="*" /&gt;
                    &lt;/Grid.ColumnDefinitions&gt;
                    &lt;TextBlock Text="系统提示词" /&gt;

                    &lt;TextBox
                        Grid.Column="1"
                        MinWidth="300"
                        MinHeight="80"
                        HorizontalAlignment="Left"
                        AcceptsReturn="True"
                        Text="{Binding SystemPrompt, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                        TextWrapping="Wrap"
                        VerticalScrollBarVisibility="Auto" /&gt;
                &lt;/Grid&gt;

                &lt;Grid Margin="0,10"&gt;
                    &lt;Grid.ColumnDefinitions&gt;
                        &lt;ColumnDefinition Width="80" /&gt;
                        &lt;ColumnDefinition Width="*" /&gt;
                    &lt;/Grid.ColumnDefinitions&gt;
                    &lt;TextBlock Text="保持上下文" /&gt;

                    &lt;CheckBox
                        Grid.Column="1"
                        HorizontalAlignment="Left"
                        IsChecked="{Binding KeepContext, Mode=TwoWay}" /&gt;
                &lt;/Grid&gt;

                &lt;Grid Margin="0,10"&gt;
                    &lt;Grid.ColumnDefinitions&gt;
                        &lt;ColumnDefinition Width="80" /&gt;
                        &lt;ColumnDefinition Width="*" /&gt;
                    &lt;/Grid.ColumnDefinitions&gt;
                    &lt;TextBlock Text="历史长度" /&gt;

                    &lt;common:PlaceholderTextBox
                        Grid.Column="1"
                        MinWidth="160"
                        HorizontalAlignment="Left"
                        Placeholder="20"
                        Text="{Binding MaxHistoryLength, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" /&gt;
                &lt;/Grid&gt;

                &lt;StackPanel
                    Margin="0,10"
                    HorizontalAlignment="Left"
                    Orientation="Horizontal"&gt;
                    &lt;Button
                        Margin="0,0,10,0"
                        Command="{Binding TestCommand}"
                        Content="{DynamicResource Service.Test}" /&gt;
                    &lt;Button
                        Command="{Binding ClearChatHistoryCommand}"
                        Content="清空对话历史" /&gt;
                &lt;/StackPanel&gt;

                &lt;TextBlock
                    Margin="0,10"
                    Text="预设对话模板"
                    FontWeight="Bold" /&gt;

                &lt;ItemsControl ItemsSource="{Binding UserDefinePrompts}"&gt;
                    &lt;ItemsControl.ItemTemplate&gt;
                        &lt;DataTemplate&gt;
                            &lt;Border
                                Margin="0,5"
                                Padding="10"
                                Background="{DynamicResource BackgroundBrush}"
                                BorderBrush="{DynamicResource BorderBrushColor}"
                                BorderThickness="1"
                                CornerRadius="4"&gt;
                                &lt;Grid&gt;
                                    &lt;Grid.ColumnDefinitions&gt;
                                        &lt;ColumnDefinition Width="*" /&gt;
                                        &lt;ColumnDefinition Width="Auto" /&gt;
                                    &lt;/Grid.ColumnDefinitions&gt;
                                    &lt;TextBlock
                                        Grid.Column="0"
                                        Text="{Binding Name}"
                                        FontWeight="{Binding Enabled, Converter={StaticResource BooleanToFontWeightConverter}}" /&gt;
                                    &lt;Button
                                        Grid.Column="1"
                                        Content="选择"
                                        Command="{Binding DataContext.SelectedPromptCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                        CommandParameter="{Binding}" /&gt;
                                &lt;/Grid&gt;
                            &lt;/Border&gt;
                        &lt;/DataTemplate&gt;
                    &lt;/ItemsControl.ItemTemplate&gt;
                &lt;/ItemsControl&gt;

            &lt;/StackPanel&gt;
        &lt;/ScrollViewer&gt;
    &lt;/Border&gt;
&lt;/UserControl&gt;
