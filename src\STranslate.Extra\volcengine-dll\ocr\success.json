{"request_id": "xx", "time_elapsed": "276.496872ms", "code": 10000, "message": "Success", "data": {"line_texts": ["visual.", "visual."], "line_rects": [{"x": 18, "y": 13, "width": 73, "height": 17}, {"x": 18, "y": 38, "width": 73, "height": 17}], "chars": [[{"x": 18, "y": 13, "width": 10, "height": 17, "score": 0.9853515625, "char": "v"}, {"x": 28, "y": 13, "width": 10, "height": 17, "score": 0.9990234375, "char": "i"}, {"x": 38, "y": 13, "width": 10, "height": 17, "score": 0.9990234375, "char": "s"}, {"x": 49, "y": 13, "width": 10, "height": 17, "score": 0.9990234375, "char": "u"}, {"x": 59, "y": 13, "width": 10, "height": 17, "score": 0.9990234375, "char": "a"}, {"x": 70, "y": 13, "width": 10, "height": 17, "score": 0.99365234375, "char": "l"}, {"x": 80, "y": 13, "width": 10, "height": 17, "score": 0.9716796875, "char": "."}], [{"x": 18, "y": 38, "width": 10, "height": 17, "score": 0.9853515625, "char": "v"}, {"x": 28, "y": 38, "width": 10, "height": 17, "score": 0.9990234375, "char": "i"}, {"x": 38, "y": 38, "width": 10, "height": 17, "score": 0.99853515625, "char": "s"}, {"x": 49, "y": 38, "width": 10, "height": 17, "score": 0.99853515625, "char": "u"}, {"x": 59, "y": 38, "width": 10, "height": 17, "score": 0.9990234375, "char": "a"}, {"x": 70, "y": 38, "width": 10, "height": 17, "score": 0.99365234375, "char": "l"}, {"x": 80, "y": 38, "width": 10, "height": 17, "score": 0.97265625, "char": "."}]], "polygons": [[[18, 13], [91, 13], [91, 30], [18, 30]], [[18, 38], [91, 38], [91, 55], [18, 55]]]}}