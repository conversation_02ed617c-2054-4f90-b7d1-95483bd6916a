<div align="center">

<p>
<a href="https://github.com/zggsong/STranslate" target="_blank">
<img align="center" alt="STranslate" width="200" src="./attachments/imgs/favicon.svg" />
</a>
</p>
<p>
<a href="https://github.com/ZGGSONG/STranslate/blob/main/LICENSE" target="_self">
 <img alt="Latest GitHub release" src="https://img.shields.io/github/license/ZGGSONG/STranslate" />
</a>
<a href="https://github.com/ZGGSONG/STranslate/releases/latest" target="_blank">
 <img alt="Latest GitHub release" src="https://img.shields.io/github/release/ZGGSONG/STranslate.svg" />
</a>
<a href="https://github.com/ZGGSONG/STranslate/releases" target="_self">
 <img alt="Downloads" src="https://img.shields.io/github/downloads/ZGGSONG/STranslate/total" />
</a>
<a href="https://github.com/ZGGSONG/STranslate/discussions" target="_self">
 <img alt="Discussions" src="https://img.shields.io/github/discussions/ZGGSONG/STranslate" />
</a>
</p>

<h1 align="center">STranslate</h1>

**English** | [**简体中文**](./README_ZH.md)

<a href="https://trendshift.io/repositories/6979" target="_blank"><img src="https://trendshift.io/api/badge/repositories/6979" alt="ZGGSONG%2FSTranslate | Trendshift" style="width: 250px; height: 55px;" width="250" height="55"/></a>

<p align="center">A <strong>ready-to-go</strong> AI assistant with translation and OCR capabilities developed with WPF.</p>

<small>With the update of features and the limitations of free services, it is recommended to apply for the service and make the corresponding configurations to obtain a better user experience.</small>

</div>

<br/>

## Access

| Overseas | Domestic |
| :--: | :--: |
| **[Github](https://github.com/ZGGSONG/STranslate)** | **[Gitee](https://gitee.com/zggsong/STranslate)** |

## Installation

Download the latest [Release](https://github.com/ZGGSONG/STranslate/releases) version and extract it to use.

## Usage

[Document](https://stranslate.zggsong.com)

## Discussion

If you have questions, please go to [Discussions](https://github.com/ZGGSONG/STranslate/discussions) for discussion.

## Cooperative promotion

🛠️ **Official API Partner**  

[DeerAPI](https://api.deerapi.com/register?aff=j5dj) - AI aggregation platform, one-click access to 500+ models, 30% off special offer, full support for the latest GPT4o, Grok 3, Gemini 2.5pro!

[Click to register](https://api.deerapi.com/register?aff=j5dj) for a free trial quota, also supporting the long-term development of the software.

## Acknowledgments

- Special thanks to [zu1k](https://github.com/zu1k)
- Thanks to [Bob](https://bobtranslate.com/guide/) for inspiration
- Thanks to [LxgwWenKai](https://github.com/lxgw/LxgwWenKai)
- Thanks to [PaddleOCRSharp](https://gitee.com/raoyutian/paddle-ocrsharp) for packing `PaddleOCR`
- Thanks to [pot-desktop](https://pot-app.com/) for `Prompt` design ideas
- Thanks to [GTranslate](https://github.com/d4n3436/GTranslate ) Microsoft/Yandex service source code
- Thanks to `ChatGPT`、`Github Copilot`
- Thanks to JetBrains for providing open-source project free License
- Thanks for [Posting Sharing](Sponsor.md#分享支持)
- Thanks to [CopyTranslator](https://github.com/CopyTranslator/CopyTranslator) for the realization of purification function

<a href="https://jb.gg/OpenSourceSupport"><img src="./attachments/imgs/jb_beam.svg" /></a>

## Donations

If you like it, you can buy the author a cup of coffee.

> Thank you to those who rewarded me [Sponsor List](Sponsor.md)

| WeChat | Alipay |
| :--: | :--: |
|![wehcatpay](./attachments/imgs/wechatpay.jpg) | ![alipay](./attachments/imgs/alipay.jpg) |

## Others

**The software is open source and free of charge. If there are special customization needs, I accept paid development [email](<EMAIL>)**

## Author

**STranslate** © [zggsong](https://github.com/zggsong), Released under the [MIT](https://github.com/ZGGSONG/STranslate/blob/main/LICENSE) License.<br>

> Website [Blog](https://www.zggsong.com) · GitHub [@zggsong](https://github.com/zggsong)

## Star History

[![Star History Chart](https://api.star-history.com/svg?repos=ZGGSONG/STranslate&type=Date)](https://star-history.com/#ZGGSONG/STranslate&Date)
