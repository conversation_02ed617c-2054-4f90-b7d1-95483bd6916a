﻿<UserControl
    x:Class="STranslate.Views.Preference.AboutPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="clr-namespace:STranslate.Style.Controls;assembly=STranslate.Style"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:props="clr-namespace:STranslate.Style.Themes;assembly=STranslate.Style"
    xmlns:vm="clr-namespace:STranslate.ViewModels.Preference"
    d:DataContext="{d:DesignInstance Type=vm:AboutViewModel}"
    d:DesignHeight="800"
    d:DesignWidth="800"
    d:FontFamily="{DynamicResource UserFont}"
    d:FontSize="{DynamicResource FontSize18}"
    Style="{StaticResource ResourceKey=Page_Style}"
    mc:Ignorable="d">
    <Border CornerRadius="5">
        <StackPanel Orientation="Vertical">

            <Border
                Margin="8"
                props:ThemeProps.Background="{DynamicResource BorderContentBackground}"
                CornerRadius="5">
                <StackPanel Orientation="Vertical">
                    <Viewbox
                        Height="80"
                        Margin="0,20"
                        HorizontalAlignment="Center">
                        <Path Data="M31.79,28.11l-7-14a2,2,0,0,0-3.58,0L18,20.56,15.3,18.73A17.13,17.13,0,0,0,19.91,9H22a2,2,0,0,0,0-4H14V3a2,2,0,0,0-4,0V5H2A2,2,0,0,0,2,9H15.86a13.09,13.09,0,0,1-3.79,7.28,13.09,13.09,0,0,1-3.19-4.95,2,2,0,1,0-3.77,1.34A17.1,17.1,0,0,0,8.9,18.75L3.84,22.37a2,2,0,0,0,2.33,3.25l5.93-4.24,4.08,2.79-2,3.93a2,2,0,0,0,3.58,1.79l.45-.89H23a2,2,0,0,0,0-4H20.24L23,19.47l5.21,10.42a2,2,0,0,0,3.58-1.79Z" Fill="{DynamicResource ThemeAccentColor}" />
                    </Viewbox>

                    <TextBlock
                        Margin="0,0,0,20"
                        HorizontalAlignment="Center"
                        FontSize="{DynamicResource FontSize30}"
                        FontWeight="Bold"
                        Text="STranslate" />
                </StackPanel>
            </Border>

            <Border
                Margin="8"
                props:ThemeProps.Background="{DynamicResource BorderContentBackground}"
                CornerRadius="5">
                <StackPanel Orientation="Vertical">
                    <DockPanel Margin="20,10">
                        <TextBlock FontWeight="Bold">
                            <Run Text="{DynamicResource About.Version}" />
                            <Run Text="    " />
                        </TextBlock>
                        <TextBlock Text="{Binding Version}" />

                        <Grid HorizontalAlignment="Right">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <ProgressBar
                                FontSize="{DynamicResource FontSize16}"
                                Maximum="100"
                                Visibility="{Binding IsDownloading, Converter={StaticResource BooleanToVisibilityConverter}}"
                                Value="{Binding DownloadProgress}" />
                            <Button
                                Grid.Column="1"
                                Width="100"
                                Padding="5,2"
                                Command="{Binding CheckUpdateCommand}"
                                Content="{DynamicResource About.CheckUpdate}"
                                Visibility="{Binding IsChecking, Converter={StaticResource BooleanToVisibilityReverseConverter}}" />
                            <Button
                                Grid.Column="1"
                                Width="100"
                                Padding="5,2"
                                Command="{Binding CheckUpdateCancelCommand}"
                                Content="{DynamicResource About.CancelCheck}"
                                Visibility="{Binding IsChecking, Converter={StaticResource BooleanToVisibilityConverter}}" />
                        </Grid>
                    </DockPanel>

                    <DockPanel Margin="20,10">
                        <TextBlock FontWeight="Bold">
                            <Run Text="{DynamicResource About.Tools}" />
                            <Run Text="   " />
                        </TextBlock>
                        <controls:BtnContentMore
                            MinWidth="140"
                            Command="{Binding CleanLogCommand}"
                            Content="{DynamicResource About.CleanLog}"
                            ContentMore="{Binding FileSize}" />

                        <Grid HorizontalAlignment="Right">
                            <ToggleButton
                                x:Name="PART_Opendir"
                                Width="100"
                                HorizontalAlignment="Right"
                                Content="{DynamicResource About.OpenDirectory}"
                                Style="{DynamicResource ToggleButtonTextStyle}">
                                <i:Interaction.Triggers>
                                    <i:EventTrigger EventName="Checked">
                                        <i:ChangePropertyAction
                                            PropertyName="IsOpen"
                                            TargetObject="{Binding ElementName=PART_Popup_OpenDir}"
                                            Value="True" />
                                    </i:EventTrigger>
                                </i:Interaction.Triggers>
                            </ToggleButton>
                            <Popup
                                x:Name="PART_Popup_OpenDir"
                                MinWidth="80"
                                AllowsTransparency="True"
                                Placement="Center"
                                PlacementTarget="{Binding ElementName=PART_Opendir}"
                                PopupAnimation="Fade"
                                StaysOpen="False">
                                <i:Interaction.Triggers>
                                    <i:EventTrigger EventName="Closed">
                                        <i:ChangePropertyAction
                                            PropertyName="IsChecked"
                                            TargetObject="{Binding ElementName=PART_Opendir}"
                                            Value="{Binding ElementName=PART_Opendir, Path=IsMouseOver}" />
                                    </i:EventTrigger>
                                </i:Interaction.Triggers>
                                <Border
                                    Width="100"
                                    props:ThemeProps.BorderBrush="{DynamicResource BorderBrushColor}"
                                    BorderThickness="1"
                                    Style="{DynamicResource BorderPopupStyle}">
                                    <StackPanel>
                                        <Button Command="{Binding OpenLogCommand}" Content="{DynamicResource About.LogDirectory}" />
                                        <Separator />
                                        <Button Command="{Binding OpenConfigCommand}" Content="{DynamicResource About.ConfigDirectory}" />
                                    </StackPanel>
                                </Border>
                            </Popup>
                        </Grid>
                    </DockPanel>

                    <WrapPanel Margin="20,10" Orientation="Horizontal">
                        <TextBlock FontWeight="Bold">
                            <Run Text="{DynamicResource About.OpenSource}" />
                            <Run Text="    " />
                        </TextBlock>
                        <TextBlock
                            Cursor="Hand"
                            Text="MIT License"
                            ToolTip="https://github.com/ZGGSONG/STranslate/blob/main/LICENSE">
                            <i:Interaction.Triggers>
                                <i:EventTrigger EventName="MouseLeftButtonDown">
                                    <i:InvokeCommandAction Command="{Binding OpenLinkCommand}" CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=TextBlock}, Path=ToolTip}" />
                                </i:EventTrigger>
                            </i:Interaction.Triggers>
                        </TextBlock>
                    </WrapPanel>

                    <WrapPanel Margin="20,10" Orientation="Horizontal">
                        <TextBlock FontWeight="Bold">
                            <Run Text="{DynamicResource About.Author}" />
                            <Run Text="    " />
                        </TextBlock>
                        <TextBlock Text="<EMAIL>" ToolTip="<EMAIL>" />
                    </WrapPanel>

                    <WrapPanel Margin="20,10" Orientation="Horizontal">
                        <TextBlock FontWeight="Bold">
                            <Run Text="{DynamicResource About.Website}" />
                            <Run Text="    " />
                        </TextBlock>
                        <TextBlock
                            Cursor="Hand"
                            Text="https://stranslate.zggsong.com"
                            ToolTip="https://stranslate.zggsong.com/">
                            <i:Interaction.Triggers>
                                <i:EventTrigger EventName="MouseLeftButtonDown">
                                    <i:InvokeCommandAction Command="{Binding OpenLinkCommand}" CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=TextBlock}, Path=ToolTip}" />
                                </i:EventTrigger>
                            </i:Interaction.Triggers>
                        </TextBlock>
                    </WrapPanel>

                    <WrapPanel Margin="20,10" Orientation="Horizontal">
                        <TextBlock FontWeight="Bold">
                            <Run Text="{DynamicResource About.SourceCode}" />
                            <Run Text="    " />
                        </TextBlock>
                        <TextBlock
                            Cursor="Hand"
                            Text="https://github.com/zggsong/stranslate"
                            ToolTip="https://github.com/zggsong/stranslate">
                            <i:Interaction.Triggers>
                                <i:EventTrigger EventName="MouseLeftButtonDown">
                                    <i:InvokeCommandAction Command="{Binding OpenLinkCommand}" CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=TextBlock}, Path=ToolTip}" />
                                </i:EventTrigger>
                            </i:Interaction.Triggers>
                        </TextBlock>
                    </WrapPanel>

                    <WrapPanel Margin="20,10" Orientation="Horizontal">
                        <TextBlock FontWeight="Bold">
                            <Run Text="{DynamicResource About.Feedback}" />
                            <Run Text="    " />
                        </TextBlock>
                        <TextBlock
                            Cursor="Hand"
                            Text="https://github.com/zggsong/stranslate/issues"
                            ToolTip="https://github.com/zggsong/stranslate/issues">
                            <i:Interaction.Triggers>
                                <i:EventTrigger EventName="MouseLeftButtonDown">
                                    <i:InvokeCommandAction Command="{Binding OpenLinkCommand}" CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=TextBlock}, Path=ToolTip}" />
                                </i:EventTrigger>
                            </i:Interaction.Triggers>
                        </TextBlock>
                    </WrapPanel>

                    <WrapPanel Margin="20,10" Orientation="Horizontal">
                        <TextBlock FontWeight="Bold">
                            <Run Text="{DynamicResource About.Thanks}" />
                            <Run Text="    " />
                        </TextBlock>
                        <TextBlock
                            Cursor="Hand"
                            Text="https://bobtranslate.com/"
                            ToolTip="https://bobtranslate.com/">
                            <i:Interaction.Triggers>
                                <i:EventTrigger EventName="MouseLeftButtonDown">
                                    <i:InvokeCommandAction Command="{Binding OpenLinkCommand}" CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=TextBlock}, Path=ToolTip}" />
                                </i:EventTrigger>
                            </i:Interaction.Triggers>
                        </TextBlock>
                        <TextBlock
                            Margin="10,0,0,0"
                            Cursor="Hand"
                            Text="https://github.com/ripperhe"
                            ToolTip="https://github.com/ripperhe">
                            <i:Interaction.Triggers>
                                <i:EventTrigger EventName="MouseLeftButtonDown">
                                    <i:InvokeCommandAction Command="{Binding OpenLinkCommand}" CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=TextBlock}, Path=ToolTip}" />
                                </i:EventTrigger>
                            </i:Interaction.Triggers>
                        </TextBlock>
                    </WrapPanel>
                </StackPanel>
            </Border>
        </StackPanel>
    </Border>
</UserControl>