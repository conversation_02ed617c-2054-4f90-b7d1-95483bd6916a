<div align="center">

<p>
<a href="https://github.com/zggsong/STranslate" target="_blank">
<img align="center" alt="STranslate" width="200" src="./attachments/imgs/favicon.svg" />
</a>
</p>
<p>
<a href="https://github.com/ZGGSONG/STranslate/blob/main/LICENSE" target="_self">
 <img alt="Latest GitHub release" src="https://img.shields.io/github/license/ZGGSONG/STranslate" />
</a>
<a href="https://github.com/ZGGSONG/STranslate/releases/latest" target="_blank">
 <img alt="Latest GitHub release" src="https://img.shields.io/github/release/ZGGSONG/STranslate.svg" />
</a>
<a href="https://github.com/ZGGSONG/STranslate/releases" target="_self">
 <img alt="Downloads" src="https://img.shields.io/github/downloads/ZGGSONG/STranslate/total" />
</a>
<a href="https://github.com/ZGGSONG/STranslate/discussions" target="_self">
 <img alt="Discussions" src="https://img.shields.io/github/discussions/ZGGSONG/STranslate" />
</a>
</p>

<h1 align="center">STranslate</h1>

[**English**](./README.md) | **简体中文**

<a href="https://trendshift.io/repositories/6979" target="_blank"><img src="https://trendshift.io/api/badge/repositories/6979" alt="ZGGSONG%2FSTranslate | Trendshift" style="width: 250px; height: 55px;" width="250" height="55"/></a>

<p align="center">WPF 开发的一款<strong>即用即走</strong>的翻译、OCR工具</p>

<small>随着功能的更新以及免费服务的限制，建议申请服务并进行相应的配置以获得更好的使用体验</small>

</div>

<br/>

## 访问

| 国外 | 国内 |
| :--: | :--: |
| **[Github](https://github.com/ZGGSONG/STranslate)** | **[Gitee](https://gitee.com/zggsong/STranslate)** |


## 安装

下载最新 [Release](https://github.com/ZGGSONG/STranslate/releases) 版本后解压即可使用

## 使用

[Document](https://stranslate.zggsong.com)

## 讨论

有疑问移步 [Discussions](https://github.com/ZGGSONG/STranslate/discussions) 进行讨论

## 合作推广

🛠️ **官方API合作伙伴**  

[DeerAPI](https://api.deerapi.com/register?aff=j5dj) - AI聚合平台，一键调用500+模型，7折特惠，最新GPT4o、Grok 3、Gemini 2.5pro全支持！

[点击注册](https://api.deerapi.com/register?aff=j5dj)享免费试用额度，也能支持软件长久发展

## 感谢

- 特别鸣谢 [zu1k](https://github.com/zu1k)
- 感谢 [Bob](https://bobtranslate.com/guide/) 的启发
- 感谢 [LxgwWenKai](https://github.com/lxgw/LxgwWenKai)
- 感谢 [PaddleOCRSharp](https://gitee.com/raoyutian/paddle-ocrsharp) 对 `PaddleOCR` 的封装
- 感谢 [pot-desktop](https://pot-app.com/) `Prompt` 设计思路
- 感谢 [GTranslate](https://github.com/d4n3436/GTranslate) Microsoft/Yandex 服务源码
- 感谢 `ChatGPT`、`Github Copilot`
- 感谢 JetBrains 提供开源项目免费License
- 感谢 [发文分享](Sponsor.md#分享支持)
- 感谢 [CopyTranslator](https://github.com/CopyTranslator/CopyTranslator) 净化文本功能

<a href="https://jb.gg/OpenSourceSupport"><img src="./attachments/imgs/jb_beam.svg" /></a>

## 打赏

觉得不错的话可以请作者喝杯阔落

> 感谢打赏的朋友 [赞赏列表](Sponsor.md)

| 微信 | 支付宝 |
| :--: | :--: |
|![wehcatpay](./attachments/imgs/wechatpay.jpg) | ![alipay](./attachments/imgs/alipay.jpg) |

## 其他

**软件开源且免费，如有特殊定制化需求，本人接受付费开发 [邮件](<EMAIL>)**

## 作者

**STranslate** © [zggsong](https://github.com/zggsong), Released under the [MIT](https://github.com/ZGGSONG/STranslate/blob/main/LICENSE) License.<br>

> Website [Blog](https://www.zggsong.com) · GitHub [@zggsong](https://github.com/zggsong)

## 星标历史

[![Star History Chart](https://api.star-history.com/svg?repos=ZGGSONG/STranslate&type=Date)](https://star-history.com/#ZGGSONG/STranslate&Date)
