using System.Diagnostics;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using STranslate.Model;

namespace STranslate.Views.Preference.AIAssistant;

public partial class AIAssistantOpenAIPage : UserControl
{
    public AIAssistantOpenAIPage(ITranslator vm)
    {
        InitializeComponent();

        DataContext = vm;
        
        // 设置密码框的初始值
        if (vm is ViewModels.Preference.AIAssistant.AIAssistantOpenAI openAI && openAI.KeyHide)
        {
            AppKeyPasswordBox.Password = openAI.AppKey;
        }
    }

    /// <summary>
    ///     通过缓存加载View时刷新ViewModel
    /// </summary>
    /// <param name="vm"></param>
    public void UpdateVM(ITranslator vm)
    {
        DataContext = vm;
        
        // 更新密码框的值
        if (vm is ViewModels.Preference.AIAssistant.AIAssistantOpenAI openAI && openAI.KeyHide)
        {
            AppKeyPasswordBox.Password = openAI.AppKey;
        }
    }

    private void AppKeyPasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
    {
        if (DataContext is ViewModels.Preference.AIAssistant.AIAssistantOpenAI vm)
        {
            vm.AppKey = AppKeyPasswordBox.Password;
        }
    }
}
