using CommunityToolkit.Mvvm.Input;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using STranslate.Helper;
using STranslate.Log;
using STranslate.Model;
using STranslate.Util;
using System.ComponentModel;
using System.Net.Http;
using System.Text;

namespace STranslate.ViewModels.Preference.AIAssistant;

public partial class AIAssistantClaude : AIAssistantBase
{
    #region Constructor

    public AIAssistantClaude()
        : this(Guid.NewGuid(), "https://api.anthropic.com", "Claude助手")
    {
    }

    public AIAssistantClaude(
        Guid guid,
        string url,
        string name = "",
        IconType icon = IconType.ClaudeAssistant,
        string appID = "",
        string appKey = "",
        bool isEnabled = true,
        ServiceType type = ServiceType.ClaudeAssistant
    )
    {
        Identify = guid;
        Url = url;
        Name = name;
        Icon = icon;
        AppID = appID;
        AppKey = appKey;
        IsEnabled = isEnabled;
        Type = type;
        
        // 设置默认模型
        Model = "claude-3-5-sonnet-20241022";
        Models = [
            "claude-3-5-sonnet-20241022",
            "claude-3-5-haiku-20241022",
            "claude-3-opus-20240229",
            "claude-3-sonnet-20240229",
            "claude-3-haiku-20240307"
        ];
    }

    #endregion Constructor

    #region Properties

    [JsonIgnore] public bool KeyHide { get; set; } = true;

    #endregion Properties

    #region Test Methods

    [RelayCommand]
    [property: JsonIgnore]
    private async Task TestAsync(CancellationToken token)
    {
        var chatRequest = new ChatRequest("Hello, please respond with 'Test successful!' to confirm the connection.");
        var result = string.Empty;
        
        try
        {
            await ChatAsync(chatRequest, chunk => result += chunk, token);
            ToastHelper.Show("测试成功", WindowType.Preference);
        }
        catch (Exception ex)
        {
            ToastHelper.Show($"测试失败: {ex.Message}", WindowType.Preference);
            LogService.Logger.Error($"[{Name}] 测试失败: {ex.Message}");
        }
    }

    #endregion Test Methods

    #region Interface Implementation

    public override async Task ChatAsync(ChatRequest request, Action<string> onDataReceived, CancellationToken token)
    {
        if (string.IsNullOrEmpty(Url) || string.IsNullOrEmpty(AppKey))
            throw new Exception("请先完善配置");

        UriBuilder uriBuilder = new(Url);
        if (uriBuilder.Path == "/")
            uriBuilder.Path = "/v1/messages";

        // 选择模型
        var selectedModel = string.IsNullOrEmpty(Model) ? "claude-3-5-sonnet-20241022" : Model.Trim();

        // 构建消息列表（Claude不支持system role在messages中，需要单独处理）
        var messages = new List<object>();
        
        // 添加历史记录
        if (KeepContext && ChatHistory.Count > 0)
        {
            foreach (var msg in ChatHistory.TakeLast(MaxHistoryLength))
            {
                messages.Add(new { role = msg.Role, content = msg.Content });
            }
        }

        // 添加当前用户消息
        messages.Add(new { role = "user", content = request.Message });

        // 构建请求数据
        var reqData = new
        {
            model = selectedModel,
            max_tokens = 4096,
            system = GetActiveSystemPrompt(), // Claude的系统提示词单独设置
            messages = messages,
            temperature = Math.Clamp(Temperature, 0, 1), // Claude的temperature范围是0-1
            stream = true
        };

        var jsonData = JsonConvert.SerializeObject(reqData);
        var assistantResponse = string.Empty;

        try
        {
            var headers = new Dictionary<string, string>
            {
                { "Authorization", $"Bearer {AppKey}" },
                { "anthropic-version", "2023-06-01" },
                { "anthropic-beta", "messages-2023-12-15" }
            };

            await HttpUtil.PostAsync(
                uriBuilder.Uri,
                headers,
                jsonData,
                msg =>
                {
                    if (string.IsNullOrEmpty(msg?.Trim()))
                        return;

                    var preprocessString = msg.Replace("data:", "").Trim();

                    // 结束标记
                    if (preprocessString.Equals("[DONE]"))
                        return;

                    try
                    {
                        // 解析JSON数据
                        var parsedData = JsonConvert.DeserializeObject<JObject>(preprocessString);
                        if (parsedData == null) return;

                        // Claude的流式响应格式
                        var eventType = parsedData["type"]?.ToString();
                        if (eventType == "content_block_delta")
                        {
                            var contentValue = parsedData["delta"]?["text"]?.ToString();
                            if (!string.IsNullOrEmpty(contentValue))
                            {
                                assistantResponse += contentValue;
                                onDataReceived?.Invoke(contentValue);
                            }
                        }
                    }
                    catch
                    {
                        // 忽略解析错误
                    }
                },
                token
            ).ConfigureAwait(false);

            // 对话完成后，添加到历史记录
            if (KeepContext && !string.IsNullOrWhiteSpace(assistantResponse))
            {
                AddToHistory(ChatMessage.User(request.Message));
                AddToHistory(ChatMessage.Assistant(assistantResponse));
            }
        }
        catch (OperationCanceledException)
        {
            throw;
        }
        catch (HttpRequestException ex) when (ex.StatusCode == null)
        {
            var msg = $"请检查服务是否可以正常访问: {Name} ({Url}).\n{ex.Message}";
            throw new HttpRequestException(msg);
        }
        catch (HttpRequestException)
        {
            throw;
        }
        catch (Exception ex)
        {
            var msg = ex.Message;
            if (ex.InnerException is HttpRequestException httpEx)
            {
                if (httpEx.Data.Contains("StatusCode"))
                {
                    var statusCode = httpEx.Data["StatusCode"]!.ToString();
                    msg = $"请求失败，状态码: {statusCode}";
                }
            }
            throw new Exception(msg);
        }
    }

    public override ITranslator Clone()
    {
        return new AIAssistantClaude
        {
            Identify = Identify,
            Type = Type,
            IsEnabled = IsEnabled,
            Icon = Icon,
            Name = Name,
            Url = Url,
            Data = TranslationResult.Reset,
            AppID = AppID,
            AppKey = AppKey,
            AutoExecute = AutoExecute,
            AutoExecuteTranslateBack = AutoExecuteTranslateBack,
            IsExecuting = IsExecuting,
            IsTranslateBackExecuting = IsTranslateBackExecuting,
            Temperature = Temperature,
            Model = Model,
            Models = new BindingList<string>(Models.ToList()),
            UserDefinePrompts = new BindingList<UserDefinePrompt>(UserDefinePrompts.Select(x => x.Clone()).Cast<UserDefinePrompt>().ToList()),
            SystemPrompt = SystemPrompt,
            ChatHistory = new BindingList<ChatMessage>(),
            MaxHistoryLength = MaxHistoryLength,
            KeepContext = KeepContext,
            KeyHide = KeyHide
        };
    }

    #endregion Interface Implementation
}
