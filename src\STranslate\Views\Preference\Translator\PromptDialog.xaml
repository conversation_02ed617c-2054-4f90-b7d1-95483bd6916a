﻿<Window
    x:Class="STranslate.Views.Preference.Translator.PromptDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:common="clr-namespace:STranslate.Style.Commons;assembly=STranslate.Style"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dd="urn:gong-wpf-dragdrop"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:props="clr-namespace:STranslate.Style.Themes;assembly=STranslate.Style"
    xmlns:vm="clr-namespace:STranslate.ViewModels.Preference.Translator"
    x:Name="promptDialog"
    Width="400"
    MaxHeight="600"
    d:DataContext="{d:DesignInstance Type=vm:PromptViewModel}"
    props:ThemeProps.Background="{DynamicResource NavigationBackground}"
    props:ThemeProps.BorderBrush="{DynamicResource BorderBrushColor}"
    BorderThickness="1"
    FontFamily="{DynamicResource UserFont}"
    Icon="{DynamicResource Icon}"
    Loaded="promptDialog_Loaded"
    SizeToContent="Height"
    Topmost="True"
    WindowStartupLocation="CenterScreen"
    mc:Ignorable="d">
    <Window.InputBindings>
        <KeyBinding
            Key="S"
            Command="{Binding SaveCommand}"
            CommandParameter="{Binding ElementName=promptDialog}"
            Modifiers="Ctrl" />
        <KeyBinding
            Key="Escape"
            Command="{Binding CancelCommand}"
            CommandParameter="{Binding ElementName=promptDialog}" />
    </Window.InputBindings>
    <WindowChrome.WindowChrome>
        <WindowChrome />
    </WindowChrome.WindowChrome>
    <Grid Margin="5">
        <Grid.RowDefinitions>
            <RowDefinition Height="35" />
            <RowDefinition Height="*" />
            <RowDefinition Height="40" />
        </Grid.RowDefinitions>
        <Grid
            props:ThemeProps.Background="{DynamicResource NavigationBackground}"
            MouseDown="Header_MouseDown"
            WindowChrome.IsHitTestVisibleInChrome="True">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="50" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="36" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="28" />
                <ColumnDefinition Width="28" />
                <ColumnDefinition Width="36" />
                <ColumnDefinition Width="36" />
            </Grid.ColumnDefinitions>
            <Image
                Grid.Column="0"
                Width="24"
                Margin="10,3"
                HorizontalAlignment="Left"
                VerticalAlignment="Top"
                Source="{DynamicResource STranslate}" />
            <TextBlock
                x:Name="TitleTxt"
                Grid.Column="1"
                props:ThemeProps.Foreground="{DynamicResource NavigationForeground}"
                FontSize="{DynamicResource FontSize20}">
                <Run Text="{DynamicResource Prompt.Edit}" />
                <Run Text=": " />
                <Run Text="{Binding UserDefinePrompt.Name}" />
            </TextBlock>
        </Grid>

        <ScrollViewer Grid.Row="1" FontSize="{DynamicResource FontSize18}">
            <StackPanel>
                <common:PlaceholderTextBox
                    x:Name="TbName"
                    MinWidth="200"
                    Margin="5,0"
                    Placeholder="Input your prompt name"
                    Text="{Binding UserDefinePrompt.Name, UpdateSourceTrigger=PropertyChanged}" />
                <ListBox
                    x:Name="listbox"
                    dd:DragDrop.IsDragSource="True"
                    dd:DragDrop.IsDropTarget="True"
                    dd:DragDrop.UseDefaultDragAdorner="True"
                    Background="Transparent"
                    BorderThickness="0"
                    ItemsSource="{Binding UserDefinePrompt.Prompts}"
                    PreviewMouseWheel="ListBox_PreviewMouseWheel"
                    ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <Border Style="{DynamicResource BorderInOutputStyle}">
                                <StackPanel>
                                    <Grid HorizontalAlignment="Stretch">
                                        <TextBlock
                                            Margin="5,2,0,0"
                                            VerticalAlignment="Top"
                                            FontWeight="Bold"
                                            Text="{Binding Role}" />
                                        <Button
                                            HorizontalAlignment="Right"
                                            Command="{Binding RelativeSource={RelativeSource AncestorType=ListBox}, Path=DataContext.DelCommand}"
                                            CommandParameter="{Binding .}"
                                            Content="&#xe74b;"
                                            Style="{DynamicResource ButtonIconStyle}"
                                            ToolTip="{DynamicResource Prompt.Delete}" />
                                    </Grid>
                                    <common:PlaceholderTextBox
                                        MinWidth="200"
                                        MinHeight="60"
                                        Margin="5,0"
                                        AcceptsReturn="True"
                                        Placeholder="Input your prompt..."
                                        Text="{Binding Content, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                        TextWrapping="Wrap" />
                                </StackPanel>
                            </Border>
                        </DataTemplate>
                    </ListBox.ItemTemplate>
                </ListBox>
                <Button
                    x:Name="Add"
                    Command="{Binding AddCommand}"
                    Content="{DynamicResource Prompt.Add}"
                    FontWeight="Bold" />
            </StackPanel>
        </ScrollViewer>

        <WrapPanel Grid.Row="2" HorizontalAlignment="Right">
            <Button
                Command="{Binding SaveCommand}"
                CommandParameter="{Binding ElementName=promptDialog}"
                Content="{DynamicResource Preference.Save}"
                IsDefault="True" />
            <Button
                Command="{Binding CancelCommand}"
                CommandParameter="{Binding ElementName=promptDialog}"
                Content="{DynamicResource Cancel}" />
        </WrapPanel>
    </Grid>
</Window>