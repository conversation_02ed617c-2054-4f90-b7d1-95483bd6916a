﻿<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:model="clr-namespace:STranslate.Model;assembly=STranslate.Model">

    <ItemsPanelTemplate x:Key="PromptTemplate">
        <WrapPanel Orientation="Horizontal" />
    </ItemsPanelTemplate>
    <DataTemplate DataType="{x:Type model:UserDefinePrompt}">
        <Border Style="{DynamicResource BorderPromptStyle}">
            <StackPanel Orientation="Horizontal">
                <StackPanel.Resources>
                    <Style BasedOn="{StaticResource {x:Type ToolTip}}" TargetType="ToolTip">
                        <Setter Property="MaxWidth" Value="500" />
                        <Setter Property="MaxHeight" Value="400" />
                        <Setter Property="ContentTemplate">
                            <Setter.Value>
                                <DataTemplate>
                                    <TextBlock Text="{TemplateBinding Content}" />
                                </DataTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                </StackPanel.Resources>
                <Button
                    Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.SelectedPromptCommand}"
                    Content="{Binding Name}"
                    Style="{DynamicResource PromptLeftBtnStyle}"
                    ToolTip="{Binding ., Converter={StaticResource JsonSerializeConverter}}">
                    <Button.CommandParameter>
                        <MultiBinding Converter="{StaticResource MultiValue2ListConverter}">
                            <Binding Path="." />
                        </MultiBinding>
                    </Button.CommandParameter>
                </Button>
                <Separator Style="{DynamicResource VerticalSeparatorStyle}" />
                <Button
                    Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.UpdatePromptCommand}"
                    CommandParameter="{Binding Path=.}"
                    Content="&#xe61b;"
                    FontFamily="{DynamicResource IconFont}"
                    FontSize="{DynamicResource FontSize14}"
                    Style="{DynamicResource PromptBtnStyle}"
                    ToolTip="{DynamicResource Service.Update}" />
                <Separator Style="{DynamicResource VerticalSeparatorStyle}" />
                <Button
                    Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.DuplicatePromptCommand}"
                    CommandParameter="{Binding Path=.}"
                    Content="&#xe652;"
                    FontFamily="{DynamicResource IconFont}"
                    FontSize="{DynamicResource FontSize14}"
                    Style="{DynamicResource PromptBtnStyle}"
                    ToolTip="{DynamicResource Service.Duplicate}" />
                <Separator Style="{DynamicResource VerticalSeparatorStyle}" />
                <Button
                    Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.DeletePromptCommand}"
                    CommandParameter="{Binding Path=.}"
                    Content="&#xe74b;"
                    FontFamily="{DynamicResource IconFont}"
                    FontSize="{DynamicResource FontSize16}"
                    Style="{DynamicResource PromptRightBtnStyle}"
                    ToolTip="{DynamicResource Service.Delete}" />
            </StackPanel>
        </Border>
    </DataTemplate>
</ResourceDictionary>