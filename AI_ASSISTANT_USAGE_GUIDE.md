# STranslate AI Assistant 使用指南

## 🎯 概述
STranslate 已成功改造为功能强大的AI助手，保留了原有的便利特性，同时增加了AI对话功能。

## 📁 文件位置
- **更新版本**：`output-updated/STranslate.exe` (48.9MB)
- **独立版本**：`output-standalone/STranslate.exe` (200.1MB)

## 🚀 如何配置AI助手

### 1. 启动应用程序
双击 `STranslate.exe` 启动应用程序

### 2. 打开设置界面
- 点击主界面右上角的 **设置图标** (⚙️)
- 或者使用快捷键打开设置

### 3. 进入翻译器配置
- 在设置界面左侧导航栏中，点击 **"翻译器"** 选项
- 这里会显示所有可用的翻译服务和AI助手服务

### 4. 添加AI助手服务
在翻译器页面底部，点击 **"添加服务"** 按钮，选择以下AI助手之一：

#### 🤖 OpenAI助手
- **服务类型**：OpenAI Assistant
- **支持模型**：GPT-4o, GPT-4, GPT-3.5-turbo, o1-preview, o1-mini
- **配置项**：
  - 名称：自定义名称（如"我的OpenAI助手"）
  - URL：`https://api.openai.com`
  - API密钥：你的OpenAI API密钥（sk-...）
  - 模型：选择要使用的模型
  - 温度：0-2之间，控制回复的创造性
  - 系统提示词：定义AI助手的行为和角色

#### 🧠 Claude助手
- **服务类型**：Claude Assistant  
- **支持模型**：Claude-3.5-Sonnet, Claude-3-Opus, Claude-3-Haiku
- **配置项**：
  - 名称：自定义名称（如"我的Claude助手"）
  - URL：`https://api.anthropic.com`
  - API密钥：你的Anthropic API密钥（sk-ant-...）
  - 模型：选择要使用的Claude模型
  - 温度：0-1之间，控制回复的创造性
  - 系统提示词：定义AI助手的行为和角色

#### 🏠 Ollama助手（本地AI）
- **服务类型**：Ollama Assistant
- **支持模型**：llama3.2, qwen2.5, deepseek-r1, gemma2等本地模型
- **配置项**：
  - 名称：自定义名称（如"本地AI助手"）
  - URL：`http://localhost:11434`（默认Ollama地址）
  - 模型：选择已安装的本地模型
  - 温度：0-2之间，控制回复的创造性
  - 系统提示词：定义AI助手的行为和角色
- **优势**：无需API密钥，完全本地运行，数据隐私

### 5. 配置AI助手参数

#### 基本配置
- **名称**：给你的AI助手起一个容易识别的名称
- **URL**：API服务地址（通常使用默认值）
- **API密钥**：从对应服务商获取的密钥

#### 高级配置
- **模型选择**：不同模型有不同的能力和成本
- **温度设置**：
  - 0.0：最确定性的回复
  - 1.0：平衡创造性和准确性
  - 2.0：最有创造性的回复
- **系统提示词**：定制AI助手的行为，例如：
  - 通用助手：`You are a helpful AI assistant.`
  - 编程助手：`You are an expert programming assistant.`
  - 写作助手：`You are a professional writing assistant.`

#### 对话设置
- **保持上下文**：开启后AI会记住之前的对话
- **历史长度**：保留多少轮对话历史（建议10-20）

### 6. 测试连接
配置完成后，点击 **"测试"** 按钮验证配置是否正确

### 7. 保存配置
点击 **"保存"** 按钮保存所有配置

## 💬 如何使用AI助手

### 基本对话
1. 在主界面输入框中输入你的问题或请求
2. 按 **Enter** 发送消息
3. AI助手会实时显示回复内容
4. 支持多轮对话，AI会记住上下文

### 快捷操作
- **Enter**：发送消息
- **Ctrl+Enter**：强制发送
- **Shift+Enter**：换行
- **快捷键呼出**：使用全局快捷键快速呼出助手

### 预设模板
在AI助手配置页面可以选择不同的对话模板：
- **通用助手**：日常问答和帮助
- **编程助手**：代码编写和调试
- **写作助手**：文本创作和修改

### 管理对话历史
- **清空历史**：在配置页面点击"清空对话历史"
- **上下文控制**：通过"保持上下文"开关控制是否记住对话

## 🔧 高级功能

### 多AI助手配置
- 可以同时配置多个不同的AI助手
- 在主界面可以快速切换使用不同的助手
- 每个助手可以有不同的专业领域和配置

### OCR + AI助手
- 使用截图OCR识别图片中的文字
- 识别结果可以直接发送给AI助手进行分析

### 托盘常驻
- AI助手会在系统托盘常驻
- 使用快捷键可以随时呼出
- 真正的"即用即走"体验

## 🛠️ 故障排除

### 常见问题
1. **API密钥错误**：检查密钥是否正确，是否有足够的配额
2. **网络连接问题**：检查网络连接，考虑使用代理
3. **模型不可用**：确认选择的模型在你的账户中可用
4. **Ollama连接失败**：确保Ollama服务正在运行

### 代理设置
如果需要使用代理访问AI服务：
1. 在设置中配置代理服务器
2. 支持HTTP/HTTPS代理
3. 支持系统代理设置

## 🎉 享受AI助手体验

现在你可以享受强大的AI助手功能了！
- 💡 **智能问答**：回答各种问题
- 💻 **编程帮助**：代码编写和调试
- ✍️ **写作辅助**：文本创作和修改
- 🔍 **信息查询**：快速获取信息
- 🤖 **多模型支持**：选择最适合的AI模型

记住：这个AI助手保留了原有STranslate的所有便利特性，同时提供了现代化的AI对话体验！
