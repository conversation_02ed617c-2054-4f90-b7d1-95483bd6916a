﻿<Window
    x:Class="STranslate.Views.OCRView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:common="clr-namespace:STranslate.Style.Commons;assembly=STranslate.Style"
    xmlns:control="clr-namespace:STranslate.Style.Controls;assembly=STranslate.Style"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:model="clr-namespace:STranslate.Model;assembly=STranslate.Model"
    xmlns:props="clr-namespace:STranslate.Style.Themes;assembly=STranslate.Style"
    xmlns:view="clr-namespace:STranslate.Views"
    xmlns:vm="clr-namespace:STranslate.ViewModels"
    Title="STranslate OCR"
    Width="{Binding OcrViewWidth, Mode=TwoWay}"
    Height="{Binding OcrViewHeight, Mode=TwoWay}"
    MinWidth="700"
    MinHeight="300"
    d:DataContext="{d:DesignInstance Type=vm:OCRViewModel}"
    d:Height="400"
    d:Width="1000"
    props:ThemeProps.Background="{DynamicResource BorderBackground}"
    props:ThemeProps.BorderBrush="{DynamicResource BorderBrushColor}"
    BorderThickness="1"
    FontFamily="{DynamicResource UserFont}"
    Icon="{DynamicResource Icon}"
    Loaded="Window_Loaded"
    mc:Ignorable="d">
    <WindowChrome.WindowChrome>
        <WindowChrome ResizeBorderThickness="5" />
    </WindowChrome.WindowChrome>
    <Window.InputBindings>
        <KeyBinding
            Key="Esc"
            Command="{Binding CloseCommand}"
            CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}" />
        <KeyBinding
            Key="OemComma"
            Command="{Binding OCRPreferenceCommand}"
            Modifiers="Ctrl" />
        <KeyBinding
            Key="R"
            Command="{Binding RecertificationCommand}"
            Modifiers="Ctrl" />
        <KeyBinding
            Key="T"
            Command="{Binding StickyCommand}"
            CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
            Modifiers="Ctrl+Shift" />
        <!--  // 快捷键复制 //  -->
        <KeyBinding Command="{Binding HotkeyCopyCommand}" Gesture="Ctrl+1" />
        <!--  // 重置图片大小及位置 //  -->
        <KeyBinding
            Command="{Binding ResetImgCommand}"
            CommandParameter="{Binding ElementName=ImgCtl}"
            Gesture="Ctrl+0" />
        <!--  // 重置字体大小 //  -->
        <KeyBinding Command="{Binding ResetFontSizeCommand}" Gesture="Ctrl+OemTilde" />
    </Window.InputBindings>
    <i:Interaction.Triggers>
        <i:EventTrigger EventName="StateChanged">
            <i:InvokeCommandAction Command="{Binding WindowStateChangeCommand}" CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}" />
        </i:EventTrigger>
    </i:Interaction.Triggers>
    <Window.Resources>
        <common:BindingProxy x:Key="TBProxy" Data="{x:Reference TB}" />
        <ContextMenu x:Key="TBCM" Width="120">
            <MenuItem
                Command="{Binding TBSelectAllCommand}"
                CommandParameter="{Binding Source={StaticResource TBProxy}, Path=Data, Mode=OneWay}"
                Header="{DynamicResource Input.SelectAll}"
                Icon="&#xe658;" />
            <Separator Margin="0,2" Style="{DynamicResource SeparatorStyle}" />
            <MenuItem
                Command="{Binding TBCopyCommand}"
                CommandParameter="{Binding Source={StaticResource TBProxy}, Path=Data, Mode=OneWay}"
                Header="{DynamicResource Input.Copy}"
                Icon="&#xe692;" />
            <MenuItem
                Command="{Binding TBPasteCommand}"
                CommandParameter="{Binding Source={StaticResource TBProxy}, Path=Data, Mode=OneWay}"
                Header="{DynamicResource Input.Paste}"
                Icon="&#xe652;" />
            <Separator Margin="0,2" Style="{DynamicResource SeparatorStyle}" />
            <MenuItem
                Command="{Binding TBClearCommand}"
                CommandParameter="{Binding Source={StaticResource TBProxy}, Path=Data, Mode=OneWay}"
                Header="{DynamicResource Input.Clear}"
                Icon="&#xe6cb;" />
        </ContextMenu>
        <common:BindingProxy x:Key="QrTBProxy" Data="{x:Reference QrTB}" />
        <ContextMenu x:Key="QrTBCM" Width="120">
            <MenuItem
                Command="{Binding TBSelectAllCommand}"
                CommandParameter="{Binding Source={StaticResource QrTBProxy}, Path=Data, Mode=OneWay}"
                Header="{DynamicResource Input.SelectAll}"
                Icon="&#xe658;" />
            <Separator Margin="0,2" Style="{DynamicResource SeparatorStyle}" />
            <MenuItem
                Command="{Binding TBCopyCommand}"
                CommandParameter="{Binding Source={StaticResource QrTBProxy}, Path=Data, Mode=OneWay}"
                Header="{DynamicResource Input.Copy}"
                Icon="&#xe692;" />
            <MenuItem
                Command="{Binding TBPasteCommand}"
                CommandParameter="{Binding Source={StaticResource QrTBProxy}, Path=Data, Mode=OneWay}"
                Header="{DynamicResource Input.Paste}"
                Icon="&#xe652;" />
            <Separator Margin="0,2" Style="{DynamicResource SeparatorStyle}" />
            <MenuItem
                Command="{Binding TBClearCommand}"
                CommandParameter="{Binding Source={StaticResource QrTBProxy}, Path=Data, Mode=OneWay}"
                Header="{DynamicResource Input.Clear}"
                Icon="&#xe6cb;" />
        </ContextMenu>
    </Window.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="50" />
            <RowDefinition />
            <RowDefinition Height="40" />
        </Grid.RowDefinitions>

        <!--  // Header //  -->
        <Border
            Name="Header"
            props:ThemeProps.Background="{DynamicResource BorderBackground}"
            CornerRadius="5"
            MouseDown="Header_MouseDown"
            MouseLeftButtonDown="Header_MouseLeftButtonDown"
            WindowChrome.IsHitTestVisibleInChrome="True">

            <Grid>
                <!--  // Topmost //  -->
                <Button
                    Margin="10,5,0,0"
                    HorizontalAlignment="Left"
                    Command="{Binding StickyCommand}"
                    CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                    Content="{Binding TopMostContent}"
                    FontSize="{DynamicResource FontSize20}"
                    Style="{DynamicResource ButtonStickyIconStyle}"
                    Tag="{Binding IsTopMost}" />

                <!--  // Title //  -->
                <StackPanel
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Orientation="Horizontal"
                    Visibility="Collapsed">
                    <Image Height="30" Source="{DynamicResource STranslate}" />
                    <TextBlock
                        Margin="20,0,0,0"
                        VerticalAlignment="Center"
                        props:ThemeProps.Foreground="{DynamicResource NavigationForeground}"
                        FontSize="{DynamicResource FontSize24}"
                        FontWeight="Bold"
                        Text="STranslate OCR" />
                </StackPanel>

                <!--  // Button //  -->
                <StackPanel
                    Margin="15,0"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Center"
                    Orientation="Horizontal">
                    <Button
                        Command="{Binding MinimizeCommand}"
                        CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                        Content="&#xe676;"
                        FontSize="{DynamicResource FontSize20}"
                        Style="{DynamicResource ButtonIconStyle}" />
                    <Button
                        Command="{Binding MaximizeCommand}"
                        CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                        Content="{Binding MaximizeContent}"
                        FontWeight="Bold"
                        Style="{DynamicResource ButtonIconStyle}" />
                    <Button
                        Command="{Binding CloseCommand}"
                        CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                        Content="&#xe64d;"
                        FontSize="{DynamicResource FontSize16}"
                        FontWeight="Bold"
                        Style="{DynamicResource ButtonCloseStyle}" />
                </StackPanel>
            </Grid>
        </Border>

        <!--  // Content //  -->
        <Grid Grid.Row="1" AllowDrop="True">
            <i:Interaction.Triggers>
                <i:EventTrigger EventName="DragEnter">
                    <i:ChangePropertyAction
                        PropertyName="Visibility"
                        TargetObject="{Binding ElementName=DropFilePanel}"
                        Value="Visible" />
                </i:EventTrigger>
                <i:EventTrigger EventName="DragLeave">
                    <i:ChangePropertyAction
                        PropertyName="Visibility"
                        TargetObject="{Binding ElementName=DropFilePanel}"
                        Value="Hidden" />
                </i:EventTrigger>
                <i:EventTrigger EventName="Drop">
                    <i:ChangePropertyAction
                        PropertyName="Visibility"
                        TargetObject="{Binding ElementName=DropFilePanel}"
                        Value="Hidden" />
                    <i:InvokeCommandAction Command="{Binding DropCommand}" PassEventArgsToCommand="True" />
                </i:EventTrigger>
            </i:Interaction.Triggers>
            <Grid.ColumnDefinitions>
                <ColumnDefinition />
                <ColumnDefinition />
            </Grid.ColumnDefinitions>

            <!--  // img //  -->
            <Border
                Margin="10"
                props:ThemeProps.Background="{DynamicResource BorderContentBackground}"
                CornerRadius="5">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" />
                        <RowDefinition Height="auto" />
                    </Grid.RowDefinitions>
                    <ScrollViewer
                        Margin="5"
                        Focusable="False"
                        HorizontalScrollBarVisibility="Disabled"
                        VerticalScrollBarVisibility="Disabled">
                        <ContentControl
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Cursor="SizeAll">
                            <i:Interaction.Triggers>
                                <i:EventTrigger EventName="MouseLeftButtonDown">
                                    <i:CallMethodAction MethodName="MouseDown" TargetObject="{Binding}" />
                                </i:EventTrigger>
                                <i:EventTrigger EventName="MouseLeftButtonUp">
                                    <i:CallMethodAction MethodName="MouseUp" TargetObject="{Binding}" />
                                </i:EventTrigger>
                                <i:EventTrigger EventName="MouseMove">
                                    <i:CallMethodAction MethodName="MouseMove" TargetObject="{Binding}" />
                                </i:EventTrigger>
                                <i:EventTrigger EventName="MouseWheel">
                                    <i:CallMethodAction MethodName="MouseWheel" TargetObject="{Binding}" />
                                </i:EventTrigger>
                            </i:Interaction.Triggers>
                            <Image
                                Name="ImgCtl"
                                RenderOptions.BitmapScalingMode="NearestNeighbor"
                                Source="{Binding GetImg}">
                                <Image.RenderTransform>
                                    <TransformGroup>
                                        <ScaleTransform ScaleX="1" ScaleY="1" />
                                        <TranslateTransform X="0" Y="0" />
                                    </TransformGroup>
                                </Image.RenderTransform>
                            </Image>
                        </ContentControl>
                    </ScrollViewer>
                    <Grid Grid.Row="1" Margin="3,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <StackPanel
                            HorizontalAlignment="Left"
                            VerticalAlignment="Bottom"
                            Orientation="Horizontal">
                            <Button
                                Command="{Binding CopyImgCommand}"
                                Content="&#xe692;"
                                Style="{DynamicResource ButtonCopyIconStyle}"
                                ToolTip="{DynamicResource OCR.CopyImage}"
                                Visibility="{Binding Bs, Converter={StaticResource VisibilityConverter}}" />
                            <Button
                                Command="{Binding SaveImgCommand}"
                                Content="&#xe63b;"
                                Style="{DynamicResource ButtonCopyIconStyle}"
                                ToolTip="{DynamicResource OCR.SaveImage}"
                                Visibility="{Binding Bs, Converter={StaticResource VisibilityConverter}}" />
                        </StackPanel>

                        <StackPanel
                            Grid.Column="1"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Bottom"
                            FlowDirection="RightToLeft"
                            Orientation="Horizontal">
                            <TextBlock
                                Name="RateTB"
                                Margin="5"
                                HorizontalAlignment="Right"
                                VerticalAlignment="Top"
                                FlowDirection="LeftToRight"
                                Text="{Binding ImgScale, StringFormat={}{0:P0}}" />
                            <Button
                                Command="{Binding ResetImgCommand}"
                                CommandParameter="{Binding ElementName=ImgCtl}"
                                Content="&#xe64f;"
                                FontSize="{DynamicResource FontSize21}"
                                Style="{DynamicResource ButtonCopyIconStyle}"
                                ToolTip="{DynamicResource OCR.FitWindow}"
                                Visibility="{Binding Bs, Converter={StaticResource VisibilityConverter}}" />
                            <Button
                                Command="{Binding SwitchImgCommand}"
                                CommandParameter="{Binding ElementName=ImgCtl}"
                                Content="&#xe621;"
                                Style="{DynamicResource ButtonCopyIconStyle}"
                                ToolTip="{DynamicResource OCR.SwitchImg}"
                                Visibility="{Binding Bs, Converter={StaticResource VisibilityConverter}}" />
                        </StackPanel>
                    </Grid>
                </Grid>
            </Border>
            <Grid
                Name="DropFilePanel"
                Grid.Column="0"
                Visibility="Hidden">
                <Border props:ThemeProps.Background="{DynamicResource BorderContentBackground}" Opacity="0.8" />
                <TextBlock
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FontSize="{DynamicResource FontSize20}"
                    FontWeight="Bold"
                    Text="{DynamicResource OCR.DropImg}" />
                <Rectangle
                    Width="200"
                    Height="100"
                    RadiusX="10"
                    RadiusY="10"
                    Stroke="{DynamicResource TextForeground}"
                    StrokeDashArray="3,4"
                    StrokeThickness="4" />
            </Grid>

            <!--  // content //  -->
            <Border
                Grid.Column="1"
                Margin="10"
                props:ThemeProps.Background="{DynamicResource BorderContentBackground}"
                CornerRadius="5">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="2*" />
                        <RowDefinition>
                            <RowDefinition.Style>
                                <Style TargetType="RowDefinition">
                                    <Setter Property="Height" Value="auto" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding QrCodeContent, Converter={StaticResource String2IsEnableConverter}}" Value="true">
                                            <Setter Property="Height" Value="*" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </RowDefinition.Style>
                        </RowDefinition>
                    </Grid.RowDefinitions>

                    <!--  // OCR Content //  -->
                    <TextBox
                        x:Name="TB"
                        Margin="3,3,3,24"
                        ContextMenu="{StaticResource TBCM}"
                        FontSize="{DynamicResource FontSize18TextBox}"
                        PreviewMouseWheel="InputTB_PreviewMouseWheel"
                        Style="{DynamicResource TextBoxOCRStyle}"
                        Text="{Binding GetContent, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                    <!--  // content footer //  -->
                    <StackPanel
                        Grid.Row="0"
                        Margin="3,0"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Bottom"
                        Orientation="Horizontal">
                        <Button
                            Command="{Binding TTSCommand}"
                            CommandParameter="{Binding GetContent}"
                            Content="&#xe610;"
                            Style="{DynamicResource ButtonCopyIconStyle}"
                            ToolTip="{DynamicResource History.Content.TTS}"
                            Visibility="{Binding GetContent, Converter={StaticResource VisibilityConverter}}" />
                        <Button
                            Command="{Binding CopyCommand}"
                            CommandParameter="{Binding GetContent}"
                            Content="&#xe692;"
                            Style="{DynamicResource ButtonCopyIconStyle}"
                            ToolTip="{DynamicResource History.Content.Copy}"
                            Visibility="{Binding GetContent, Converter={StaticResource VisibilityConverter}}" />
                        <Button
                            Content="&#xe6b2;"
                            FontWeight="Bold"
                            Style="{DynamicResource ButtonCopyIconStyle}"
                            ToolTip="{DynamicResource Input.RemoveLineBreak}"
                            Visibility="{Binding GetContent, Converter={StaticResource VisibilityConverter}}">
                            <i:Interaction.Triggers>
                                <i:EventTrigger EventName="PreviewMouseLeftButtonDown">
                                    <i:InvokeCommandAction Command="{Binding RemoveLineBreaksCommand}" CommandParameter="{Binding ElementName=TB}" />
                                </i:EventTrigger>
                            </i:Interaction.Triggers>
                        </Button>
                        <Button
                            Command="{Binding RemoveSpaceCommand}"
                            CommandParameter="{Binding ElementName=TB}"
                            Content="&#xe6ab;"
                            FontSize="{DynamicResource FontSize20}"
                            FontWeight="Bold"
                            Style="{DynamicResource ButtonCopyIconStyle}"
                            ToolTip="{DynamicResource Input.RemoveSpaces}"
                            Visibility="{Binding GetContent, Converter={StaticResource VisibilityConverter}}" />
                    </StackPanel>

                    <Separator
                        Grid.Row="0"
                        VerticalAlignment="Bottom"
                        props:ThemeProps.BorderBrush="{DynamicResource TextForeground}"
                        BorderThickness="3"
                        Visibility="{Binding QrCodeContent, Converter={StaticResource VisibilityConverter}}" />
                    <!--  // QrCode Content //  -->
                    <TextBox
                        x:Name="QrTB"
                        Grid.Row="1"
                        Margin="3,23,3,3"
                        ContextMenu="{StaticResource QrTBCM}"
                        FontSize="{DynamicResource FontSize18TextBox}"
                        PreviewMouseWheel="InputTB_PreviewMouseWheel"
                        Style="{DynamicResource TextBoxOCRStyle}"
                        Text="{Binding QrCodeContent, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                        Visibility="{Binding QrCodeContent, Converter={StaticResource VisibilityConverter}}" />
                    <Button
                        Grid.Row="1"
                        Width="20"
                        Height="20"
                        Margin="2"
                        HorizontalAlignment="Right"
                        VerticalAlignment="Top"
                        Command="{Binding ClearQrContentCommand}"
                        Content="&#xe64d;"
                        FontSize="{DynamicResource FontSize12}"
                        Foreground="{DynamicResource ThemeAccentColor}"
                        Style="{DynamicResource ButtonIconStyle}"
                        Visibility="{Binding QrCodeContent, Converter={StaticResource VisibilityConverter}}" />
                    <TextBlock
                        Grid.Row="1"
                        Margin="4"
                        VerticalAlignment="Top"
                        FontFamily="{DynamicResource IconFont}"
                        FontSize="{DynamicResource FontSize14}"
                        Foreground="{DynamicResource ThemeAccentColor}"
                        IsHitTestVisible="False"
                        Text="&#xe642;"
                        Visibility="{Binding QrCodeContent, Converter={StaticResource VisibilityConverter}}" />
                    <TextBlock
                        Grid.Row="1"
                        Margin="22,2,2,2"
                        VerticalAlignment="Top"
                        FontSize="{DynamicResource FontSize14}"
                        Foreground="{DynamicResource ThemeAccentColor}"
                        IsHitTestVisible="False"
                        Text="{DynamicResource OCR.QRCodeResult}"
                        Visibility="{Binding QrCodeContent, Converter={StaticResource VisibilityConverter}}" />

                    <control:LoadingUc
                        Grid.Row="0"
                        Margin="2,5"
                        IsAnimationPlaying="{Binding IsExecuting}"
                        Visibility="{Binding IsExecuting, Converter={StaticResource BooleanToVisibilityConverter}}" />
                </Grid>
            </Border>
        </Grid>

        <!--  //Footer//  -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition />
                <ColumnDefinition />
            </Grid.ColumnDefinitions>

            <StackPanel
                Grid.ColumnSpan="2"
                Margin="10,0,10,10"
                HorizontalAlignment="Left"
                Orientation="Horizontal">
                <Button
                    Margin="10,0"
                    Command="{Binding OpenfileCommand}"
                    Content="{DynamicResource OCR.File}"
                    ToolTip="{DynamicResource OCR.File.Tooltip}" />
                <Button
                    Margin="10,0"
                    Command="{Binding ScreenshotCommand}"
                    CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                    Content="{DynamicResource OCR.Screenshot}"
                    ToolTip="{DynamicResource OCR.Screenshot.Tooltip}" />
                <Button
                    Margin="10,0"
                    Command="{Binding ClipboardImgCommand}"
                    Content="{DynamicResource OCR.Clipboard}"
                    ToolTip="{DynamicResource OCR.Clipboard.Tooltip}" />

                <Separator Width="20" Margin="10,0">
                    <Separator.LayoutTransform>
                        <RotateTransform Angle="90" />
                    </Separator.LayoutTransform>
                </Separator>

                <Button
                    Margin="10,0"
                    Command="{Binding OCRPreferenceCommand}"
                    CommandParameter="{Binding OcrScvVm.ActivedOCR}"
                    Content="{DynamicResource OCR.Setting}" />

                <ComboBox
                    MaxWidth="280"
                    Margin="10,0"
                    BorderThickness="1"
                    DisplayMemberPath="Name"
                    FontSize="{DynamicResource FontSize18}"
                    ItemsSource="{Binding OcrScvVm.CurOCRServiceList}"
                    SelectedValue="{Binding OcrScvVm.ActivedOCR, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />

                <ComboBox
                    x:Name="LangCb"
                    Margin="10,0"
                    common:LangAwareSelector.IsLangAware="True"
                    BorderThickness="1"
                    DisplayMemberPath="Description"
                    FontSize="{DynamicResource FontSize18}"
                    SelectedValue="{Binding Lang, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                    SelectedValuePath="Value">
                    <ComboBox.ItemsSource>
                        <MultiBinding Converter="{StaticResource MultiLangFilterConverter}">
                            <Binding Source="{common:LangEnumeration {x:Type model:LangEnum}}" />
                            <Binding Path="InputVm.OftenUsedLang" />
                        </MultiBinding>
                    </ComboBox.ItemsSource>
                </ComboBox>
            </StackPanel>

            <StackPanel
                Grid.Column="1"
                Margin="10,0,10,10"
                HorizontalAlignment="Right"
                FlowDirection="RightToLeft"
                Orientation="Horizontal">
                <Button
                    Margin="10,0"
                    HorizontalAlignment="Right"
                    Command="{Binding TranslateCommand}"
                    Content="{DynamicResource OCR.Translate}">
                    <Button.CommandParameter>
                        <MultiBinding Converter="{StaticResource MultiValue2ListConverter}">
                            <Binding Path="GetContent" />
                            <Binding RelativeSource="{RelativeSource AncestorType=Window}" />
                        </MultiBinding>
                    </Button.CommandParameter>
                </Button>
                <Button
                    Margin="10,0"
                    Command="{Binding QRCodeCommand}"
                    Content="{DynamicResource OCR.QRCode}" />
                <Button
                    Margin="10,0"
                    Command="{Binding RecertificationCommand}"
                    Content="{DynamicResource OCR.Recertification}" />
            </StackPanel>
        </Grid>

        <!--  // Notify //  -->
        <view:ToastView
            x:Name="Notify"
            Grid.Row="0"
            Grid.RowSpan="2"
            Margin="0,8,0,0"
            VerticalAlignment="Top"
            Visibility="Collapsed" />
    </Grid>
</Window>