using CommunityToolkit.Mvvm.Input;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using STranslate.Helper;
using STranslate.Log;
using STranslate.Model;
using STranslate.Util;
using System.ComponentModel;
using System.Net.Http;
using System.Text;

namespace STranslate.ViewModels.Preference.AIAssistant;

public partial class AIAssistantOpenAI : AIAssistantBase
{
    #region Constructor

    public AIAssistantOpenAI()
        : this(Guid.NewGuid(), "https://api.openai.com", "OpenAI助手")
    {
    }

    public AIAssistantOpenAI(
        Guid guid,
        string url,
        string name = "",
        IconType icon = IconType.OpenAIAssistant,
        string appID = "",
        string appKey = "",
        bool isEnabled = true,
        ServiceType type = ServiceType.OpenAIAssistant
    )
    {
        Identify = guid;
        Url = url;
        Name = name;
        Icon = icon;
        AppID = appID;
        AppKey = appKey;
        IsEnabled = isEnabled;
        Type = type;
        
        // 设置默认模型
        Model = "gpt-4o";
        Models = [
            "gpt-4o",
            "gpt-4o-mini",
            "gpt-4-turbo",
            "gpt-4",
            "gpt-3.5-turbo",
            "o1-preview",
            "o1-mini"
        ];
    }

    #endregion Constructor

    #region Properties

    [JsonIgnore] public bool KeyHide { get; set; } = true;

    #endregion Properties

    #region Test Methods

    [RelayCommand]
    [property: JsonIgnore]
    private async Task TestAsync(CancellationToken token)
    {
        var chatRequest = new ChatRequest("Hello, please respond with 'Test successful!' to confirm the connection.");
        var result = string.Empty;
        
        try
        {
            await ChatAsync(chatRequest, chunk => result += chunk, token);
            ToastHelper.Show("测试成功", WindowType.Preference);
        }
        catch (Exception ex)
        {
            ToastHelper.Show($"测试失败: {ex.Message}", WindowType.Preference);
            LogService.Logger.Error($"[{Name}] 测试失败: {ex.Message}");
        }
    }

    #endregion Test Methods

    #region Interface Implementation

    public override async Task ChatAsync(ChatRequest request, Action<string> onDataReceived, CancellationToken token)
    {
        if (string.IsNullOrEmpty(Url) || string.IsNullOrEmpty(AppKey))
            throw new Exception("请先完善配置");

        UriBuilder uriBuilder = new(Url);
        if (uriBuilder.Path == "/")
            uriBuilder.Path = "/v1/chat/completions";

        // 选择模型
        var selectedModel = string.IsNullOrEmpty(Model) ? "gpt-4o" : Model.Trim();

        // 构建消息列表
        var messages = new List<object>();
        
        // 添加系统提示词
        var systemPrompt = GetActiveSystemPrompt();
        if (!string.IsNullOrWhiteSpace(systemPrompt))
        {
            messages.Add(new { role = "system", content = systemPrompt });
        }

        // 添加历史记录
        if (KeepContext && ChatHistory.Count > 0)
        {
            foreach (var msg in ChatHistory.TakeLast(MaxHistoryLength))
            {
                messages.Add(new { role = msg.Role, content = msg.Content });
            }
        }

        // 添加当前用户消息
        messages.Add(new { role = "user", content = request.Message });

        // 构建请求数据
        var reqData = new
        {
            model = selectedModel,
            messages = messages,
            temperature = Math.Clamp(Temperature, 0, 2),
            stream = true
        };

        var jsonData = JsonConvert.SerializeObject(reqData);
        var assistantResponse = string.Empty;

        try
        {
            await HttpUtil.PostAsync(
                uriBuilder.Uri,
                jsonData,
                AppKey,
                msg =>
                {
                    if (string.IsNullOrEmpty(msg?.Trim()))
                        return;

                    var preprocessString = msg.Replace("data:", "").Trim();

                    // 结束标记
                    if (preprocessString.Equals("[DONE]"))
                        return;

                    try
                    {
                        // 解析JSON数据
                        var parsedData = JsonConvert.DeserializeObject<JObject>(preprocessString);
                        if (parsedData == null) return;

                        // 提取content的值
                        var contentValue = parsedData["choices"]?.FirstOrDefault()?["delta"]?["content"]?.ToString();
                        if (string.IsNullOrEmpty(contentValue)) return;

                        assistantResponse += contentValue;
                        onDataReceived?.Invoke(contentValue);
                    }
                    catch
                    {
                        // 忽略解析错误
                    }
                },
                token
            ).ConfigureAwait(false);

            // 对话完成后，添加到历史记录
            if (KeepContext && !string.IsNullOrWhiteSpace(assistantResponse))
            {
                AddToHistory(ChatMessage.User(request.Message));
                AddToHistory(ChatMessage.Assistant(assistantResponse));
            }
        }
        catch (OperationCanceledException)
        {
            throw;
        }
        catch (HttpRequestException ex) when (ex.StatusCode == null)
        {
            var msg = $"请检查服务是否可以正常访问: {Name} ({Url}).\n{ex.Message}";
            throw new HttpRequestException(msg);
        }
        catch (HttpRequestException)
        {
            throw;
        }
        catch (Exception ex)
        {
            var msg = ex.Message;
            if (ex.InnerException is HttpRequestException httpEx)
            {
                if (httpEx.Data.Contains("StatusCode"))
                {
                    var statusCode = httpEx.Data["StatusCode"]!.ToString();
                    msg = $"请求失败，状态码: {statusCode}";
                }
            }
            throw new Exception(msg);
        }
    }

    public override ITranslator Clone()
    {
        return new AIAssistantOpenAI
        {
            Identify = Identify,
            Type = Type,
            IsEnabled = IsEnabled,
            Icon = Icon,
            Name = Name,
            Url = Url,
            Data = TranslationResult.Reset,
            AppID = AppID,
            AppKey = AppKey,
            AutoExecute = AutoExecute,
            AutoExecuteTranslateBack = AutoExecuteTranslateBack,
            IsExecuting = IsExecuting,
            IsTranslateBackExecuting = IsTranslateBackExecuting,
            Temperature = Temperature,
            Model = Model,
            Models = new BindingList<string>(Models.ToList()),
            UserDefinePrompts = new BindingList<UserDefinePrompt>(UserDefinePrompts.Select(x => x.Clone()).ToList()),
            SystemPrompt = SystemPrompt,
            ChatHistory = new BindingList<ChatMessage>(),
            MaxHistoryLength = MaxHistoryLength,
            KeepContext = KeepContext,
            KeyHide = KeyHide
        };
    }

    #endregion Interface Implementation
}
