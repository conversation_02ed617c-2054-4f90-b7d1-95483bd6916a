﻿<UserControl
    x:Class="STranslate.Style.Controls.LoadingUc2"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:STranslate.Style.Controls"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Name="Root"
    d:DesignHeight="450"
    d:DesignWidth="800"
    mc:Ignorable="d">
    <Grid>
        <StackPanel
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            Orientation="Horizontal"
            Visibility="{Binding IsLoading, ElementName=Root, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel.Resources>
                <!--  定义缩放动画样式  -->
                <Style x:Key="LoadingDotStyle" TargetType="Ellipse">
                    <Setter Property="Width" Value="{Binding DotSize, ElementName=Root}" />
                    <Setter Property="Height" Value="{Binding DotSize, ElementName=Root}" />
                    <Setter Property="Fill" Value="{Binding DotColor, ElementName=Root}" />
                    <Setter Property="Margin" Value="2" />
                </Style>
            </StackPanel.Resources>

            <!--  三个小球  -->
            <Ellipse x:Name="Dot1" Style="{StaticResource LoadingDotStyle}" />
            <Ellipse x:Name="Dot2" Style="{StaticResource LoadingDotStyle}" />
            <Ellipse x:Name="Dot3" Style="{StaticResource LoadingDotStyle}" />
            <Ellipse x:Name="Dot4" Style="{StaticResource LoadingDotStyle}" />
            <Ellipse x:Name="Dot5" Style="{StaticResource LoadingDotStyle}" />
        </StackPanel>
    </Grid>
</UserControl>
