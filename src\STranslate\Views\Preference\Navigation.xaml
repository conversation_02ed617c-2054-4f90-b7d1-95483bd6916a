﻿<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:view="clr-namespace:STranslate.Views.Preference"
    xmlns:vm="clr-namespace:STranslate.ViewModels.Preference">
    <DataTemplate DataType="{x:Type vm:CommonViewModel}">
        <view:CommonPage />
    </DataTemplate>

    <DataTemplate DataType="{x:Type vm:HotkeyViewModel}">
        <view:HotkeyPage />
    </DataTemplate>

    <DataTemplate DataType="{x:Type vm:TranslatorViewModel}">
        <view:TranslatorPage />
    </DataTemplate>

    <DataTemplate DataType="{x:Type vm:ReplaceViewModel}">
        <view:ReplacePage />
    </DataTemplate>

    <DataTemplate DataType="{x:Type vm:OCRScvViewModel}">
        <view:OCRPage />
    </DataTemplate>

    <DataTemplate DataType="{x:Type vm:TTSViewModel}">
        <view:TTSPage />
    </DataTemplate>

    <DataTemplate DataType="{x:Type vm:FavoriteViewModel}">
        <view:FavoritePage />
    </DataTemplate>

    <DataTemplate DataType="{x:Type vm:HistoryViewModel}">
        <view:HistoryPage />
    </DataTemplate>

    <DataTemplate DataType="{x:Type vm:BackupViewModel}">
        <view:BackupPage />
    </DataTemplate>

    <DataTemplate DataType="{x:Type vm:AboutViewModel}">
        <view:AboutPage />
    </DataTemplate>

    <DataTemplate DataType="{x:Type vm:ServiceViewModel}">
        <view:ServicePage />
    </DataTemplate>

</ResourceDictionary>