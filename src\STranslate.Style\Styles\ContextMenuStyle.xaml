﻿<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:props="clr-namespace:STranslate.Style.Themes">

    <!--  // Outer menu items //  -->
    <Style TargetType="{x:Type MenuItem}">
        <Style.Triggers>
            <Trigger Property="IsHighlighted" Value="False">
                <Setter Property="Height" Value="30" />
            </Trigger>
            <Trigger Property="IsHighlighted" Value="True">
                <Setter Property="Height" Value="30" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Foreground" Value="{DynamicResource MenuItemDisabledForeground}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--  // Outer menu //  -->
    <Style TargetType="{x:Type ContextMenu}">
        <Setter Property="FontFamily" Value="{DynamicResource UserFont}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ContextMenu}">
                    <!--  Here is where you change the border thickness to zero on the menu  -->
                    <Border
                        x:Name="Border"
                        Width="{TemplateBinding Width}"
                        Padding="5"
                        props:ThemeProps.Background="{DynamicResource BorderBackground}"
                        props:ThemeProps.BorderBrush="{DynamicResource BorderBrushColor}"
                        BorderThickness="1"
                        CornerRadius="5">
                        <StackPanel
                            ClipToBounds="True"
                            IsItemsHost="True"
                            KeyboardNavigation.DirectionalNavigation="Cycle"
                            Orientation="Vertical" />
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  // SubmenuItem //  -->
    <ControlTemplate x:Key="{x:Static MenuItem.SubmenuItemTemplateKey}" TargetType="{x:Type MenuItem}">
        <Border
            Name="Border"
            Width="{TemplateBinding Width}"
            Background="Transparent"
            ClipToBounds="True"
            CornerRadius="5">
            <StackPanel Margin="0,0,10,0" Orientation="Horizontal">
                <TextBlock
                    Width="26"
                    Margin="10,0,0,0"
                    VerticalAlignment="Center"
                    props:ThemeProps.Foreground="{DynamicResource BtnForeground}"
                    FontFamily="{DynamicResource IconFont}"
                    FontSize="{DynamicResource FontSize16}"
                    Text="{TemplateBinding Icon}" />
                <TextBlock
                    MaxWidth="120"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center"
                    props:ThemeProps.Foreground="{DynamicResource TextForeground}"
                    FontSize="{DynamicResource FontSize14}"
                    Text="{TemplateBinding Header}"
                    TextTrimming="CharacterEllipsis"
                    ToolTip="{TemplateBinding Header}" />
                <TextBlock
                    x:Name="Checkabled"
                    Margin="10,0,0,0"
                    VerticalAlignment="Center"
                    FontFamily="{DynamicResource IconFont}"
                    FontSize="{DynamicResource FontSize14}"
                    Text="&#xec9e;"
                    Visibility="Collapsed" />
            </StackPanel>
        </Border>
        <ControlTemplate.Triggers>
            <Trigger Property="IsHighlighted" Value="true">
                <Setter TargetName="Border" Property="Background" Value="{DynamicResource MenuItemHighlighted}" />
            </Trigger>
            <Trigger Property="IsCheckable" Value="true">
                <Setter TargetName="Checkabled" Property="Visibility" Value="Visible" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  // SubmenuHeader //  -->
    <ControlTemplate x:Key="{x:Static MenuItem.SubmenuHeaderTemplateKey}" TargetType="{x:Type MenuItem}">
        <Border Name="Border">
            <Grid Background="Transparent">
                <StackPanel Orientation="Horizontal">
                    <TextBlock
                        Margin="10,0,10,0"
                        VerticalAlignment="Center"
                        props:ThemeProps.Foreground="{DynamicResource BtnForeground}"
                        FontFamily="{DynamicResource IconFont}"
                        FontSize="{DynamicResource FontSize16}"
                        Text="{TemplateBinding Icon}" />
                    <TextBlock
                        VerticalAlignment="Center"
                        props:ThemeProps.Foreground="{DynamicResource TextForeground}"
                        FontSize="{DynamicResource FontSize14}"
                        Text="{TemplateBinding Header}" />
                </StackPanel>
                <TextBlock
                    Margin="0,0,10,0"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Center"
                    FontFamily="{DynamicResource IconFont}"
                    FontSize="{DynamicResource FontSize10}"
                    Text="&#xe629;" />
                <Popup
                    Name="Popup"
                    Width="{TemplateBinding Width}"
                    AllowsTransparency="True"
                    Focusable="False"
                    HorizontalOffset="-5"
                    IsOpen="{TemplateBinding IsSubmenuOpen}"
                    Placement="Right"
                    PopupAnimation="Fade">
                    <StackPanel Orientation="Horizontal">
                        <Border
                            Name="SubmenuBorder"
                            Width="5"
                            Height="35"
                            Padding="5"
                            props:ThemeProps.Background="{DynamicResource BorderBackground}"
                            Opacity="0.01">
                            <StackPanel IsItemsHost="True" KeyboardNavigation.DirectionalNavigation="Cycle" />
                        </Border>
                    </StackPanel>
                </Popup>
            </Grid>
        </Border>

        <ControlTemplate.Triggers>
            <Trigger Property="IsHighlighted" Value="true">
                <Setter TargetName="Border" Property="Background" Value="{DynamicResource MenuItemHighlighted}" />
            </Trigger>
            <Trigger SourceName="Popup" Property="Popup.AllowsTransparency" Value="True">
                <Setter TargetName="SubmenuBorder" Property="CornerRadius" Value="4" />
                <Setter TargetName="SubmenuBorder" Property="Padding" Value="0,3,0,3" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>
</ResourceDictionary>