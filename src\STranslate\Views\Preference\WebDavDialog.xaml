﻿<Window
    x:Class="STranslate.Views.Preference.WebDavDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:common="clr-namespace:STranslate.Style.Commons;assembly=STranslate.Style"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:props="clr-namespace:STranslate.Style.Themes;assembly=STranslate.Style"
    xmlns:vm="clr-namespace:STranslate.ViewModels.Preference"
    Title="WebDavDialog"
    Width="600"
    MaxHeight="400"
    d:DataContext="{d:DesignInstance Type=vm:WebDavViewModel}"
    props:ThemeProps.Background="{DynamicResource NavigationBackground}"
    props:ThemeProps.BorderBrush="{DynamicResource BorderBrushColor}"
    BorderThickness="1"
    FontFamily="{DynamicResource UserFont}"
    Icon="{DynamicResource Icon}"
    SizeToContent="Height"
    Topmost="True"
    WindowStartupLocation="CenterScreen"
    mc:Ignorable="d">
    <WindowChrome.WindowChrome>
        <WindowChrome />
    </WindowChrome.WindowChrome>
    <Window.InputBindings>
        <KeyBinding
            Key="Esc"
            Command="{Binding CloseCommand}"
            CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}" />
    </Window.InputBindings>

    <Grid Margin="5">
        <Grid.RowDefinitions>
            <RowDefinition Height="35" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>
        <!--  // Header //  -->
        <Border
            Name="Header"
            props:ThemeProps.Background="{DynamicResource BorderBackground}"
            CornerRadius="5"
            MouseDown="Header_MouseDown"
            WindowChrome.IsHitTestVisibleInChrome="True">

            <Grid>
                <!--  // Title //  -->
                <StackPanel
                    Margin="20,0"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center"
                    Orientation="Horizontal">
                    <Image Height="24" Source="{DynamicResource STranslate}" />
                    <TextBlock
                        Margin="16,0,0,0"
                        VerticalAlignment="Center"
                        props:ThemeProps.Foreground="{DynamicResource NavigationForeground}"
                        FontSize="{DynamicResource FontSize20}"
                        FontWeight="Bold"
                        Text="{DynamicResource WebDav.Title}" />
                </StackPanel>

                <!--  // Button //  -->
                <StackPanel
                    Margin="15,0"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Center"
                    Orientation="Horizontal">
                    <Button
                        Command="{Binding MinimizeCommand}"
                        CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                        Content="&#xe676;"
                        FontSize="{DynamicResource FontSize20}"
                        Style="{DynamicResource ButtonIconStyle}"
                        Visibility="Collapsed" />
                    <Button
                        Command="{Binding MaximizeCommand}"
                        CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                        Content="{Binding MaximizeContent}"
                        FontWeight="Bold"
                        Style="{DynamicResource ButtonIconStyle}"
                        Visibility="Collapsed" />
                    <Button
                        Command="{Binding CloseCommand}"
                        CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                        Content="&#xe64d;"
                        FontSize="{DynamicResource FontSize16}"
                        Style="{DynamicResource ButtonCloseStyle}" />
                </StackPanel>
            </Grid>
        </Border>

        <ScrollViewer Grid.Row="1" FontSize="{DynamicResource FontSize18}">
            <ListBox
                x:Name="listbox"
                Background="Transparent"
                BorderThickness="0"
                ItemsSource="{Binding WebDavResultList}"
                PreviewMouseWheel="ListBox_PreviewMouseWheel"
                ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                <ListBox.ItemTemplate>
                    <DataTemplate>
                        <Border Style="{DynamicResource BorderInOutputStyle}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="50" />
                                    <ColumnDefinition Width="50" />
                                    <ColumnDefinition Width="50" />
                                </Grid.ColumnDefinitions>

                                <TextBlock
                                    Margin="20,0,0,0"
                                    FontWeight="Bold"
                                    Text="{Binding FullName}"
                                    TextTrimming="CharacterEllipsis"
                                    ToolTip="{Binding FullName}"
                                    Visibility="{Binding IsEdit, Converter={StaticResource BooleanToVisibilityReverseConverter}}" />
                                <TextBox
                                    Margin="20,0,0,0"
                                    common:DisallowSpecialCharactersTextboxBehavior.DisallowSpecialCharacters="True"
                                    FontWeight="Bold"
                                    Text="{Binding Name, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                    Visibility="{Binding IsEdit, Converter={StaticResource BooleanToVisibilityConverter}}">
                                    <TextBox.InputBindings>
                                        <KeyBinding
                                            Key="Enter"
                                            Command="{Binding DataContext.ConfirmCommand, RelativeSource={RelativeSource AncestorType=ListBox}}"
                                            CommandParameter="{Binding FullName}" />
                                    </TextBox.InputBindings>
                                </TextBox>

                                <Button
                                    Grid.Column="1"
                                    Command="{Binding DataContext.DownloadCommand, RelativeSource={RelativeSource AncestorType=ListBox}}"
                                    Content="&#xe6d5;"
                                    FontWeight="Bold"
                                    Style="{DynamicResource ButtonIconStyle}"
                                    Visibility="{Binding IsEdit, Converter={StaticResource BooleanToVisibilityReverseConverter}}">
                                    <Button.CommandParameter>
                                        <MultiBinding Converter="{StaticResource MultiValue2ListConverter}">
                                            <Binding Path="FullName" />
                                            <Binding Path="." RelativeSource="{RelativeSource AncestorType=Window}" />
                                        </MultiBinding>
                                    </Button.CommandParameter>
                                </Button>

                                <Button
                                    Grid.Column="2"
                                    Command="{Binding DataContext.UpdateCommand, RelativeSource={RelativeSource AncestorType=ListBox}}"
                                    CommandParameter="{Binding FullName}"
                                    Content="&#xe61b;"
                                    FontWeight="Bold"
                                    Style="{DynamicResource ButtonIconStyle}"
                                    Visibility="{Binding IsEdit, Converter={StaticResource BooleanToVisibilityReverseConverter}}" />

                                <Button
                                    Grid.Column="3"
                                    Command="{Binding DataContext.DeleteCommand, RelativeSource={RelativeSource AncestorType=ListBox}}"
                                    CommandParameter="{Binding FullName}"
                                    Content="&#xe74b;"
                                    FontWeight="Bold"
                                    Style="{DynamicResource ButtonIconStyle}"
                                    Visibility="{Binding IsEdit, Converter={StaticResource BooleanToVisibilityReverseConverter}}" />

                                <Button
                                    Grid.Column="2"
                                    Command="{Binding DataContext.ConfirmCommand, RelativeSource={RelativeSource AncestorType=ListBox}}"
                                    CommandParameter="{Binding FullName}"
                                    Content="√"
                                    FontWeight="Bold"
                                    Style="{DynamicResource ButtonIconStyle}"
                                    Visibility="{Binding IsEdit, Converter={StaticResource BooleanToVisibilityConverter}}" />

                                <Button
                                    Grid.Column="3"
                                    Command="{Binding DataContext.CancelCommand, RelativeSource={RelativeSource AncestorType=ListBox}}"
                                    CommandParameter="{Binding FullName}"
                                    Content="×"
                                    FontWeight="Bold"
                                    Style="{DynamicResource ButtonIconStyle}"
                                    Visibility="{Binding IsEdit, Converter={StaticResource BooleanToVisibilityConverter}}" />
                            </Grid>
                        </Border>
                    </DataTemplate>
                </ListBox.ItemTemplate>
            </ListBox>
        </ScrollViewer>
        <Border
            Grid.Row="1"
            Style="{DynamicResource BorderInOutputStyle}"
            Visibility="{Binding WebDavResultList, Converter={StaticResource Collection2VisibilityConverter}}">
            <TextBlock
                Margin="0,10"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="{DynamicResource FontSize20}"
                FontWeight="Bold"
                Text="{DynamicResource WebDav.NoContent}" />
        </Border>
    </Grid>
</Window>