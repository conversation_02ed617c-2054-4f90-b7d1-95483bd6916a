﻿<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:props="clr-namespace:STranslate.Style.Themes">

    <Style TargetType="ToggleButton">
        <!--  // Checked颜色 //  -->
        <Setter Property="props:ThemeProps.Foreground" Value="{DynamicResource ToggleBtnForeground}" />
        <!--  // 非Checked颜色 //  -->
        <Setter Property="props:ThemeProps.Background" Value="{DynamicResource ToggleBtnBackground}" />
        <!--  // 未使用 //  -->
        <Setter Property="props:ThemeProps.BorderBrush" Value="{DynamicResource ToggleBtnBrushColor}" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Padding" Value="6,0,0,0" />
        <Setter Property="Height" Value="20" />
        <Setter Property="FocusVisualStyle">
            <Setter.Value>
                <Style>
                    <Setter Property="Control.Template">
                        <Setter.Value>
                            <ControlTemplate>
                                <Rectangle>
                                    <Rectangle.Style>
                                        <Style TargetType="Rectangle">
                                            <Setter Property="Margin" Value="-2" />
                                            <Setter Property="Opacity" Value=".6" />
                                            <Setter Property="SnapsToDevicePixels" Value="True" />
                                            <!--  //未使用//  -->
                                            <Setter Property="Stroke" Value="#757575" />
                                            <Setter Property="StrokeThickness" Value="2" />
                                            <Setter Property="StrokeDashArray" Value="1 1" />
                                        </Style>
                                    </Rectangle.Style>
                                </Rectangle>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ToggleButton">
                    <ControlTemplate.Resources>
                        <Storyboard x:Key="StoryboardChecked">
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="thumb" Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.X)">
                                <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="50">
                                    <EasingDoubleKeyFrame.EasingFunction>
                                        <CubicEase EasingMode="EaseInOut" />
                                    </EasingDoubleKeyFrame.EasingFunction>
                                </EasingDoubleKeyFrame>
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="lightBorder" Storyboard.TargetProperty="Opacity">
                                <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="1" />
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="innerBorder" Storyboard.TargetProperty="Opacity">
                                <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="0" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                        <Storyboard x:Key="StoryboardUnChecked">
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="thumb" Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.X)">
                                <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="0">
                                    <EasingDoubleKeyFrame.EasingFunction>
                                        <CubicEase EasingMode="EaseInOut" />
                                    </EasingDoubleKeyFrame.EasingFunction>
                                </EasingDoubleKeyFrame>
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="lightBorder" Storyboard.TargetProperty="Opacity">
                                <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="0" />
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="innerBorder" Storyboard.TargetProperty="Opacity">
                                <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="1" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                    </ControlTemplate.Resources>
                    <Grid
                        x:Name="templateRoot"
                        Background="Transparent"
                        SnapsToDevicePixels="True">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <Viewbox>
                            <Grid Width="100" Height="48">
                                <Border
                                    Name="innerBorder"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding Background}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="24" />
                                <Border
                                    x:Name="lightBorder"
                                    Background="{TemplateBinding Foreground}"
                                    BorderBrush="{TemplateBinding Foreground}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="24"
                                    Opacity="0" />
                                <Border
                                    Name="thumb"
                                    Width="38"
                                    Height="38"
                                    Margin="5"
                                    HorizontalAlignment="Left"
                                    Background="{DynamicResource ToggleBtnThumbColor}"
                                    CornerRadius="22"
                                    RenderTransformOrigin="0.5,0.5">
                                    <Border.Effect>
                                        <DropShadowEffect
                                            BlurRadius="8"
                                            Direction="270"
                                            Opacity=".2"
                                            RenderingBias="Performance"
                                            ShadowDepth="1.5"
                                            Color="{DynamicResource ToggleBtnThumbDropShadowEffectColor}" />
                                    </Border.Effect>
                                    <Border.RenderTransform>
                                        <TransformGroup>
                                            <TranslateTransform />
                                        </TransformGroup>
                                    </Border.RenderTransform>
                                </Border>
                            </Grid>
                        </Viewbox>
                        <ContentPresenter
                            x:Name="contentPresenter"
                            Grid.Column="1"
                            Margin="{TemplateBinding Padding}"
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                            Focusable="False"
                            RecognizesAccessKey="True"
                            SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"
                            Visibility="Collapsed" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="HasContent" Value="true">
                            <Setter TargetName="contentPresenter" Property="Visibility" Value="Visible" />
                        </Trigger>
                        <Trigger Property="IsChecked" Value="true">
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource StoryboardChecked}" />
                            </Trigger.EnterActions>
                            <Trigger.ExitActions>
                                <BeginStoryboard Storyboard="{StaticResource StoryboardUnChecked}" />
                            </Trigger.ExitActions>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value="0.4" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  // 普通 Icon ToggleButton //  -->
    <Style x:Key="ToggleButtonIconStyle" TargetType="ToggleButton">
        <Setter Property="FontFamily" Value="{DynamicResource IconFont}" />
        <Setter Property="FontSize" Value="{DynamicResource FontSize18}" />
        <Setter Property="props:ThemeProps.Background" Value="{DynamicResource BorderBackground}" />
        <Setter Property="props:ThemeProps.Foreground" Value="{DynamicResource BtnForeground}" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Height" Value="30" />
        <Setter Property="Focusable" Value="False" />
        <Setter Property="MinWidth" Value="30" />
        <Setter Property="Margin" Value="3" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ToggleButton">
                    <Border
                        x:Name="border"
                        Width="{TemplateBinding Width}"
                        Height="{TemplateBinding Height}"
                        Padding="{TemplateBinding Padding}"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="5"
                        SnapsToDevicePixels="true">
                        <TextBlock
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            props:ThemeProps.Foreground="{TemplateBinding Foreground}"
                            FontFamily="{TemplateBinding FontFamily}"
                            FontSize="{TemplateBinding FontSize}"
                            Text="{TemplateBinding Content}"
                            TextTrimming="CharacterEllipsis" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnMouseOverBackground}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="true">
                            <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnPressedBackground}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnDisabledBackground}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  // 普通 ToggleButton //  -->
    <Style x:Key="ToggleButtonTextStyle" TargetType="ToggleButton">
        <Setter Property="FontFamily" Value="{DynamicResource UserFont}" />
        <Setter Property="FontSize" Value="{DynamicResource FontSize18}" />
        <Setter Property="props:ThemeProps.Background" Value="{DynamicResource BtnBackground}" />
        <Setter Property="props:ThemeProps.Foreground" Value="{DynamicResource TextForeground}" />
        <Setter Property="Height" Value="30" />
        <Setter Property="Focusable" Value="False" />
        <Setter Property="Width" Value="30" />
        <Setter Property="Margin" Value="3" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ToggleButton">
                    <Border
                        x:Name="border"
                        Width="{TemplateBinding Width}"
                        Height="{TemplateBinding Height}"
                        Padding="{TemplateBinding Padding}"
                        Background="{TemplateBinding Background}"
                        CornerRadius="5"
                        SnapsToDevicePixels="true">
                        <TextBlock
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            FontFamily="{TemplateBinding FontFamily}"
                            FontSize="{TemplateBinding FontSize}"
                            Foreground="{TemplateBinding Foreground}"
                            Text="{TemplateBinding Content}" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnMouseOverBackground}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="true">
                            <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnPressedBackground}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnDisabledBackground}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>