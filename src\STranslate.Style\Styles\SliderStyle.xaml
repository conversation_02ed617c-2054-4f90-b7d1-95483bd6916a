﻿<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:props="clr-namespace:STranslate.Style.Themes">

    <!--  // Slider //  -->
    <Style x:Key="RepeatButtonTransparent" TargetType="{x:Type RepeatButton}">
        <Setter Property="OverridesDefaultStyle" Value="true" />
        <Setter Property="Focusable" Value="false" />
        <Setter Property="IsTabStop" Value="false" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type RepeatButton}">
                    <Border
                        Width="{TemplateBinding Width}"
                        Height="{TemplateBinding Height}"
                        Background="{TemplateBinding Background}"
                        CornerRadius="2,0,0,2" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <ControlTemplate x:Key="SliderThumbHorizontalDefault" TargetType="{x:Type Thumb}">
        <Grid
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            UseLayoutRounding="True">
            <Ellipse
                Width="{TemplateBinding Width}"
                Height="{TemplateBinding Height}"
                Fill="{TemplateBinding Foreground}" />
        </Grid>
    </ControlTemplate>

    <ControlTemplate x:Key="SliderHorizontal" TargetType="{x:Type Slider}">
        <Grid props:ThemeProps.Background="Transparent" SnapsToDevicePixels="True">
            <Border
                x:Name="TrackBackground"
                Height="4.0"
                VerticalAlignment="center"
                Background="{TemplateBinding Background}"
                CornerRadius="2" />

            <Track x:Name="PART_Track">
                <Track.DecreaseRepeatButton>
                    <RepeatButton
                        Height="4.0"
                        Background="{TemplateBinding Foreground}"
                        Style="{StaticResource RepeatButtonTransparent}" />
                </Track.DecreaseRepeatButton>

                <Track.Thumb>
                    <Thumb
                        x:Name="Thumb"
                        Width="{TemplateBinding Height}"
                        Height="{TemplateBinding Height}"
                        VerticalAlignment="Center"
                        Cursor="Hand"
                        Focusable="False"
                        Foreground="{TemplateBinding Foreground}"
                        OverridesDefaultStyle="True"
                        Template="{StaticResource SliderThumbHorizontalDefault}" />
                </Track.Thumb>
            </Track>
        </Grid>
    </ControlTemplate>

    <Style TargetType="{x:Type Slider}">
        <Setter Property="Stylus.IsPressAndHoldEnabled" Value="false" />
        <Setter Property="props:ThemeProps.Background" Value="{DynamicResource ThumbBackground}" />
        <Setter Property="props:ThemeProps.Foreground" Value="{DynamicResource ToggleBtnForeground}" />
        <Setter Property="Template" Value="{StaticResource SliderHorizontal}" />
    </Style>
</ResourceDictionary>