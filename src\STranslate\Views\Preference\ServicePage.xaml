﻿<UserControl
    x:Class="STranslate.Views.Preference.ServicePage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:props="clr-namespace:STranslate.Style.Themes;assembly=STranslate.Style"
    xmlns:uc="clr-namespace:STranslate.Views.Preference"
    xmlns:vm="clr-namespace:STranslate.ViewModels.Preference"
    d:DataContext="{d:DesignInstance Type=vm:ServiceViewModel}"
    d:DesignHeight="450"
    d:DesignWidth="800"
    d:FontFamily="{DynamicResource UserFont}"
    d:FontSize="{DynamicResource FontSize18}"
    Style="{StaticResource ResourceKey=Page_Style}"
    mc:Ignorable="d">
    <UserControl.Resources>
        <!--#region TabControl Style-->
        <Style TargetType="{x:Type TabControl}">
            <Setter Property="Margin" Value="20,-20,0,20" />
            <Setter Property="props:ThemeProps.Background" Value="{DynamicResource BorderBackground}" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type TabControl}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>
                            <TabPanel Grid.Row="0" IsItemsHost="True" />
                            <Border
                                Grid.Row="1"
                                props:ThemeProps.BorderBrush="{DynamicResource BorderBrushColor}"
                                BorderThickness="1"
                                CornerRadius="6">
                                <ContentPresenter ContentSource="SelectedContent" />
                            </Border>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <Style TargetType="{x:Type TabItem}">
            <Setter Property="Background" Value="Transparent" />
            <!--  设置初始背景色为透明  -->
            <Setter Property="props:ThemeProps.BorderBrush" Value="{DynamicResource BorderBackground}" />
            <Setter Property="BorderThickness" Value="0,0,0,2" />
            <!--  底部边框  -->
            <Setter Property="Padding" Value="10" />
            <!--  文本颜色  -->
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type TabItem}">
                        <Border
                            x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="5">
                            <ContentPresenter
                                x:Name="contentPresenter"
                                Margin="{TemplateBinding Padding}"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                ContentSource="Header" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <!--  鼠标悬停效果  -->
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="{DynamicResource BtnMouseOverBackground}" />
                                <!--  悬停背景色  -->
                                <Setter TargetName="contentPresenter" Property="TextElement.Foreground" Value="{DynamicResource BtnPressedBackground}" />
                                <!--  悬停文本颜色  -->
                            </Trigger>
                            <!--  选中效果  -->
                            <Trigger Property="IsSelected" Value="True">
                                <Setter TargetName="border" Property="Background" Value="{DynamicResource BorderContentBackground}" />
                                <!--  选中背景色  -->
                                <Setter TargetName="border" Property="BorderBrush" Value="{DynamicResource BorderBrushColor}" />
                                <!--  选中边框颜色  -->
                                <Setter TargetName="contentPresenter" Property="TextElement.Foreground" Value="{DynamicResource TextForeground}" />
                                <!--  选中文本颜色  -->
                            </Trigger>
                            <!--  鼠标点击动画  -->
                            <EventTrigger RoutedEvent="MouseLeftButtonDown">
                                <BeginStoryboard>
                                    <Storyboard>
                                        <DoubleAnimation
                                            AutoReverse="True"
                                            Storyboard.TargetName="border"
                                            Storyboard.TargetProperty="(Border.Opacity)"
                                            From="1.0"
                                            To="0.8"
                                            Duration="0:0:0.1" />
                                    </Storyboard>
                                </BeginStoryboard>
                            </EventTrigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <!--#endregion-->
    </UserControl.Resources>
    <Border
        Margin="8"
        props:ThemeProps.Background="{DynamicResource BorderBackground}"
        CornerRadius="5">
        <TabControl SelectedIndex="{Binding SelectedIndex}">
            <TabItem Header="{DynamicResource Service.Navi.Translator}">
                <uc:TranslatorPage />
            </TabItem>
            <TabItem Header="{DynamicResource Service.Navi.OCR}">
                <uc:OCRPage />
            </TabItem>
            <TabItem Header="{DynamicResource Service.Navi.TTS}">
                <uc:TTSPage />
            </TabItem>
            <TabItem Header="{DynamicResource Service.Navi.VocabularyBook}">
                <uc:VocabularyBookPage />
            </TabItem>
        </TabControl>
    </Border>
</UserControl>