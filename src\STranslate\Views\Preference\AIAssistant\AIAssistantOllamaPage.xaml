<UserControl
    x:Class="STranslate.Views.Preference.AIAssistant.AIAssistantOllamaPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:common="clr-namespace:STranslate.Style.Commons;assembly=STranslate.Style"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:props="clr-namespace:STranslate.Style.Themes;assembly=STranslate.Style"
    xmlns:vm="clr-namespace:STranslate.ViewModels.Preference.AIAssistant"
    d:DataContext="{d:DesignInstance Type=vm:AIAssistantOllama}"
    d:DesignHeight="850"
    d:DesignWidth="800"
    Background="Transparent"
    FontSize="{DynamicResource FontSize18}"
    mc:Ignorable="d">
    <Border
        Padding="10,0,0,0"
        props:ThemeProps.BorderBrush="{DynamicResource BorderBrushColor}"
        BorderThickness="1"
        CornerRadius="4">
        <ScrollViewer>
            <StackPanel>
                <Grid Margin="0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="80" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    <TextBlock Text="{DynamicResource Service.Name}" />

                    <common:PlaceholderTextBox
                        Grid.Column="1"
                        MinWidth="160"
                        HorizontalAlignment="Left"
                        Placeholder="Ollama助手"
                        Text="{Binding Name, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" />
                </Grid>

                <Grid Margin="0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="80" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    <TextBlock Text="{DynamicResource Service.Url}" />

                    <common:PlaceholderTextBox
                        Grid.Column="1"
                        MinWidth="160"
                        HorizontalAlignment="Left"
                        Placeholder="http://localhost:11434"
                        Text="{Binding Url, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" />
                </Grid>

                <Grid Margin="0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="80" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    <TextBlock Text="模型" />

                    <ComboBox
                        Grid.Column="1"
                        MinWidth="160"
                        HorizontalAlignment="Left"
                        IsEditable="True"
                        ItemsSource="{Binding Models}"
                        SelectedItem="{Binding Model, Mode=TwoWay}" />
                </Grid>

                <Grid Margin="0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="80" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    <TextBlock Text="温度" />

                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <Slider
                            MinWidth="160"
                            HorizontalAlignment="Left"
                            Maximum="2"
                            Minimum="0"
                            TickFrequency="0.1"
                            TickPlacement="BottomRight"
                            Value="{Binding Temperature, Mode=TwoWay}" />
                        <TextBlock
                            Margin="10,0,0,0"
                            VerticalAlignment="Center"
                            Text="{Binding Temperature, StringFormat=F1}" />
                    </StackPanel>
                </Grid>

                <Grid Margin="0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="80" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    <TextBlock Text="系统提示词" />

                    <TextBox
                        Grid.Column="1"
                        MinWidth="300"
                        MinHeight="80"
                        HorizontalAlignment="Left"
                        AcceptsReturn="True"
                        Text="{Binding SystemPrompt, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                        TextWrapping="Wrap"
                        VerticalScrollBarVisibility="Auto" />
                </Grid>

                <Grid Margin="0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="80" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    <TextBlock Text="保持上下文" />

                    <CheckBox
                        Grid.Column="1"
                        HorizontalAlignment="Left"
                        IsChecked="{Binding KeepContext, Mode=TwoWay}" />
                </Grid>

                <Grid Margin="0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="80" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    <TextBlock Text="历史长度" />

                    <common:PlaceholderTextBox
                        Grid.Column="1"
                        MinWidth="160"
                        HorizontalAlignment="Left"
                        Placeholder="20"
                        Text="{Binding MaxHistoryLength, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" />
                </Grid>

                <StackPanel
                    Margin="0,10"
                    HorizontalAlignment="Left"
                    Orientation="Horizontal">
                    <Button
                        Margin="0,0,10,0"
                        Command="{Binding TestCommand}"
                        Content="{DynamicResource Service.Test}" />
                    <Button
                        Command="{Binding ClearChatHistoryCommand}"
                        Content="清空对话历史" />
                </StackPanel>

                <TextBlock
                    Margin="0,10"
                    Text="预设对话模板"
                    FontWeight="Bold" />

                <ItemsControl ItemsSource="{Binding UserDefinePrompts}">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Border
                                Margin="0,5"
                                Padding="10"
                                Background="{DynamicResource BackgroundBrush}"
                                BorderBrush="{DynamicResource BorderBrushColor}"
                                BorderThickness="1"
                                CornerRadius="4">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <TextBlock
                                        Grid.Column="0"
                                        Text="{Binding Name}"
                                        FontWeight="{Binding Enabled, Converter={StaticResource BooleanToFontWeightConverter}}" />
                                    <Button
                                        Grid.Column="1"
                                        Content="选择"
                                        Command="{Binding DataContext.SelectedPromptCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                        CommandParameter="{Binding}" />
                                </Grid>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>

                <TextBlock
                    Margin="0,20,0,10"
                    Text="说明：Ollama是本地AI服务，无需API密钥"
                    FontStyle="Italic"
                    Foreground="Gray" />

            </StackPanel>
        </ScrollViewer>
    </Border>
</UserControl>
