﻿<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:props="clr-namespace:STranslate.Style.Themes">

    <!--  // Separator //  -->
    <Style x:Key="SeparatorStyle" TargetType="Separator">
        <Setter Property="Height" Value="0.7" />
        <!--<Setter Property="OverridesDefaultStyle" Value="True" />-->

        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Separator">
                    <Border Height="{TemplateBinding Height}" props:ThemeProps.Background="{DynamicResource SeparatorBackground}" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>