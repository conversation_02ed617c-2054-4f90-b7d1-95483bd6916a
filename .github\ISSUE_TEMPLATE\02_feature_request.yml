name: Feature 请求
description: "为这个项目提出一个建议"
title: "[Feature request]: "
labels: ['enhancement']
body: 
- type: input
  id: problem
  attributes:
    label: 相关问题
    description: "清楚而简洁地描述问题是什么。"
    placeholder: "当我想要……时，软件不能……"
  validations:
    required: true
- type: input
  id: way-to-solve
  attributes:
    label: 描述你希望的解决方案
    description: "你希望发生什么"
  validations:
    required: true
- type: input
  id: instead
  attributes:
    label: 描述你所考虑的替代方案
  validations:
    required: false
- type: checkboxes
  id: "issues"
  attributes:
    label: "我确认已查询历史issues"
    description: |
      请先[查找历史 issues](https://github.com/ZGGSONG/STranslate/issues)、[讨论区](https://github.com/ZGGSONG/STranslate/discussions)或[官网](https://stranslate.zggsong.com)等信息，确认没有相关内容后再提交。
    options:
      - label: 是
        required: true