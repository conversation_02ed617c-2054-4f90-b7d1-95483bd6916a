﻿<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:props="clr-namespace:STranslate.Style.Themes">

    <!--  // https://www.cnblogs.com/xiaomingg/p/8886884.html //  -->
    <Style TargetType="{x:Type ProgressBar}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="Height" Value="30" />
        <Setter Property="HorizontalAlignment" Value="Center" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="Width" Value="150" />
        <Setter Property="props:ThemeProps.Background" Value="{DynamicResource ToggleBtnForeground}" />
        <Setter Property="Padding" Value="5,0" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ProgressBar}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>

                        <Grid Height="{TemplateBinding Height}">
                            <Border
                                props:ThemeProps.Background="{DynamicResource TextBoxBorderBrushColor}"
                                CornerRadius="7.5"
                                Opacity="0.05" />
                            <Border
                                props:ThemeProps.BorderBrush="{DynamicResource TextBoxBorderBrushColor}"
                                BorderThickness="1"
                                CornerRadius="7.5"
                                Opacity="0.1" />
                            <Grid Margin="{TemplateBinding BorderThickness}">
                                <Border x:Name="PART_Track" />
                                <Grid
                                    x:Name="PART_Indicator"
                                    HorizontalAlignment="Left"
                                    ClipToBounds="True">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition x:Name="width1" />
                                        <ColumnDefinition x:Name="width2" Width="0" />
                                    </Grid.ColumnDefinitions>
                                    <Grid x:Name="Animation" RenderTransformOrigin="0.5,0.5">
                                        <Grid.RenderTransform>
                                            <TransformGroup>
                                                <ScaleTransform ScaleX="1" ScaleY="-1" />
                                                <SkewTransform AngleX="0" AngleY="0" />
                                                <RotateTransform Angle="180" />
                                                <TranslateTransform />
                                            </TransformGroup>
                                        </Grid.RenderTransform>
                                        <Border Background="{TemplateBinding Background}" CornerRadius="7.5">
                                            <Viewbox
                                                Margin="{TemplateBinding Padding}"
                                                HorizontalAlignment="Left"
                                                SnapsToDevicePixels="True"
                                                StretchDirection="DownOnly">
                                                <TextBlock
                                                    VerticalAlignment="Center"
                                                    props:ThemeProps.Foreground="{DynamicResource TextForeground}"
                                                    FontSize="{TemplateBinding FontSize}"
                                                    RenderTransformOrigin="0.5,0.5"
                                                    SnapsToDevicePixels="True"
                                                    Text="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=Value, StringFormat={}{0}%}">
                                                    <TextBlock.RenderTransform>
                                                        <TransformGroup>
                                                            <ScaleTransform ScaleX="-1" ScaleY="1" />
                                                            <SkewTransform AngleX="0" AngleY="0" />
                                                            <RotateTransform Angle="0" />
                                                            <TranslateTransform />
                                                        </TransformGroup>
                                                    </TextBlock.RenderTransform>
                                                </TextBlock>
                                            </Viewbox>
                                        </Border>
                                        <Border
                                            props:ThemeProps.BorderBrush="{DynamicResource TextForeground}"
                                            BorderThickness="1"
                                            CornerRadius="7.5"
                                            Opacity="0.1" />
                                    </Grid>
                                </Grid>
                            </Grid>
                        </Grid>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Determinate" />
                                <VisualState x:Name="Indeterminate">
                                    <Storyboard RepeatBehavior="Forever">
                                        <PointAnimationUsingKeyFrames Storyboard.TargetName="Animation" Storyboard.TargetProperty="(UIElement.RenderTransformOrigin)">
                                            <EasingPointKeyFrame KeyTime="0:0:0" Value="0.5,0.5" />
                                            <EasingPointKeyFrame KeyTime="0:0:1.5" Value="1.95,0.5" />
                                            <EasingPointKeyFrame KeyTime="0:0:3" Value="0.5,0.5" />
                                        </PointAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Grid>
                    <ControlTemplate.Triggers>

                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="props:ThemeProps.Background" Value="{DynamicResource BtnDisabledBackground}" />
                        </Trigger>
                        <Trigger Property="IsIndeterminate" Value="true">
                            <Setter TargetName="width1" Property="Width" Value="0.25*" />
                            <Setter TargetName="width2" Property="Width" Value="0.725*" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>