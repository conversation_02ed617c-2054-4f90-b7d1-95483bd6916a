<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:system="clr-namespace:System;assembly=mscorlib">

    <!--#region Constant-->

    <system:String x:Key="Constant.NeweastVersionInfo">恭喜您, 当前为最新版本!</system:String>
    <system:String x:Key="Constant.Loading">加载中...</system:String>
    <system:String x:Key="Constant.Unloading">加载结束...</system:String>
    <system:String x:Key="Constant.PlaceHolderContent">Enter 发送消息/nCtrl+Enter 强制发送/nShift+Enter 换行</system:String>
    <system:String x:Key="Constant.InputErrorContent">该服务未获取到缓存Ctrl+Enter更新</system:String>
    <system:String x:Key="Constant.HistoryErrorContent">该服务对话时未正确返回Ctrl+Enter以更新</system:String>

    <!--#endregion-->

    <!--#region 其他-->

    <system:String x:Key="Confirm">确认</system:String>
    <system:String x:Key="Cancel">取消</system:String>
    <system:String x:Key="Yes">是</system:String>
    <system:String x:Key="No">否</system:String>
    <system:String x:Key="Admin">[管理员]</system:String>
    <system:String x:Key="Start">已启动...</system:String>

    <!--#endregion-->

    <!--#region Enum-->

    <system:String x:Key="LangEnum.auto">自动选择</system:String>
    <system:String x:Key="LangEnum.zh_cn">中文</system:String>
    <system:String x:Key="LangEnum.zh_tw">中文繁体</system:String>
    <system:String x:Key="LangEnum.yue">中文粤语</system:String>
    <system:String x:Key="LangEnum.en">英语</system:String>
    <system:String x:Key="LangEnum.ja">日语</system:String>
    <system:String x:Key="LangEnum.ko">韩语</system:String>
    <system:String x:Key="LangEnum.fr">法语</system:String>
    <system:String x:Key="LangEnum.es">西班牙语</system:String>
    <system:String x:Key="LangEnum.ru">俄语</system:String>
    <system:String x:Key="LangEnum.de">德语</system:String>
    <system:String x:Key="LangEnum.it">意大利语</system:String>
    <system:String x:Key="LangEnum.tr">土耳其语</system:String>
    <system:String x:Key="LangEnum.pt_pt">葡萄牙语</system:String>
    <system:String x:Key="LangEnum.pt_br">布列塔尼语</system:String>
    <system:String x:Key="LangEnum.vi">越南语</system:String>
    <system:String x:Key="LangEnum.id">印度尼西亚语</system:String>
    <system:String x:Key="LangEnum.th">泰语</system:String>
    <system:String x:Key="LangEnum.ms">马来语</system:String>
    <system:String x:Key="LangEnum.ar">阿拉伯语</system:String>
    <system:String x:Key="LangEnum.hi">印地语</system:String>
    <system:String x:Key="LangEnum.mn_cy">蒙古语(西里尔)</system:String>
    <system:String x:Key="LangEnum.mn_mo">蒙古语</system:String>
    <system:String x:Key="LangEnum.km">高棉语</system:String>
    <system:String x:Key="LangEnum.nb_no">书面挪威语</system:String>
    <system:String x:Key="LangEnum.nn_no">新挪威语</system:String>
    <system:String x:Key="LangEnum.fa">波斯语</system:String>
    <system:String x:Key="LangEnum.sv">瑞典语</system:String>
    <system:String x:Key="LangEnum.pl">波兰语</system:String>
    <system:String x:Key="LangEnum.nl">荷兰语</system:String>
    <system:String x:Key="LangEnum.uk">乌克兰语</system:String>

    <system:String x:Key="ThemeType.Light">明亮主题</system:String>
    <system:String x:Key="ThemeType.Dark">黑暗主题</system:String>
    <system:String x:Key="ThemeType.FollowSystem">跟随系统</system:String>
    <system:String x:Key="ThemeType.FollowApp">跟随软件</system:String>

    <system:String x:Key="LangDetectType.Local">本地识别</system:String>
    <system:String x:Key="LangDetectType.Baidu">百度识别</system:String>
    <system:String x:Key="LangDetectType.Tencent">腾讯识别</system:String>
    <system:String x:Key="LangDetectType.Niutrans">小牛识别</system:String>
    <system:String x:Key="LangDetectType.Bing">必应识别</system:String>
    <system:String x:Key="LangDetectType.Yandex">Yandex</system:String>
    <system:String x:Key="LangDetectType.Google">谷歌识别</system:String>
    <system:String x:Key="LangDetectType.Microsoft">微软识别</system:String>

    <system:String x:Key="LineBreakHandlingMode.None">不处理换行</system:String>
    <system:String x:Key="LineBreakHandlingMode.RemoveExtraLineBreak">移除多余换行</system:String>
    <system:String x:Key="LineBreakHandlingMode.RemoveAllLineBreak">移除所有换行</system:String>
    <system:String x:Key="LineBreakHandlingMode.RemoveAllLineBreakWithoutSpace">移除所有换行-非空格填充</system:String>

    <system:String x:Key="OcrImageQualityEnum.Low">低质量</system:String>
    <system:String x:Key="OcrImageQualityEnum.Medium">中等质量</system:String>
    <system:String x:Key="OcrImageQualityEnum.High">高质量</system:String>

    <system:String x:Key="DoubleTapFuncEnum.InputFunc">输入翻译</system:String>
    <system:String x:Key="DoubleTapFuncEnum.ScreenFunc">截图翻译</system:String>
    <system:String x:Key="DoubleTapFuncEnum.MouseHookFunc">鼠标划词</system:String>
    <system:String x:Key="DoubleTapFuncEnum.OCRFunc">文字识别</system:String>
    <system:String x:Key="DoubleTapFuncEnum.ShowViewFunc">显示界面</system:String>
    <system:String x:Key="DoubleTapFuncEnum.PreferenceFunc">偏好设置</system:String>
    <system:String x:Key="DoubleTapFuncEnum.ForbidShortcutFunc">禁用热键</system:String>
    <system:String x:Key="DoubleTapFuncEnum.ExitFunc">退出程序</system:String>

    <system:String x:Key="GlobalFontSizeEnum.ExtremelySmall">特小(14px)</system:String>
    <system:String x:Key="GlobalFontSizeEnum.UltraSmall">超小(15px)</system:String>
    <system:String x:Key="GlobalFontSizeEnum.VerySmall">很小(16px)</system:String>
    <system:String x:Key="GlobalFontSizeEnum.Small">小(17px)</system:String>
    <system:String x:Key="GlobalFontSizeEnum.General">标准(18px)</system:String>
    <system:String x:Key="GlobalFontSizeEnum.Big">大(19px)</system:String>
    <system:String x:Key="GlobalFontSizeEnum.VeryBig">很大(20px)</system:String>
    <system:String x:Key="GlobalFontSizeEnum.UltraBig">超大(21px)</system:String>
    <system:String x:Key="GlobalFontSizeEnum.ExtremelyBig">特大(22px)</system:String>

    <system:String x:Key="ProxyMethodEnum.NoProxy">不使用代理</system:String>
    <system:String x:Key="ProxyMethodEnum.SystemProxy">系统代理</system:String>
    <system:String x:Key="ProxyMethodEnum.Http">Http</system:String>
    <system:String x:Key="ProxyMethodEnum.Socks5">Socks5</system:String>

    <system:String x:Key="AnimationSpeedEnum.Slow">较慢(300ms)</system:String>
    <system:String x:Key="AnimationSpeedEnum.Middle">适中(200ms)</system:String>
    <system:String x:Key="AnimationSpeedEnum.Fast">较快(150ms)</system:String>

    <system:String x:Key="BaiduOCRAction.accurate">高精度含位置版</system:String>
    <system:String x:Key="BaiduOCRAction.accurate_basic">高精度版</system:String>
    <system:String x:Key="BaiduOCRAction.general">标准含位置版</system:String>
    <system:String x:Key="BaiduOCRAction.general_basic">标准版</system:String>

    <system:String x:Key="TencentOCRAction.GeneralBasicOCR">通用印刷体识别</system:String>
    <system:String x:Key="TencentOCRAction.GeneralAccurateOCR">通用印刷体识别(高精度版)</system:String>

    <system:String x:Key="VolcengineOCRAction.OCRNormal">通用文字识别</system:String>
    <system:String x:Key="VolcengineOCRAction.MultiLanguageOCR">多语种OCR</system:String>

    <system:String x:Key="IconType.STranslate">本地</system:String>
    <system:String x:Key="IconType.DeepL">DeepL</system:String>
    <system:String x:Key="IconType.Baidu">百度</system:String>
    <system:String x:Key="IconType.Google">谷歌</system:String>
    <system:String x:Key="IconType.Iciba">爱词霸</system:String>
    <system:String x:Key="IconType.Youdao">有道</system:String>
    <system:String x:Key="IconType.Bing">必应</system:String>
    <system:String x:Key="IconType.OpenAI">OpenAI</system:String>
    <system:String x:Key="IconType.Gemini">Gemini</system:String>
    <system:String x:Key="IconType.Tencent">腾讯</system:String>
    <system:String x:Key="IconType.Ali">阿里</system:String>
    <system:String x:Key="IconType.Niutrans">小牛</system:String>
    <system:String x:Key="IconType.Caiyun">彩云</system:String>
    <system:String x:Key="IconType.Microsoft">微软</system:String>
    <system:String x:Key="IconType.Volcengine">火山</system:String>
    <system:String x:Key="IconType.Ecdict">简明汉字词典</system:String>
    <system:String x:Key="IconType.Azure">Azure</system:String>
    <system:String x:Key="IconType.Chatglm">智谱清言</system:String>
    <system:String x:Key="IconType.Linyi">零一万物</system:String>
    <system:String x:Key="IconType.DeepSeek">DeepSeek</system:String>
    <system:String x:Key="IconType.Groq">Groq</system:String>
    <system:String x:Key="IconType.PaddleOCR">PaddleOCR</system:String>
    <system:String x:Key="IconType.BaiduBce">百度云平台</system:String>
    <system:String x:Key="IconType.TencentOCR">腾讯OCR</system:String>
    <system:String x:Key="IconType.Ollama">Ollama</system:String>
    <system:String x:Key="IconType.Kimi">Kimi</system:String>
    <system:String x:Key="IconType.Lingva">Lingva</system:String>
    <system:String x:Key="IconType.WeChat">微信</system:String>
    <system:String x:Key="IconType.Claude">Claude</system:String>
    <system:String x:Key="IconType.EuDict">欧陆词典</system:String>
    <system:String x:Key="IconType.Yandex">Yandex</system:String>
    <system:String x:Key="IconType.DeerAPI">DeerAPI</system:String>
    <system:String x:Key="IconType.Grok">Grok</system:String>
    <system:String x:Key="IconType.Bailian">阿里百炼</system:String>
    <system:String x:Key="IconType.Transmart">Transmart</system:String>
    <system:String x:Key="IconType.OpenRouter">OpenRouter</system:String>
    <system:String x:Key="IconType.Maimemo">墨墨</system:String>

    <system:String x:Key="BackupType.Local">本地</system:String>
    <system:String x:Key="BackupType.WebDav">WebDav</system:String>

    <system:String x:Key="STranslateMode.Brower">模式一</system:String>
    <system:String x:Key="STranslateMode.IOS">模式二</system:String>

    <system:String x:Key="StartModeKind.Normal">正常启动</system:String>
    <system:String x:Key="StartModeKind.Admin">管理员启动</system:String>
    <system:String x:Key="StartModeKind.SkipUACAdmin">跳过UAC管理员启动</system:String>

    <!--#endregion-->

    <!--#region Toast-->

    <system:String x:Key="Toast.Copy">复制</system:String>
    <system:String x:Key="Toast.Speak">播报</system:String>
    <system:String x:Key="Toast.Result">结果</system:String>
    <system:String x:Key="Toast.DeleteSuccess">删除成功</system:String>
    <system:String x:Key="Toast.CopySuccess">复制成功</system:String>
    <system:String x:Key="Toast.ClearSuccess">清理成功</system:String>
    <system:String x:Key="Toast.CopySnakeSuccess">蛇形复制成功</system:String>
    <system:String x:Key="Toast.CopySmallHumpSuccess">小驼峰复制成功</system:String>
    <system:String x:Key="Toast.CopyLargeHumpSuccess">大驼峰复制成功</system:String>
    <system:String x:Key="Toast.SpeakInputContent">播报输入内容</system:String>
    <system:String x:Key="Toast.AutoTransBack">自动回译</system:String>
    <system:String x:Key="Toast.Open">打开</system:String>
    <system:String x:Key="Toast.Close">关闭</system:String>
    <system:String x:Key="Toast.KeepAtListOneTranslator">至少保留一个服务</system:String>
    <system:String x:Key="Toast.NoOCR">未启用OCR服务</system:String>
    <system:String x:Key="Toast.SaveSuccess">保存成功</system:String>
    <system:String x:Key="Toast.SaveFailed">保存失败</system:String>
    <system:String x:Key="Toast.ResetConf">重置配置</system:String>
    <system:String x:Key="Toast.SaveTo">保存至</system:String>
    <system:String x:Key="Toast.Success">成功</system:String>
    <system:String x:Key="Toast.Failed">失败</system:String>
    <system:String x:Key="Toast.RemoveSpace">移除空格</system:String>
    <system:String x:Key="Toast.RemoveLinebreak">移除换行</system:String>
    <system:String x:Key="Toast.EnableSticky">启用置顶</system:String>
    <system:String x:Key="Toast.DisableSticky">关闭置顶</system:String>
    <system:String x:Key="Toast.PleaseSelectImg">请选择图片</system:String>
    <system:String x:Key="Toast.ClipboardNoImgRecently">剪贴板最近无图片</system:String>
    <system:String x:Key="Toast.NoQRCode">未识别到二维码</system:String>
    <system:String x:Key="Toast.WordSelect">鼠标划词</system:String>
    <system:String x:Key="Toast.AutoTranslate">自动翻译</system:String>
    <system:String x:Key="Toast.IncrementalTranslate">增量翻译</system:String>
    <system:String x:Key="Toast.Show">隐藏</system:String>
    <system:String x:Key="Toast.Hide">显示</system:String>
    <system:String x:Key="Toast.LangView">语言框</system:String>
    <system:String x:Key="Toast.Export">导出</system:String>
    <system:String x:Key="Toast.Import">导入</system:String>
    <system:String x:Key="Toast.Add">添加</system:String>
    <system:String x:Key="Toast.Clear">清空</system:String>
    <system:String x:Key="Toast.PleaseCheckCnfOrLog">请检查配置或查看日志</system:String>
    <system:String x:Key="Toast.DeleteFailed">删除失败</system:String>
    <system:String x:Key="Toast.DeleteAllSuccess">全部删除成功</system:String>
    <system:String x:Key="Toast.SingleCharInfo">单字符可能会影响使用</system:String>
    <system:String x:Key="Toast.DropPrompFile">请拖入Prompt文件</system:String>
    <system:String x:Key="Toast.ImportEmpty">导入内容为空</system:String>
    <system:String x:Key="Toast.NoSelectPrompt">未选择Prompt</system:String>
    <system:String x:Key="Toast.DownloadStart">开始下载</system:String>
    <system:String x:Key="Toast.DownloadComplete">下载完成</system:String>
    <system:String x:Key="Toast.DownloadCancel">取消下载</system:String>
    <system:String x:Key="Toast.DownloadException">下载时发生异常</system:String>
    <system:String x:Key="Toast.Decompress">解压数据包</system:String>
    <system:String x:Key="Toast.LoadDataSuccess">加载数据包成功</system:String>
    <system:String x:Key="Toast.DecompressFailed">解压失败,请重启再试</system:String>
    <system:String x:Key="Toast.DataIntegrity">数据完整</system:String>
    <system:String x:Key="Toast.MissingData">数据缺失</system:String>
    <system:String x:Key="Toast.DeleteFailedInfo">删除失败,请重启再试</system:String>
    <system:String x:Key="Toast.UnSupport">不支持</system:String>
    <system:String x:Key="Toast.NoPaddleOCRData">离线数据不完整,请前往PaddleOCR配置页面进行下载</system:String>
    <system:String x:Key="Toast.VerifySuccess">验证成功</system:String>
    <system:String x:Key="Toast.VerifyFailed">验证失败</system:String>
    <system:String x:Key="Toast.QuerySuccess">查询成功</system:String>
    <system:String x:Key="Toast.QueryFailed">查询失败</system:String>
    <system:String x:Key="Toast.QueryCanceled">取消查询</system:String>
    <system:String x:Key="Toast.TTSFailed">文本转语音失败</system:String>
    <system:String x:Key="Toast.NoTTS">未启用TTS服务</system:String>
    <system:String x:Key="Toast.TTSNotEnd">当前语音未结束</system:String>
    <system:String x:Key="Toast.CheckVocabularyConf">请检查生词本配置</system:String>
    <system:String x:Key="Toast.RenameFailed">修改名称失败</system:String>

    <!--#endregion-->

    <!--#region 首选项-->

    <system:String x:Key="Preference.WindowTitleInTaskbar">STranslate 首选项</system:String>
    <system:String x:Key="Preference.WindowTitle">首选项</system:String>
    <system:String x:Key="Preference.Navi.Common">常规设置</system:String>
    <system:String x:Key="Preference.Navi.Hotkey">热键设置</system:String>
    <system:String x:Key="Preference.Navi.Service">服务设置</system:String>
    <system:String x:Key="Preference.Navi.Replace">替换翻译</system:String>
    <system:String x:Key="Preference.Navi.History">历史记录</system:String>
    <system:String x:Key="Preference.Navi.Backup">导入导出</system:String>
    <system:String x:Key="Preference.Navi.About">关于软件</system:String>

    <system:String x:Key="Preference.Reset">撤销</system:String>
    <system:String x:Key="Preference.Save">保存</system:String>

    <!--#endregion-->

    <!--#region 设置相关-->

    <!--#region Common 设置项-->
    <system:String x:Key="Common.Title">常规配置</system:String>
    <system:String x:Key="Common.AutoStart">开机自启动</system:String>
    <system:String x:Key="Common.AppLanguage">软件语言</system:String>
    <system:String x:Key="Common.StartMode">启动方式</system:String>
    <system:String x:Key="Common.AutoCheckUpdate">自动检查更新</system:String>
    <system:String x:Key="Common.DownloadProxy">资源下载渠道</system:String>
    <system:String x:Key="Common.AutoTranslate">自动对话</system:String>
    <system:String x:Key="Common.ThemeSelection">主题选择</system:String>
    <system:String x:Key="Common.FontSelection">字体选择[立即生效]</system:String>
    <system:String x:Key="Common.GlobalFontSize">全局字体大小[立即生效]</system:String>
    <system:String x:Key="Common.DisableGlobalHotkeys">禁用全局热键</system:String>
    <system:String x:Key="Common.IgnoreHotkeysOnFullscreen">全屏模式下忽略热键</system:String>
    <system:String x:Key="Common.OftenUsedLang">常用语言</system:String>
    <system:String x:Key="Common.OftenUsedLangSetButton">点击设置</system:String>
    <system:String x:Key="Common.LangDetect">语种识别</system:String>
    <system:String x:Key="Common.LangDetectRatio">语种识别比例(zh-en)</system:String>
    <system:String x:Key="Common.WordPickingInterval">取词时间间隔</system:String>
    <system:String x:Key="Common.DoubleTapTrayFunc">双击托盘功能</system:String>
    <system:String x:Key="Common.HistorySize">历史记录数量</system:String>
    <system:String x:Key="Common.HistorySize.50">50 条</system:String>
    <system:String x:Key="Common.HistorySize.100">100 条</system:String>
    <system:String x:Key="Common.HistorySize.200">200 条</system:String>
    <system:String x:Key="Common.HistorySize.500">500 条</system:String>
    <system:String x:Key="Common.HistorySize.1000">1000 条</system:String>
    <system:String x:Key="Common.HistorySize.Unlimited">无限制</system:String>
    <system:String x:Key="Common.HistorySize.None">不保存</system:String>
    <system:String x:Key="Common.SilentOcrLang">静默OCR/截图翻译语种</system:String>
    <system:String x:Key="Common.SourceLangIfAuto">语种识别为自动时使用</system:String>
    <system:String x:Key="Common.TargetLangIfSourceZh">目标语种为自动且原始语种为中文时使用</system:String>
    <system:String x:Key="Common.TargetLangIfSourceNotZh">目标语种为自动且原始语种为非中文时使用</system:String>
    <system:String x:Key="Common.HttpTimeout">请求超时时间(秒)</system:String>

    <!--  网络配置  -->
    <system:String x:Key="Network.Title">网络配置</system:String>
    <system:String x:Key="Network.ExternalCall">外部调用服务(非代理)</system:String>
    <system:String x:Key="Network.ExternalCallPort">外部调用端口</system:String>
    <system:String x:Key="Network.ProxySettings">代理设置</system:String>
    <system:String x:Key="Network.Server">服务器</system:String>
    <system:String x:Key="Network.Port">端口</system:String>
    <system:String x:Key="Network.ProxyAuth">代理服务器验证</system:String>
    <system:String x:Key="Network.Username">用户名</system:String>
    <system:String x:Key="Network.Password">密码</system:String>
    <system:String x:Key="Network.ServerPlaceholder">请输入IP地址</system:String>
    <system:String x:Key="Network.PortPlaceholder">请输入端口号</system:String>
    <system:String x:Key="Network.UsernamePlaceholder">请输入用户名</system:String>
    <system:String x:Key="Network.PasswordPlaceholder">请输入密码</system:String>

    <!--  功能配置  -->
    <system:String x:Key="Function.Title">功能配置</system:String>
    <system:String x:Key="Function.AdjustContentTranslate">主界面调整内容后立即翻译</system:String>
    <system:String x:Key="Function.ChangedLang2Execute">主界面修改语种后立即翻译</system:String>
    <system:String x:Key="Function.UsePasteOutput">替换翻译使用剪贴板输出</system:String>
    <system:String x:Key="Function.UseFormsCopy">剪贴板占用问题</system:String>
    <system:String x:Key="Function.ScreenshotOcrAutoCopyText">截图翻译识别文本后自动复制</system:String>
    <system:String x:Key="Function.LineBreakHandler">取词换行处理</system:String>
    <system:String x:Key="Function.LineBreakOCRHandler">OCR取词换行处理</system:String>
    <system:String x:Key="Function.OcrAutoCopyText">OCR后自动复制</system:String>
    <system:String x:Key="Function.OcrChangedLang2Execute">OCR修改服务或语种后立即识别</system:String>
    <system:String x:Key="Function.IncrementalTranslation">增量翻译</system:String>
    <system:String x:Key="Function.CopyResultAfterTranslate">翻译后自动复制</system:String>
    <system:String x:Key="Function.CopyResultAfterTranslate.NoAction">无操作</system:String>
    <system:String x:Key="Function.CopyResultAfterTranslate.First">第一个</system:String>
    <system:String x:Key="Function.CopyResultAfterTranslate.Second">第二个</system:String>
    <system:String x:Key="Function.CopyResultAfterTranslate.Third">第三个</system:String>
    <system:String x:Key="Function.CopyResultAfterTranslate.Fourth">第四个</system:String>
    <system:String x:Key="Function.CopyResultAfterTranslate.Fifth">第五个</system:String>
    <system:String x:Key="Function.CopyResultAfterTranslate.Sixth">第六个</system:String>
    <system:String x:Key="Function.CopyResultAfterTranslate.Seventh">第七个</system:String>
    <system:String x:Key="Function.CopyResultAfterTranslate.Eighth">第八个</system:String>
    <system:String x:Key="Function.CopyResultAfterTranslate.Last">最后一个</system:String>
    <system:String x:Key="Function.HotkeyCopySuccessToast">热键触发复制成功提示</system:String>
    <system:String x:Key="Function.OcrImageQuality">文字识别图片质量</system:String>

    <!--  显示配置  -->
    <system:String x:Key="Display.Title">显示配置</system:String>
    <system:String x:Key="Display.HideOnStart">启动时隐藏主界面</system:String>
    <system:String x:Key="Display.DisableNoticeOnStart">启动时不显示通知</system:String>
    <system:String x:Key="Display.UseCacheLocation">启动时使用缓存位置</system:String>
    <system:String x:Key="Display.AnimationSpeed">动画速度</system:String>
    <system:String x:Key="Display.ShowMainPlaceholder">输入框水印</system:String>
    <system:String x:Key="Display.OnlyShowRet">主界面仅显示输出界面</system:String>
    <system:String x:Key="Display.HideLangWhenOnlyShowOutput">主界面仅显示输出界面时隐藏语言框</system:String>
    <system:String x:Key="Display.TriggerShowHide">显示界面重复触发显/隐</system:String>
    <system:String x:Key="Display.MainViewMaxHeight">主界面最大高度[立即生效]</system:String>
    <system:String x:Key="Display.MainViewWidth">主界面宽度[立即生效]</system:String>
    <system:String x:Key="Display.TitleMaxWidth">输出界面服务名称最大宽度</system:String>
    <system:String x:Key="Display.PromptMaxWidth">输出界面提示词名称最大宽度</system:String>
    <system:String x:Key="Display.InputViewMaxHeight">输入框最大高度[立即生效]</system:String>
    <system:String x:Key="Display.FollowMouse">窗口跟随鼠标</system:String>
    <system:String x:Key="Display.ShowAuxiliaryLine">截图辅助线</system:String>
    <system:String x:Key="Display.MainViewShadow">主窗口阴影(比较占用性能)</system:String>
    <system:String x:Key="Display.PromptToggleVisible">主界面显示 Prompt 切换</system:String>
    <system:String x:Key="Display.CloseUIOcrRetTranslate">OCR点击翻译后关闭界面</system:String>
    <system:String x:Key="Display.KeepTopmostAfterMousehook">退出鼠标划词功能时保持置顶</system:String>
    <system:String x:Key="Display.ShowCopyOnHeader">翻译结果收缩框是否显示按钮</system:String>
    <system:String x:Key="Display.CaretLast">显示主窗口时光标移动至末尾[立即生效]</system:String>

    <!--  图标配置  -->
    <system:String x:Key="Icon.Title">图标配置</system:String>
    <system:String x:Key="Icon.ShowSnakeCopyBtn">显示蛇形结果复制按钮</system:String>
    <system:String x:Key="Icon.ShowSmallHumpCopyBtn">显示小驼峰结果复制按钮</system:String>
    <system:String x:Key="Icon.ShowLargeHumpCopyBtn">显示大驼峰结果复制按钮</system:String>
    <system:String x:Key="Icon.ShowTranslateBackBtn">显示回译按钮</system:String>
    <system:String x:Key="Icon.ShowClose">显示关闭图标</system:String>
    <system:String x:Key="Icon.StayMainViewWhenLoseFocus">丢失焦点时主界面不隐藏</system:String>
    <system:String x:Key="Icon.ShowMinimalBtn">显示最小化图标</system:String>
    <system:String x:Key="Icon.ShowPreference">显示设置图标</system:String>
    <system:String x:Key="Icon.ShowConfigureService">显示服务配置图标</system:String>
    <system:String x:Key="Icon.ShowMainOcrLang">显示静默OCR/截图翻译语种选择图标</system:String>
    <system:String x:Key="Icon.ShowMousehook">显示鼠标划词图标</system:String>
    <system:String x:Key="Icon.ShowAutoTranslate">显示自动翻译图标</system:String>
    <system:String x:Key="Icon.ShowIncrementalTranslation">显示增量翻译图标</system:String>
    <system:String x:Key="Icon.ShowOnlyShowRet">显示仅显示输出界面图标</system:String>
    <system:String x:Key="Icon.ShowTopmost">显示置顶图标</system:String>
    <system:String x:Key="Icon.ShowScreenshot">显示截图翻译图标</system:String>
    <system:String x:Key="Icon.ShowOCR">显示OCR图标</system:String>
    <system:String x:Key="Icon.ShowSilentOCR">显示静默OCR图标</system:String>
    <system:String x:Key="Icon.ShowQRCode">显示二维码识别图标</system:String>
    <system:String x:Key="Icon.ShowHistory">显示历史记录图标</system:String>
    <system:String x:Key="Icon.ShowClipboardMonitor">显示剪贴板监听图标</system:String>
    <system:String x:Key="Icon.ShowReplaceTranslate">显示替换翻译图标</system:String>
    <system:String x:Key="Icon.ShowTTS">显示TTS图标</system:String>
    <system:String x:Key="Icon.ShowCopy">显示复制图标</system:String>
    <system:String x:Key="Icon.ShowCopySource">显示复制原文图标</system:String>
    <system:String x:Key="Icon.ShowCopyAll">显示复制全部图标</system:String>
    <system:String x:Key="Icon.ShowCopyAllSource">显示复制全部原文图标</system:String>
    <system:String x:Key="Icon.ShowCopyAllResult">显示复制全部结果图标</system:String>
    <system:String x:Key="Icon.ShowCopyAllSourceResult">显示复制全部原文结果图标</system:String>
    <system:String x:Key="Icon.ShowCopyAllSourceAndResult">显示复制全部原文和结果图标</system:String>
    <system:String x:Key="Icon.ShowCopyAllSourceWithResult">显示复制全部原文与结果图标</system:String>
    <system:String x:Key="Icon.ShowCopyAllResultWithSource">显示复制全部结果与原文图标</system:String>

    <!--  工具提示  -->
    <system:String x:Key="Tooltip.AutoStartTip">开机时自动启动软件(该用户登录)</system:String>
    <system:String x:Key="Tooltip.StartModeTip">选择是否以管理员身份运行，并可设置是否跳过 UAC 提示</system:String>
    <system:String x:Key="Tooltip.AutoCheckUpdateTip">开启后自动检查更新，每隔24h检查一次更新</system:String>
    <system:String x:Key="Tooltip.DownloadProxyTip">下载Github资源(软件安装包、离线资源包)所使用的代理方式</system:String>
    <system:String x:Key="Tooltip.AppLanguageTip">切换软件语言版本</system:String>
    <system:String x:Key="Tooltip.AutoTranslateTip">输入内容以后自动发送给AI助手</system:String>
    <system:String x:Key="Tooltip.ThemeSelectionTip">亮色主题、暗色主题、跟随系统Windows模式、跟随系统应用模式</system:String>
    <system:String x:Key="Tooltip.FontSelectionTip">选择系统中自带字体</system:String>
    <system:String x:Key="Tooltip.GlobalFontSizeTip">全局字体大小配置</system:String>
    <system:String x:Key="Tooltip.DisableGlobalHotkeysTip">是否禁用全局热键</system:String>
    <system:String x:Key="Tooltip.IgnoreHotkeysOnFullscreenTip">鼠标所处显示器应用全屏时禁用全局热键（建议游戏时打开）</system:String>
    <system:String x:Key="Tooltip.OftenUsedLangTip">配置选择语言时显示的语种列表</system:String>
    <system:String x:Key="Tooltip.LangDetectTip">语种识别方式</system:String>
    <system:String x:Key="Tooltip.LangDetectRatioTip">仅针对 本地识别 中英文情况下英文字符占总字符数的比例</system:String>
    <system:String x:Key="Tooltip.WordPickingIntervalTip">针对部分软件取词失败的情况，延长取词时间间隔</system:String>
    <system:String x:Key="Tooltip.DoubleTapTrayFuncTip">鼠标左键双击头盘程序功能选择</system:String>
    <system:String x:Key="Tooltip.HistorySizeTip">历史记录最大数量设置</system:String>
    <system:String x:Key="Tooltip.SilentOcrLangTip">静默OCR/截图翻译时OCR的语种&#13;主界面可配置图标快捷切换</system:String>
    <system:String x:Key="Tooltip.SourceLangIfAutoTip">如果识别语种结果仍然为自动则使用该项指定语种&#13;仅当在线识别服务返回出错情况才会触发</system:String>
    <system:String x:Key="Tooltip.TargetLangIfSourceZhTip">如果目标语种为自动且原始语种为中文时则使用该项指定语种</system:String>
    <system:String x:Key="Tooltip.TargetLangIfSourceNotZhTip">如果目标语种为自动且原始语种为非中文时则使用该项指定语种</system:String>
    <system:String x:Key="Tooltip.HttpTimeoutTip">服务请求的超时时间，单位为秒</system:String>
    <system:String x:Key="Tooltip.ExternalCallTip">是否启用外部调用服务</system:String>
    <system:String x:Key="Tooltip.ExternalCallPortTip">配置服务端口进行外部调用</system:String>
    <system:String x:Key="Tooltip.ProxySettingsTip">配置网络代理方式&#13;本地地址直连</system:String>
    <system:String x:Key="Tooltip.ServerTip">代理服务器IP地址</system:String>
    <system:String x:Key="Tooltip.PortTip">代理服务器端口</system:String>
    <system:String x:Key="Tooltip.ProxyAuthTip">代理服务器需要身份验证</system:String>
    <system:String x:Key="Tooltip.UsernameTip">代理服务器用户名</system:String>
    <system:String x:Key="Tooltip.PasswordTip">代理服务器密码</system:String>
    <system:String x:Key="Tooltip.AdjustContentTranslateTip">主界面调整内容后是否立即翻译</system:String>
    <system:String x:Key="Tooltip.ChangedLang2ExecuteTip">主界面修改选择语言后立即翻译</system:String>
    <system:String x:Key="Tooltip.UsePasteOutputTip">替换翻译输出结果不完整时，可尝试启用此配置替代模拟键盘输出</system:String>
    <system:String x:Key="Tooltip.UseFormsCopyTip">如果出现剪贴板被占用了，可尝试启用此配置</system:String>
    <system:String x:Key="Tooltip.ScreenshotOcrAutoCopyTextTip">截图翻译识别文本后自动复制文本内容</system:String>
    <system:String x:Key="Tooltip.LineBreakHandlerTip">包括划词、截图翻译、剪贴板、静默OCR取词场景的换行处理</system:String>
    <system:String x:Key="Tooltip.LineBreakOCRHandlerTip">针对OCR界面的换行处理</system:String>
    <system:String x:Key="Tooltip.OcrAutoCopyTextTip">OCR界面识别文本后自动复制文本内容</system:String>
    <system:String x:Key="Tooltip.OcrChangedLang2ExecuteTip">OCR页面选择服务或选择语言后立即翻译</system:String>
    <system:String x:Key="Tooltip.IncrementalTranslationTip">开启后增量添加文本进行翻译</system:String>
    <system:String x:Key="Tooltip.CopyResultAfterTranslateTip">配置翻译结束后执行第几个翻译结果的复制操作</system:String>
    <system:String x:Key="Tooltip.HotkeyCopySuccessToastTip">热键触发复制成功提示是否显示&#13;如果开启翻译后自动复制后弹框比较多可以关闭此项</system:String>
    <system:String x:Key="Tooltip.OcrImageQualityTip">文字识别时图片质量选择</system:String>
    <system:String x:Key="Tooltip.HideOnStartTip">启动时不显示主界面</system:String>
    <system:String x:Key="Tooltip.DisableNoticeOnStartTip">启动时不显示通知</system:String>
    <system:String x:Key="Tooltip.UseCacheLocationTip">启动软件时是否使用上次打开位置</system:String>
    <system:String x:Key="Tooltip.AnimationSpeedTip">主界面显示时的动画速度</system:String>
    <system:String x:Key="Tooltip.ShowMainPlaceholderTip">是否显示主界面输入框水印提示</system:String>
    <system:String x:Key="Tooltip.OnlyShowRetTip">主界面是否只显示输出结果部分</system:String>
    <system:String x:Key="Tooltip.HideLangWhenOnlyShowOutputTip">主界面仅显示输出结果时是否时隐藏语言界面</system:String>
    <system:String x:Key="Tooltip.TriggerShowHideTip">重复触发显示界面功能为显示(隐藏状态下)/隐藏(显示状态下)</system:String>
    <system:String x:Key="Tooltip.MainViewMaxHeightTip">主界面最大高度调整</system:String>
    <system:String x:Key="Tooltip.MainViewWidthTip">主界面宽度调整</system:String>
    <system:String x:Key="Tooltip.TitleMaxWidthTip">输出界面服务名称最大宽度调整</system:String>
    <system:String x:Key="Tooltip.PromptMaxWidthTip">输出界面提示词名称最大宽度调整</system:String>
    <system:String x:Key="Tooltip.InputViewMaxHeightTip">输入框最大高度调整</system:String>
    <system:String x:Key="Tooltip.FollowMouseTip">输入、截图、划词和监听剪贴板进行翻译时&#13;主窗口弹出位置跟随鼠标位置</system:String>
    <system:String x:Key="Tooltip.ShowAuxiliaryLineTip">截图时是否显示辅助线</system:String>
    <system:String x:Key="Tooltip.MainViewShadowTip">主窗口是否显示显示阴影&#13;比较占用性能请自行选择</system:String>
    <system:String x:Key="Tooltip.PromptToggleVisibleTip">主界面输出UI是否显示 Prompt 切换按钮</system:String>
    <system:String x:Key="Tooltip.CloseUIOcrRetTranslateTip">OCR界面点击翻译按钮后进行翻译的同时关闭OCR窗口</system:String>
    <system:String x:Key="Tooltip.KeepTopmostAfterMousehookTip">关闭鼠标划词功能后保持窗口置顶</system:String>
    <system:String x:Key="Tooltip.ShowCopyOnHeaderTip">翻译结果收缩框收缩时上方是否显示TTS、复制按钮</system:String>
    <system:String x:Key="Tooltip.CaretLastTip">激活窗口时光标移动至末尾</system:String>
    <system:String x:Key="Tooltip.ShowSnakeCopyBtnTip">主界面输出UI是否显示复制蛇形结果按钮</system:String>
    <system:String x:Key="Tooltip.ShowSmallHumpCopyBtnTip">主界面输出UI是否显示复制小驼峰结果按钮</system:String>
    <system:String x:Key="Tooltip.ShowLargeHumpCopyBtnTip">主界面输出UI是否显示复制大驼峰结果按钮</system:String>
    <system:String x:Key="Tooltip.ShowTranslateBackBtnTip">主界面输出UI是否显示回译按钮</system:String>
    <system:String x:Key="Tooltip.ShowCloseTip">主界面显示关闭图标</system:String>
    <system:String x:Key="Tooltip.StayMainViewWhenLoseFocusTip">丢失焦点时主界面不隐藏</system:String>
    <system:String x:Key="Tooltip.ShowMinimalBtnTip">主界面显示最小化图标, 丢失焦点时主界面不隐藏选项</system:String>
    <system:String x:Key="Tooltip.ShowPreferenceTip">主界面显示设置图标</system:String>
    <system:String x:Key="Tooltip.ShowConfigureServiceTip">主界面显示服务配置图标</system:String>
    <system:String x:Key="Tooltip.ShowMainOcrLangTip">静默OCR/截图翻译语种选择图标</system:String>
    <system:String x:Key="Tooltip.ShowMousehookTip">主界面显示鼠标划词图标</system:String>
    <system:String x:Key="Tooltip.ShowAutoTranslateTip">主界面显示自动翻译图标</system:String>
    <system:String x:Key="Tooltip.ShowIncrementalTranslationTip">主界面显示增量翻译图标</system:String>
    <system:String x:Key="Tooltip.ShowOnlyShowRetTip">主界面显示仅显示输出界面图标</system:String>
    <system:String x:Key="Tooltip.ShowScreenshotTip">主界面显示截图翻译图标</system:String>
    <system:String x:Key="Tooltip.ShowOCRTip">主界面显示OCR图标</system:String>
    <system:String x:Key="Tooltip.ShowSilentOCRTip">主界面显示静默OCR图标</system:String>
    <system:String x:Key="Tooltip.ShowClipboardMonitorTip">主界面显示监听剪贴板图标</system:String>
    <system:String x:Key="Tooltip.ShowQRCodeTip">主界面显示识别二维码图标</system:String>
    <system:String x:Key="Tooltip.ShowHistoryTip">主界面显示历史记录图标</system:String>

    <!--#endregion-->

    <!--#region Hotkey 设置项-->

    <system:String x:Key="Hotkey.Title">热键配置</system:String>
    <system:String x:Key="Hotkey.GlobalHotkey">全局热键</system:String>
    <system:String x:Key="Hotkey.SoftHotkey">软件热键</system:String>
    <system:String x:Key="Hotkey.InputTranslate">输入翻译</system:String>
    <system:String x:Key="Hotkey.CrosswordTranslate">划词翻译</system:String>
    <system:String x:Key="Hotkey.ScreenshotTranslate">截图翻译</system:String>
    <system:String x:Key="Hotkey.ReplaceTranslate">替换翻译</system:String>
    <system:String x:Key="Hotkey.ShowInterface">显示界面</system:String>
    <system:String x:Key="Hotkey.MouseCrossword">鼠标划词</system:String>
    <system:String x:Key="Hotkey.OCR">文字识别</system:String>
    <system:String x:Key="Hotkey.SilentOCR">静默OCR</system:String>
    <system:String x:Key="Hotkey.ClipboardMonitor">监听剪贴板</system:String>
    <system:String x:Key="Hotkey.SilentTTS">静默TTS</system:String>
    <system:String x:Key="Hotkey.SaveConfig">保存配置</system:String>
    <system:String x:Key="Hotkey.CancelModify">撤销修改</system:String>
    <system:String x:Key="Hotkey.HotkeyConflict">热键冲突</system:String>
    <system:String x:Key="Hotkey.ShortcutKey">快捷键</system:String>
    <system:String x:Key="Hotkey.Function">功能</system:String>

    <!--  软件热键  -->
    <system:String x:Key="Hotkey.SoftHotkey.ESC">隐藏/退出界面(若有请求则同时取消该请求: 翻译、OCR、TTS)</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.OpenSettings">打开设置</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.OpenHistory">打开历史记录</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.ToggleAutoTranslate">打开/关闭自动翻译</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.ToggleIncrementalTranslation">打开/关闭增量翻译</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.ToggleInputBox">隐藏/显示输入框</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.ResetWindowPosition">重置窗口至主显示器中央</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.ToggleTopmost">置顶/取消置顶</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.SwitchTheme">切换主题(自动/明亮/黑暗主题切换)</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.SaveToVocabulary">保存至生词本</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.ExitProgram">退出程序</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.ZoomInText">在输入输出文本框配合Ctrl滚动放大文字</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.ZoomOutText">在输入输出文本框配合Ctrl滚动缩小文字</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.ResetTextSize">文本框恢复默认文字大小</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.CopyTranslationByService">按服务顺序复制翻译结果</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.CopyLastTranslation">复制最后一条服务翻译结果</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.TTSInput">语音播报输入内容</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.TTSByService">按服务顺序语音播报翻译结果</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.TTSLastTranslation">语音播报最后一条服务翻译结果</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.ZoomInUI">放大界面(宽度、最大高度)</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.ZoomOutUI">缩小界面(宽度、最大高度)</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.ResetUI">恢复默认界面(宽度、最大高度)</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.IncreaseWidth">宽度增加</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.DecreaseWidth">宽度减少</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.IncreaseMaxHeight">最大高度增加</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.DecreaseMaxHeight">最大高度减少</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.DecreaseFontSize">全局字体减小</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.IncreaseFontSize">全局字体增大</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.ResetFontSize">全局字体恢复默认</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.ToggleLineBreakMode">切换换行处理模式</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.ToggleBackTranslation">打开/关闭回译功能</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.Key.CtrlWithMouseWheelUp">Ctrl + 滚轮上</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.Key.CtrlWithMouseWheelDown">Ctrl + 滚轮下</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.Key.CtrlWithReturn">Ctrl + 点击换行图标</system:String>
    <system:String x:Key="Hotkey.SoftHotkey.Key.CtrlWithTransBack">Ctrl + 点击回译图标</system:String>

    <!--#endregion-->

    <!--#region Service 设置项-->

    <system:String x:Key="Service.Navi.Translator">文本翻译</system:String>
    <system:String x:Key="Service.Navi.OCR">文本识别</system:String>
    <system:String x:Key="Service.Navi.TTS">语音合成</system:String>
    <system:String x:Key="Service.Navi.VocabularyBook">生词本</system:String>
    <system:String x:Key="Service.Translator.Title">文本翻译</system:String>
    <system:String x:Key="Service.Translator.SubTitle">翻译功能的核心服务支持配置，下方开启的服务将被使用。</system:String>
    <system:String x:Key="Service.Translator.Duplicate">创建副本</system:String>
    <system:String x:Key="Service.Translator.Delete">删除服务</system:String>
    <system:String x:Key="Service.OCR.Title">文本识别</system:String>
    <system:String x:Key="Service.OCR.SubTitle">文字识别服务，添加你的OCR服务并选择使用</system:String>
    <system:String x:Key="Service.TTS.Title">文本识别</system:String>
    <system:String x:Key="Service.TTS.SubTitle">文本转语音服务，添加你的TTS服务并选择使用</system:String>
    <system:String x:Key="Service.VocabularyBook.Title">生词本</system:String>
    <system:String x:Key="Service.VocabularyBook.SubTitle">将文本存储至在线生词本中</system:String>
    <system:String x:Key="Service.Name">名称:</system:String>
    <system:String x:Key="Service.Type">类型:</system:String>
    <system:String x:Key="Service.Api">接口:</system:String>
    <system:String x:Key="Service.AutoExecute">自动执行:</system:String>
    <system:String x:Key="Service.AutoExecute.Tooltip">执行翻译时是否自动翻译，否则需要手动点击才能进行翻译</system:String>
    <system:String x:Key="Service.AutoTransBack">自动回译:</system:String>
    <system:String x:Key="Service.AutoTransBack.Tooltip">执行翻译时是否自动执行回译，否则需要手动点击才能进行回译</system:String>
    <system:String x:Key="Service.LinkTest">连通测试:</system:String>
    <system:String x:Key="Service.Verify">验证</system:String>
    <system:String x:Key="Service.Icon">图标</system:String>
    <system:String x:Key="Service.Terms">术语:</system:String>
    <system:String x:Key="Service.Domains">领域提示:</system:String>
    <system:String x:Key="Service.QwenMT.Domains">领域相关信息</system:String>
    <system:String x:Key="Service.OpenInBrower">在浏览器中打开</system:String>
    <system:String x:Key="Service.EnterOfficialWebsite">进入官网</system:String>
    <system:String x:Key="Service.Version">版本:</system:String>
    <system:String x:Key="Service.Update">更新</system:String>
    <system:String x:Key="Service.Duplicate">副本</system:String>
    <system:String x:Key="Service.Delete">删除</system:String>
    <system:String x:Key="Service.Model">模型:</system:String>
    <system:String x:Key="Service.Prompt.Info">通过自定义Prompt自定义LLM行为, $target 将会被替换为 目标语言</system:String>
    <system:String x:Key="Service.Translator.Prompt.Info">通过自定义Prompt自定义LLM行为, $source $target $content 将会被替换为 原始语言 目标语言 待翻译文本。</system:String>
    <system:String x:Key="Service.Thinking">思考:</system:String>
    <system:String x:Key="Service.Thinking.Tooltip">仅 GLM-4.5 及以上模型支持此参数配置. 控制大模型是否开启思维链</system:String>
    <system:String x:Key="Service.Temperature">温度:</system:String>
    <system:String x:Key="Service.Temperature.Tooltip">语言模型的上下文中，是控制模型生成文本多样性的一个参数。模型在做出下一个词预测时的确定性和随机性程度</system:String>
    <system:String x:Key="Service.OCR.GeminiApi">接口API地址不建议填写Path部分，除非你很清楚你在干什么&#13;如OpenAI接口地址为 https://api.openai.com/v1/chat/completions 其中 Path 为 /v1/chat/completions</system:String>
    <system:String x:Key="Service.OCR.GoogleLocation">词语位置:</system:String>
    <system:String x:Key="Service.OCR.GoogleLocation.Tooltip">获取结果时是否定位到每个词语的位置</system:String>
    <system:String x:Key="Service.OpenAI.Info1">接口API地址不填写Path内容则自动填充 /v1/chat/completions</system:String>
    <system:String x:Key="Service.OpenAI.Info2">如OpenAI接口地址为 https://api.openai.com/v1/chat/completions 其中 Path 为 /v1/chat/completions</system:String>
    <system:String x:Key="Service.OpenAI.Info3">若仍然存疑请点击此链接</system:String>
    <system:String x:Key="Service.OpenRouter.Info1">接口API地址不填写Path内容则自动填充 /api/v1/chat/completions</system:String>
    <system:String x:Key="Service.OpenRouter.Info2">如OpenRouter接口地址为 https://openrouter.ai/api/v1/chat/completions 其中 Path 为 /api/v1/chat/completions</system:String>
    <system:String x:Key="Service.OCR.PaddleOCR.Data">数据包:</system:String>
    <system:String x:Key="Service.OCR.PaddleOCR.Download">下载数据包</system:String>
    <system:String x:Key="Service.OCR.PaddleOCR.Check">检查数据包</system:String>
    <system:String x:Key="Service.OCR.PaddleOCR.CPUNotSupported">CPU架构不支持</system:String>
    <system:String x:Key="Service.OCR.PaddleOCR.InComplete">离线数据不完整,请前往PaddleOCR配置页面进行下载</system:String>
    <system:String x:Key="Service.OCR.Tencent.Version.Tooltip">高精度版不支持语种选择</system:String>
    <system:String x:Key="Service.OCR.WechatOCR.Path">路径:</system:String>
    <system:String x:Key="Service.OCR.WechatOCR.Path.Info">路径默认可以不带版本号，软件会自动寻找注册表中微信写入的版本号信息，方便微信升级无需更新也能正常使用。但有时使用scoop等方式安装的微信不会及时更新注册表中版本号，所以遇到无法找到微信路径的请填写带有版本号如 'D:\scoop\WeChat\[*********]' 的完整路径</system:String>
    <system:String x:Key="Service.OCR.WechatOCR.Dll.Info">该服务调用时会自动加载上面填入微信路径中的mmmojo.dll和mmmojo_64.dll动态链接库以实现功能调用，如果遇到微信升级后无法使用可尝试清理后再试</system:String>
    <system:String x:Key="Service.Clear">清理</system:String>
    <system:String x:Key="Service.ClearDll">清理依赖库</system:String>
    <system:String x:Key="Service.Translator.ChatGLM">接口API地址不填写Path内容则自动填充 /api/paas/v4/chat/completions&#13;如OpenAI接口地址为 https://api.openai.com/v1/chat/completions 其中 Path 为 /v1/chat/completions</system:String>
    <system:String x:Key="Service.Translator.Claude">接口API地址不填写Path内容则自动填充 /v1/messages&#13;如OpenAI接口地址为 https://api.openai.com/v1/chat/completions 其中 Path 为 /v1/chat/completions</system:String>
    <system:String x:Key="Service.Translator.DeepL.UsageSearch">余额查询:</system:String>
    <system:String x:Key="Service.Translator.DeepL.Search">查询</system:String>
    <system:String x:Key="Service.Translator.DeepLX.Token">没有则无需填写</system:String>
    <system:String x:Key="Service.Translator.ECDict.Home">主页</system:String>
    <system:String x:Key="Service.Translator.ECDict.ProjectHome">项目主页</system:String>
    <system:String x:Key="Service.Translator.ECDict.Description">仅支持英语单词，不支持句子翻译</system:String>
    <system:String x:Key="Service.Translator.ECDict.Tip">本地服务，需下载词典文件</system:String>
    <system:String x:Key="Service.Description">说明</system:String>
    <system:String x:Key="Service.Translator.STranslate.Mode">模式:</system:String>
    <system:String x:Key="Service.Translator.STranslate.Mode.Tooltip">如果某种模式翻译失败，请切换其余模式，短时间大量访问服务方可能会限制</system:String>
    <system:String x:Key="Service.Translator.STranslate.Tip">本地服务，无需配置</system:String>
    <system:String x:Key="Service.STranslate">本地服务</system:String>
    <system:String x:Key="Service.Translator.Tencent.ProjectID">项目ID:</system:String>
    <system:String x:Key="Service.Translator.Tencent.Region">地区:</system:String>
    <system:String x:Key="Service.TTS.Voice">声音:</system:String>
    <system:String x:Key="Service.TTS.Lingva">该服务依赖语种，请在常规设置-常用配置-语种识别中选择可用服务，有时可能因为网络问题或服务问题而无法正常识别(需要注意的是本地识别目前仅支持中英文，请避免使用本地识别方案配合Lingva进行除中英文外的文本转语音操作)</system:String>
    <system:String x:Key="Service.TTS.Speed">语速:</system:String>
    <system:String x:Key="Service.TTS.Speed.Tooltip">设置离线TTS的语速。</system:String>
    <system:String x:Key="Service.TTS.Offline">获取系统支持的语音</system:String>
    <system:String x:Key="Service.VocabularyBook.Maimemo">实测如果添加例如 "stranslate" 的单词，接口正常回复成功，但是并不会出现在其单词列表，请熟悉</system:String>
    <system:String x:Key="Service.DescriptionTitle">描述:</system:String>
    <system:String x:Key="Service.DeerAPI.Description">AI聚合平台，一键调用500+模型，7折特惠，最新GPT4o、Grok 3、Gemini 2.5pro全支持！</system:String>
    <system:String x:Key="Service.DeerAPI.Promotion">注册即送试用额度</system:String>

    <!--#endregion-->

    <!--#region Replace 设置项-->

    <system:String x:Key="Replace.Translator">替换翻译服务</system:String>
    <system:String x:Key="Replace.Translator.Tooltip">选择替换翻译服务</system:String>
    <system:String x:Key="Replace.Source">原始语种</system:String>
    <system:String x:Key="Replace.Source.Tooltip">选择翻译原始语种</system:String>
    <system:String x:Key="Replace.Target">目标语种</system:String>
    <system:String x:Key="Replace.Target.Tooltip">选择翻译目标语种</system:String>
    <system:String x:Key="Replace.DetectTypeWithSourceAuto">语种识别(仅当原始语种为自动时生效)</system:String>
    <system:String x:Key="Replace.DetectTypeWithSourceAuto.Tooltip">语种识别方式，仅当原始语种为自动</system:String>
    <system:String x:Key="Replace.AutoDetectType">语种识别比例(zh-en)</system:String>
    <system:String x:Key="Replace.AutoDetectType.Tooltip">仅针对 本地识别 中英文情况下英文字符占总字符数的比例</system:String>
    <system:String x:Key="Replace.SourceLangIfAuto">语种识别为自动时使用</system:String>
    <system:String x:Key="Replace.SourceLangIfAuto.Tooltip">如果识别语种结果仍然为自动则使用该项指定语种&#13;仅当在线识别服务返回出错情况才会触发</system:String>
    <system:String x:Key="Replace.TargetLangIfSourceZh">目标语种为自动且原始语种为中文时使用</system:String>
    <system:String x:Key="Replace.TargetLangIfSourceZh.Tooltip">如果目标语种为自动且原始语种为中文时则使用该项指定语种&#13;仅当在线识别服务返回出错情况才会触发</system:String>
    <system:String x:Key="Replace.TargetLangIfSourceNotZh">目标语种为自动且原始语种为非中文时使用</system:String>
    <system:String x:Key="Replace.TargetLangIfSourceNotZh.Tooltip">如果目标语种为自动且原始语种为非中文时则使用该项指定语种&#13;仅当在线识别服务返回出错情况才会触发</system:String>

    <!--#endregion-->

    <!--#region History 设置项-->

    <system:String x:Key="History.Search">请输入搜索内容</system:String>
    <system:String x:Key="History.Refresh">刷新记录</system:String>
    <system:String x:Key="History.Delete">删除记录</system:String>
    <system:String x:Key="History.Gong">共</system:String>
    <system:String x:Key="History.Xiang">项</system:String>
    <system:String x:Key="History.ClearAl">清空全部</system:String>
    <system:String x:Key="History.Content.TTS">TTS</system:String>
    <system:String x:Key="History.Content.Copy">复制</system:String>
    <system:String x:Key="History.Content.CopyResult">普通复制</system:String>
    <system:String x:Key="History.Content.CopySnakeResult">复制为蛇形字符串</system:String>
    <system:String x:Key="History.Content.CopySmallHumpResult">复制为小驼峰字符串</system:String>
    <system:String x:Key="History.Content.CopyLargeHumpResult">复制为大驼峰字符串</system:String>

    <!--#endregion-->

    <!--#region Backup 设置项-->

    <system:String x:Key="Backup.BackupType">备份方式</system:String>
    <system:String x:Key="Backup.BackupType.Tooltip">请选择备份方式</system:String>
    <system:String x:Key="Backup.Address">地址</system:String>
    <system:String x:Key="Backup.Address.Tooltip">WebDav服务器地址</system:String>
    <system:String x:Key="Backup.Address.Placeholder">请输入WebDav地址</system:String>
    <system:String x:Key="Backup.Username">用户名</system:String>
    <system:String x:Key="Backup.Username.Tooltip">WebDav服务器用户名</system:String>
    <system:String x:Key="Backup.Username.Placeholder">请输入用户名</system:String>
    <system:String x:Key="Backup.Password">密码</system:String>
    <system:String x:Key="Backup.Password.Tooltip">WebDav服务器密码</system:String>
    <system:String x:Key="Backup.Password.Placeholder">请输入密码</system:String>
    <system:String x:Key="Backup.Export">导出配置</system:String>
    <system:String x:Key="Backup.Import">导入配置</system:String>

    <!--#endregion-->

    <!--#region About 设置项-->

    <system:String x:Key="About.Version">版本</system:String>
    <system:String x:Key="About.CheckUpdate">检查更新</system:String>
    <system:String x:Key="About.CancelCheck">取消操作</system:String>
    <system:String x:Key="About.Tools">工具</system:String>
    <system:String x:Key="About.CleanLog">清理日志</system:String>
    <system:String x:Key="About.OpenDirectory">打开目录</system:String>
    <system:String x:Key="About.LogDirectory">日志目录</system:String>
    <system:String x:Key="About.ConfigDirectory">配置目录</system:String>
    <system:String x:Key="About.OpenSource">开源</system:String>
    <system:String x:Key="About.Author">作者</system:String>
    <system:String x:Key="About.Website">官网</system:String>
    <system:String x:Key="About.SourceCode">源码</system:String>
    <system:String x:Key="About.Feedback">反馈</system:String>
    <system:String x:Key="About.Thanks">感谢</system:String>
    <system:String x:Key="About.ConfirmClearAllLog">确定要清理所有日志吗?</system:String>
    <system:String x:Key="About.Warning">警告</system:String>
    <system:String x:Key="About.NoUpdateExe">升级程序似乎遭到破坏，请手动前往发布页查看新版本</system:String>
    <system:String x:Key="About.GetNewer">检测到最新版本</system:String>
    <system:String x:Key="About.UpdateFailed">检查更新出错, 请检查网络情况</system:String>
    <system:String x:Key="About.Downloading">正在下载软件</system:String>
    <system:String x:Key="About.DownloadSuccess">软件下载成功, 是否更新?</system:String>
    <system:String x:Key="About.ClearFiles">此次升级是否删除原软件目录下所有文件(排除log/portable_config)</system:String>

    <!--#endregion-->

    <!--#region LangSetting-->

    <system:String x:Key="LangSetting.Title">语言设置</system:String>
    <system:String x:Key="LangSetting.UnSelectAll">全不选</system:String>
    <system:String x:Key="LangSetting.SelectAll">全选</system:String>

    <!--#endregion-->

    <!--#region Translator Selector-->

    <system:String x:Key="TranslatorSelector.Title">选择服务</system:String>
    <system:String x:Key="TranslatorSelector.SelfBuild">自建服务</system:String>
    <system:String x:Key="TranslatorSelector.SelfBuild.Tooltip">需要手动配置服务器，并填入API(Token等验证信息)</system:String>
    <system:String x:Key="TranslatorSelector.BuiltIn">内置服务</system:String>
    <system:String x:Key="TranslatorSelector.BuiltIn.Tooltip">添加后可直接使用</system:String>
    <system:String x:Key="TranslatorSelector.Official">官方服务</system:String>
    <system:String x:Key="TranslatorSelector.Official.Tooltip">对应官网申请API Key后填入使用</system:String>

    <system:String x:Key="ServiceType.SelfBuild">自建</system:String>
    <system:String x:Key="ServiceType.BuiltIn">内置</system:String>
    <system:String x:Key="ServiceType.Official">官方</system:String>

    <!--#endregion-->

    <!--#region WebDav-->

    <system:String x:Key="WebDav.Title">选择配置</system:String>
    <system:String x:Key="WebDav.NoContent">当前路径下尚未找到备份内容...</system:String>

    <!--#endregion-->

    <!--#endregion-->

    <!--#region 主界面-->

    <system:String x:Key="Topmost">置顶</system:String>
    <system:String x:Key="WindowClose">关闭窗口</system:String>
    <system:String x:Key="WindowMinimized">最小化窗口</system:String>
    <system:String x:Key="Preference">偏好设置</system:String>
    <system:String x:Key="ConfigureService">配置服务</system:String>
    <system:String x:Key="SilentOCRLang">静默OCR/截图翻译语种</system:String>
    <system:String x:Key="MouseHook">监听鼠标划词</system:String>
    <system:String x:Key="AutoTranslate">自动翻译</system:String>
    <system:String x:Key="IncrementalTranslation">增量翻译</system:String>
    <system:String x:Key="OnlyShowOutput">仅显示输出界面&#13;Alt+鼠标点击控制语言界面</system:String>
    <system:String x:Key="ScreenshotTranslate">截图翻译</system:String>
    <system:String x:Key="SilentOCR">静默OCR</system:String>
    <system:String x:Key="OCR">OCR</system:String>
    <system:String x:Key="ClipboardMonitor">监听剪贴板</system:String>
    <system:String x:Key="QRCode">识别二维码</system:String>
    <system:String x:Key="History">历史记录</system:String>
    <system:String x:Key="DevelopmentVersion">[开发版]</system:String>

    <system:String x:Key="Input.SelectAll">全选</system:String>
    <system:String x:Key="Input.Copy">复制</system:String>
    <system:String x:Key="Input.Paste">粘贴</system:String>
    <system:String x:Key="Input.Clear">清空</system:String>
    <system:String x:Key="Input.SaveToVocabulary">保存至生词本</system:String>
    <system:String x:Key="Input.CopyInputContent">复制输入内容</system:String>
    <system:String x:Key="Input.RemoveLineBreak">移除换行&#13;Ctrl+点击切换处理换行模式</system:String>
    <system:String x:Key="Input.RemoveSpaces">移除空格</system:String>
    <system:String x:Key="Input.DetectedAs">识别为</system:String>
    <system:String x:Key="Input.ValidContent">请输入有效内容</system:String>
    <system:String x:Key="Input.Cache">缓存</system:String>
    <system:String x:Key="Input.RequestCancel">请求取消</system:String>
    <system:String x:Key="Input.RequestError">请求出错</system:String>
    <system:String x:Key="Input.RequestTimeout">请求超时(请检查网络环境是否正常或服务是否可用)\n</system:String>
    <system:String x:Key="Input.RequestApi">请求API</system:String>
    <system:String x:Key="Input.ExceptionMsg">异常信息</system:String>

    <system:String x:Key="Output.Retry">重试</system:String>
    <system:String x:Key="Output.TextToSpeech">文字转语音</system:String>
    <system:String x:Key="Output.CopyAsLargeCamelCase">复制为大驼峰字符串</system:String>
    <system:String x:Key="Output.CopyAsSmallCamelCase">复制为小驼峰字符串</system:String>
    <system:String x:Key="Output.CopyAsSnakeCase">复制为蛇形字符串</system:String>
    <system:String x:Key="Output.CopyDirectly">直接复制结果</system:String>
    <system:String x:Key="Output.InsertResult">插入结果</system:String>
    <system:String x:Key="Output.ExecuteTranslation">执行翻译</system:String>
    <system:String x:Key="Output.ExecuteTranslationTooltip">执行当前服务翻译</system:String>
    <system:String x:Key="Output.ExecuteBackTranslation">执行回译</system:String>
    <system:String x:Key="Output.ExecuteBackTranslationTooltip">执行当前服务回译</system:String>
    <system:String x:Key="Output.AutoExecute">自动执行</system:String>
    <system:String x:Key="Output.AutoExecuteTooltip">执行翻译时是否自动翻译，否则需要手动点击才能进行翻译，保存至配置文件</system:String>
    <system:String x:Key="Output.AutoBackTranslation">自动回译</system:String>
    <system:String x:Key="Output.AutoBackTranslationTooltip">执行翻译时是否自动执行回译，否则需要手动点击才能进行回译，保存至配置文件</system:String>
    <system:String x:Key="Output.ConfigureService">配置服务</system:String>
    <system:String x:Key="Output.ConfigureServiceTooltip">进入服务配置页面</system:String>
    <system:String x:Key="Output.CloseService">关闭服务</system:String>
    <system:String x:Key="Output.CloseServiceTooltip">立即关闭服务并保存至配置文件</system:String>
    <system:String x:Key="Output.BackTranslateTooltip">回译(Ctrl+点击 启/禁用)</system:String>
    <system:String x:Key="Output.BackTranslationResultTooltip">右键双击可关闭回译内容</system:String>

    <!--#endregion-->

    <!--#region 托盘-->

    <system:String x:Key="NotifyIcon.InputTranslate">输入翻译</system:String>
    <system:String x:Key="NotifyIcon.ScreenShotTranslate">截图翻译</system:String>
    <system:String x:Key="NotifyIcon.MousehookTranslate">鼠标划词</system:String>
    <system:String x:Key="NotifyIcon.ClipboardMonitor">监听剪贴板</system:String>
    <system:String x:Key="NotifyIcon.OCR">文字识别</system:String>
    <system:String x:Key="NotifyIcon.SilentOCR">静默识字</system:String>
    <system:String x:Key="NotifyIcon.QRCode">二维码</system:String>
    <system:String x:Key="NotifyIcon.OpenMainWindow">显示界面</system:String>
    <system:String x:Key="NotifyIcon.OpenPreference">偏好设置</system:String>
    <system:String x:Key="NotifyIcon.ForbiddenShortcuts">禁用热键</system:String>
    <system:String x:Key="NotifyIcon.Exit">退出程序</system:String>
    <system:String x:Key="NotifyIcon.NewVersion">「新版软件已经发布，请前往【关于】页面更新」</system:String>
    <system:String x:Key="NotifyIcon.Show.ShortcutDisabled">快捷键禁用</system:String>
    <system:String x:Key="NotifyIcon.Show.Input">输入</system:String>
    <system:String x:Key="NotifyIcon.Show.Crossword">划词</system:String>
    <system:String x:Key="NotifyIcon.Show.Screenshot">截图</system:String>
    <system:String x:Key="NotifyIcon.Show.Replace">替换</system:String>
    <system:String x:Key="NotifyIcon.Show.Mainview">显示</system:String>
    <system:String x:Key="NotifyIcon.Show.Mouse">鼠标</system:String>
    <system:String x:Key="NotifyIcon.Show.OCR">识字</system:String>
    <system:String x:Key="NotifyIcon.Show.SlientOCR">静默OCR</system:String>
    <system:String x:Key="NotifyIcon.Show.SlientTTS">静默TTS</system:String>
    <system:String x:Key="NotifyIcon.Show.Clipboard">剪贴板</system:String>

    <!--#endregion-->

    <!--#region OCR-->

    <system:String x:Key="OCR.CopyImage">复制图片</system:String>
    <system:String x:Key="OCR.SaveImage">保存图片</system:String>
    <system:String x:Key="OCR.FitWindow">适应窗口</system:String>
    <system:String x:Key="OCR.SwitchImg">切换原图/标注图</system:String>
    <system:String x:Key="OCR.DropImg">将图片拖放到此处</system:String>
    <system:String x:Key="OCR.QRCodeResult">二维码识别结果</system:String>
    <system:String x:Key="OCR.File">文件</system:String>
    <system:String x:Key="OCR.File.Tooltip">选中图片文件进行文字识别</system:String>
    <system:String x:Key="OCR.Screenshot">截图</system:String>
    <system:String x:Key="OCR.Screenshot.Tooltip">截图进行文字识别</system:String>
    <system:String x:Key="OCR.Clipboard">剪贴板</system:String>
    <system:String x:Key="OCR.Clipboard.Tooltip">截图进行文字识别</system:String>
    <system:String x:Key="OCR.Setting">设置</system:String>
    <system:String x:Key="OCR.Translate">翻译</system:String>
    <system:String x:Key="OCR.QRCode">二维码</system:String>
    <system:String x:Key="OCR.Recertification">识别</system:String>

    <!--#endregion-->

    <!--#region Prompt-->

    <system:String x:Key="Prompt.Edit">编辑Prompt</system:String>
    <system:String x:Key="Prompt.Delete">删除Prompt</system:String>
    <system:String x:Key="Prompt.Add">添加Prompt</system:String>
    <system:String x:Key="Prompt.Import">导入Prompt&#13;从文件添加</system:String>
    <system:String x:Key="Prompt.Export">导出Prompt&#13;默认导出选中项&#13;Ctrl点击导出全部</system:String>
    <system:String x:Key="Prompt.DropImport">将Prompt拖放到此处</system:String>

    <!--#endregion-->

    <!--#region MessageBox-->

    <system:String x:Key="MessageBox.AlreadyRunning">应用程序已经在运行中。</system:String>
    <system:String x:Key="MessageBox.MultiOpeningDetection">多开检测</system:String>
    <system:String x:Key="MessageBox.HotkeysConflict">全局热键冲突，请前往软件首选项中修改...</system:String>
    <system:String x:Key="MessageBox.AlreadyListeningWordSelection">当前监听鼠标划词中，请先解除监听...</system:String>
    <system:String x:Key="MessageBox.ContinueReset">重置该配置影响替换翻译配置，是否继续恢复？</system:String>
    <system:String x:Key="MessageBox.Tip">提示</system:String>
    <system:String x:Key="MessageBox.SupportedVoice">系统支持的语音：</system:String>

    <!--#endregion-->

    <!--#region Toast Terms-->
    <system:String x:Key="Toast.Terms.EmptyList">术语列表为空，无法导出</system:String>
    <system:String x:Key="Toast.Terms.ExportTitle">导出术语文件</system:String>
    <system:String x:Key="Toast.Terms.ExportSuccess">导出 {0} 条记录</system:String>
    <system:String x:Key="Toast.Terms.ExportFailed">导出失败</system:String>
    <system:String x:Key="Toast.Terms.ImportTitle">选择要导入的术语文件</system:String>
    <system:String x:Key="Toast.Terms.NoValidData">没有找到有效的术语数据</system:String>
    <system:String x:Key="Toast.Terms.ImportReplace">导入替换 {0} 条记录</system:String>
    <system:String x:Key="Toast.Terms.ImportAppend">导入追加 {0} 条记录</system:String>
    <system:String x:Key="Toast.Terms.ImportFailed">导入失败</system:String>
    <!--#endregion-->

    <!--#region MessageBox Terms-->
    <system:String x:Key="MessageBox.Terms.Clear">是否清空术语?</system:String>
    <system:String x:Key="MessageBox.Terms.ImportTitle">导入术语</system:String>
    <system:String x:Key="MessageBox.Terms.ImportConfirm" xml:space="preserve">找到 {0} 条术语记录。
点击【是】替换现有术语列表
点击【否】追加到现有术语列表
点击【取消】取消导入操作</system:String>
    <!--#endregion-->

</ResourceDictionary>