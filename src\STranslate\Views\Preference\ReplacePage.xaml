﻿<UserControl
    x:Class="STranslate.Views.Preference.ReplacePage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:common="clr-namespace:STranslate.Style.Commons;assembly=STranslate.Style"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:model="clr-namespace:STranslate.Model;assembly=STranslate.Model"
    xmlns:props="clr-namespace:STranslate.Style.Themes;assembly=STranslate.Style"
    xmlns:vm="clr-namespace:STranslate.ViewModels.Preference"
    d:DataContext="{d:DesignInstance Type=vm:ReplaceViewModel}"
    d:DesignHeight="450"
    d:DesignWidth="800"
    d:FontFamily="{DynamicResource UserFont}"
    d:FontSize="{DynamicResource FontSize18}"
    Style="{StaticResource ResourceKey=Page_Style}"
    mc:Ignorable="d">
    <Border CornerRadius="5">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition />
                <RowDefinition Height="60" />
            </Grid.RowDefinitions>

            <StackPanel Orientation="Vertical">
                <Border
                    Margin="8"
                    props:ThemeProps.Background="{DynamicResource BorderContentBackground}"
                    CornerRadius="5">
                    <StackPanel Orientation="Vertical">

                        <DockPanel Margin="20,10">
                            <TextBlock Text="{DynamicResource Replace.Translator}" />
                            <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Replace.Translator.Tooltip}" />
                            <ComboBox
                                Height="30"
                                HorizontalAlignment="Right"
                                BorderThickness="1"
                                DisplayMemberPath="Name"
                                ItemsSource="{Binding AllServices}"
                                SelectedItem="{Binding ReplaceProp.ActiveService, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                        </DockPanel>

                        <DockPanel Margin="20,10">
                            <TextBlock Text="{DynamicResource Replace.Source}" />
                            <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Replace.Source.Tooltip}" />
                            <ComboBox
                                Name="SourceLangCb"
                                Height="30"
                                HorizontalAlignment="Right"
                                common:LangAwareSelector.IsLangAware="True"
                                BorderThickness="1"
                                DisplayMemberPath="Description"
                                SelectedValue="{Binding ReplaceProp.SourceLang, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                SelectedValuePath="Value">
                                <ComboBox.ItemsSource>
                                    <MultiBinding Converter="{StaticResource MultiLangFilterConverter}">
                                        <Binding Source="{common:LangEnumeration {x:Type model:LangEnum}}" />
                                        <Binding Path="InputVm.OftenUsedLang" />
                                    </MultiBinding>
                                </ComboBox.ItemsSource>
                            </ComboBox>
                        </DockPanel>

                        <DockPanel Margin="20,10">
                            <TextBlock Text="{DynamicResource Replace.Target}" />
                            <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Replace.Target.Tooltip}" />
                            <ComboBox
                                Name="TargetLangCb"
                                Height="30"
                                HorizontalAlignment="Right"
                                common:LangAwareSelector.IsLangAware="True"
                                BorderThickness="1"
                                DisplayMemberPath="Description"
                                SelectedValue="{Binding ReplaceProp.TargetLang, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                SelectedValuePath="Value">
                                <ComboBox.ItemsSource>
                                    <MultiBinding Converter="{StaticResource MultiLangFilterConverter}">
                                        <Binding Source="{common:LangEnumeration {x:Type model:LangEnum}}" />
                                        <Binding Path="InputVm.OftenUsedLang" />
                                    </MultiBinding>
                                </ComboBox.ItemsSource>
                            </ComboBox>
                        </DockPanel>

                        <DockPanel Margin="20,10" Visibility="{Binding ReplaceProp.TargetLang, ConverterParameter=auto, Converter={StaticResource ParamEqualToVisibility}}">
                            <TextBlock Text="{DynamicResource Replace.DetectTypeWithSourceAuto}" />
                            <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Replace.DetectTypeWithSourceAuto.Tooltip}" />
                            <ComboBox
                                Height="30"
                                HorizontalAlignment="Right"
                                common:LangAwareSelector.IsLangAware="True"
                                BorderThickness="1"
                                DisplayMemberPath="Description"
                                ItemsSource="{Binding Source={common:Enumeration {x:Type model:LangDetectType}}}"
                                SelectedValue="{Binding ReplaceProp.DetectType, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                SelectedValuePath="Value" />
                        </DockPanel>

                        <DockPanel Margin="20,10">
                            <DockPanel.Visibility>
                                <MultiBinding Converter="{StaticResource ReplaceMultiProp2VisibilityConverter}">
                                    <Binding Path="ReplaceProp.DetectType" />
                                    <Binding Path="ReplaceProp.TargetLang" />
                                </MultiBinding>
                            </DockPanel.Visibility>
                            <TextBlock Text="{DynamicResource Replace.AutoDetectType}" />
                            <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Replace.AutoDetectType.Tooltip}" />
                            <StackPanel
                                HorizontalAlignment="Right"
                                DockPanel.Dock="Right"
                                Orientation="Horizontal">
                                <Slider
                                    Height="14"
                                    MinWidth="160"
                                    Margin="0,0,30,0"
                                    IsSnapToTickEnabled="True"
                                    Maximum="0.99"
                                    Minimum="0.01"
                                    TickFrequency="0.01"
                                    TickPlacement="None"
                                    Value="{Binding ReplaceProp.AutoScale}" />
                                <TextBlock
                                    Width="65"
                                    Margin="10,0"
                                    Text="{Binding ReplaceProp.AutoScale, StringFormat={}{0:P0}}" />
                            </StackPanel>
                        </DockPanel>

                        <DockPanel Margin="20,10">
                            <TextBlock Text="{DynamicResource Replace.SourceLangIfAuto}" />
                            <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Replace.SourceLangIfAuto.Tooltip}" />
                            <ComboBox
                                Name="SourceLangIfAutoCb"
                                Height="30"
                                HorizontalAlignment="Right"
                                common:LangAwareSelector.IsLangAware="True"
                                BorderThickness="1"
                                DisplayMemberPath="Description"
                                SelectedValue="{Binding ReplaceProp.SourceLangIfAuto, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                SelectedValuePath="Value">
                                <ComboBox.ItemsSource>
                                    <MultiBinding Converter="{StaticResource MultiLangFilterConverter}">
                                        <Binding Source="{common:LangEnumeration {x:Type model:LangEnum}}" />
                                        <Binding Path="InputVm.OftenUsedLang" />
                                    </MultiBinding>
                                </ComboBox.ItemsSource>
                            </ComboBox>
                        </DockPanel>

                        <DockPanel Margin="20,10">
                            <TextBlock Text="{DynamicResource Replace.TargetLangIfSourceZh}" />
                            <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Replace.TargetLangIfSourceZh.Tooltip}" />
                            <ComboBox
                                Name="TargetLangIfSourceZhCb"
                                Height="30"
                                HorizontalAlignment="Right"
                                common:LangAwareSelector.IsLangAware="True"
                                BorderThickness="1"
                                DisplayMemberPath="Description"
                                SelectedValue="{Binding ReplaceProp.TargetLangIfSourceZh, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                SelectedValuePath="Value">
                                <ComboBox.ItemsSource>
                                    <MultiBinding Converter="{StaticResource MultiLangFilterConverter}">
                                        <Binding Source="{common:LangEnumeration {x:Type model:LangEnum}}" />
                                        <Binding Path="InputVm.OftenUsedLang" />
                                    </MultiBinding>
                                </ComboBox.ItemsSource>
                            </ComboBox>
                        </DockPanel>

                        <DockPanel Margin="20,10">
                            <TextBlock Text="{DynamicResource Replace.TargetLangIfSourceNotZh}" />
                            <TextBlock Style="{DynamicResource InfoTextBlock}" ToolTip="{DynamicResource Replace.TargetLangIfSourceNotZh.Tooltip}" />
                            <ComboBox
                                Name="TargetLangIfSourceNotZhCb"
                                Height="30"
                                HorizontalAlignment="Right"
                                common:LangAwareSelector.IsLangAware="True"
                                BorderThickness="1"
                                DisplayMemberPath="Description"
                                SelectedValue="{Binding ReplaceProp.TargetLangIfSourceNotZh, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                SelectedValuePath="Value">
                                <ComboBox.ItemsSource>
                                    <MultiBinding Converter="{StaticResource MultiLangFilterConverter}">
                                        <Binding Source="{common:LangEnumeration {x:Type model:LangEnum}}" />
                                        <Binding Path="InputVm.OftenUsedLang" />
                                    </MultiBinding>
                                </ComboBox.ItemsSource>
                            </ComboBox>
                        </DockPanel>
                    </StackPanel>
                </Border>
            </StackPanel>


            <Grid Grid.Row="1" Margin="20,0">
                <Button
                    Width="60"
                    Margin="0,0,76,0"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Center"
                    HorizontalContentAlignment="Center"
                    VerticalContentAlignment="Center"
                    Command="{Binding ResetCommand}"
                    Content="{DynamicResource Preference.Reset}" />
                <Button
                    Width="60"
                    Padding="3"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Center"
                    Command="{Binding SaveCommand}"
                    Content="{DynamicResource Preference.Save}" />
            </Grid>

        </Grid>
    </Border>
</UserControl>