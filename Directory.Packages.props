<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>
  <ItemGroup>
    <PackageVersion Include="AlibabaCloud.SDK.Alimt20181012" Version="2.3.0" />
    <PackageVersion Include="CommunityToolkit.Mvvm" Version="8.3.2" />
    <PackageVersion Include="Dapper" Version="2.1.35" />
    <PackageVersion Include="Dapper.Contrib" Version="2.0.78" />
    <PackageVersion Include="EdgeTTS.Net" Version="1.1.2.3" />
    <PackageVersion Include="gong-wpf-dragdrop" Version="4.0.0" />
    <PackageVersion Include="H.InputSimulator" Version="1.4.2" />
    <PackageVersion Include="Hardcodet.NotifyIcon.Wpf" Version="2.0.1" />
    <PackageVersion Include="MdXaml" Version="1.27.0" />
    <PackageVersion Include="Microsoft.CognitiveServices.Speech" Version="1.41.1" />
    <PackageVersion Include="Microsoft.Data.Sqlite" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Xaml.Behaviors.Wpf" Version="1.1.135" />
    <PackageVersion Include="NAudio" Version="2.2.1" />
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageVersion Include="PaddleOCRSharp" Version="4.3.0" />
    <PackageVersion Include="ScreenGrab" Version="1.0.8" />
    <PackageVersion Include="System.Net.Http" Version="4.3.4" />
    <PackageVersion Include="System.Private.Uri" Version="4.3.2" />
    <PackageVersion Include="System.Speech" Version="9.0.0" />
    <PackageVersion Include="System.Text.RegularExpressions" Version="4.3.1" />
    <PackageVersion Include="TencentCloudSDK.Tmt" Version="3.0.1122" />
    <PackageVersion Include="WebDav.Client" Version="2.8.0" />
    <PackageVersion Include="WeChatOcr" Version="1.0.3" />
    <PackageVersion Include="WpfScreenHelper" Version="2.1.1" />
    <PackageVersion Include="XamlFlair.WPF" Version="1.2.13" />
    <PackageVersion Include="ZXing.Net.Bindings.ZKWeb.System.Drawing" Version="0.16.7" />
    <PackageVersion Include="Serilog" Version="4.1.0" />
    <PackageVersion Include="Serilog.Sinks.File" Version="6.0.0" />
    <PackageVersion Include="System.Drawing.Common" Version="9.0.4" />
    <PackageVersion Include="System.IdentityModel.Tokens.Jwt" Version="8.2.0" />
    <PackageVersion Include="System.Management" Version="9.0.0" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.11.1" />
    <PackageVersion Include="xunit" Version="2.9.2" />
  </ItemGroup>
</Project>