﻿<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converter="clr-namespace:STranslate.Style.Converters">
    <!--  // 转换器 //  -->
    <converter:VisibilityConverter x:Key="VisibilityConverter" />
    <converter:MultiValue2ExpandedConverter x:Key="MultiValue2ExpandedConverter" />
    <converter:String2IconConverter x:Key="String2IconConverter" />
    <converter:MultiValue2ListConverter x:Key="MultiValue2ListConverter" />
    <converter:MultiExpanderValue2VisibilityConverter x:Key="MultiExpanderValue2VisibilityConverter" />
    <converter:String2IsEnableConverter x:Key="String2IsEnableConverter" />
    <converter:BooleanReverseConverter x:Key="BooleanReverseConverter" />
    <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
    <converter:BooleanToVisibilityReverseConverter x:Key="BooleanToVisibilityReverseConverter" />
    <converter:BooleanToContentConverter x:Key="BooleanToContentConverter" />
    <converter:PreferenceType2BooleanConverter x:Key="PreferenceType2BooleanConverter" />
    <converter:EnumToVisibilityConverter x:Key="EnumToVisibilityConverter" />
    <converter:MultiProxyParam2VisibilityConverter x:Key="MultiProxyParam2VisibilityConverter" />
    <converter:Expander2ScaleYConverter x:Key="Expander2ScaleYConverter" />
    <converter:ComboBoxIconConverter x:Key="ComboBoxIconConverter" />
    <converter:MultiComboBoxIconConverter x:Key="MultiComboBoxIconConverter" />
    <converter:ServiceType2StringConverter x:Key="ServiceType2StringConverter" />
    <converter:ServiceType2BrushConverter x:Key="ServiceType2BrushConverter" />
    <converter:BooleanToVisibilityHiddenConverter x:Key="BooleanToVisibilityHiddenConverter" />
    <converter:Collection2VisibilityConverter x:Key="Collection2VisibilityConverter" />
    <converter:TextBoxToIntConverter x:Key="TextBoxToIntConverter" />
    <converter:ParamEqualToVisibility x:Key="ParamEqualToVisibility" />
    <converter:PromptToVisibilityConverter x:Key="PromptToVisibilityConverter" />
    <converter:PromptConverter x:Key="PromptConverter" />
    <converter:PromptToVisibilityMultiConverter x:Key="PromptToVisibilityMultiConverter" />
    <converter:StringBoolean2VisibilityConverter x:Key="StringBoolean2VisibilityConverter" />
    <converter:ReplaceMultiProp2VisibilityConverter x:Key="ReplaceMultiProp2VisibilityConverter" />
    <converter:LangEnumDescriptionConverter x:Key="LangEnumDescriptionConverter" />
    <converter:MultiLangFilterConverter x:Key="MultiLangFilterConverter" />
    <converter:StringToVisibilityHiddenConverter x:Key="StringToVisibilityHiddenConverter" />
    <converter:GlobalFontSizeToFontSizeConverter x:Key="GlobalFontSizeToFontSizeConverter" />
    <converter:GlobalFontSizeToDescriptionConverter x:Key="GlobalFontSizeToDescriptionConverter" />
    <converter:JsonSerializeConverter x:Key="JsonSerializeConverter" />
    <converter:MultiValue2VisibilityReverseConverter x:Key="MultiValue2VisibilityReverseConverter" />
    <converter:ServiceTypeFilterConverter x:Key="ServiceTypeFilterConverter" />
    <converter:DictConverter x:Key="DictConverter" />
    <converter:DictItemVisibilityConverter x:Key="DictItemVisibilityConverter" />
    <converter:DictResultMultiVisibilityConverter x:Key="DictResultMultiVisibilityConverter" />
    <converter:DictResultMultiVisibilityCollapsedConverter x:Key="DictResultMultiVisibilityCollapsedConverter" />
    <converter:DictIconMultiVisibilityCollapsedConverter x:Key="DictIconMultiVisibilityCollapsedConverter" />
    <converter:DictIconMultiVisibilityConverter x:Key="DictIconMultiVisibilityConverter" />
    <converter:DictToCollapsedConverter x:Key="DictToCollapsedConverter" />
    <converter:DictGetWordConverter x:Key="DictGetWordConverter" />
    <converter:MultiValue2BooleanConverter x:Key="MultiValue2BooleanConverter" />
    <converter:MultiServiceType2StringConverter x:Key="MultiServiceType2StringConverter" />
    <converter:MultiDetectTypeEnumDescriptionConverter x:Key="MultiDetectTypeEnumDescriptionConverter" />
    <converter:ServiceTypeConverter x:Key="ServiceTypeConverter" />
</ResourceDictionary>