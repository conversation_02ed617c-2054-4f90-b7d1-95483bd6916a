﻿<UserControl
    x:Class="STranslate.Views.OutputView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:cnvt="clr-namespace:STranslate.Style.Converters;assembly=STranslate.Style"
    xmlns:common="clr-namespace:STranslate.Style.Commons;assembly=STranslate.Style"
    xmlns:control="clr-namespace:STranslate.Style.Controls;assembly=STranslate.Style"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:model="clr-namespace:STranslate.Model;assembly=STranslate.Model"
    xmlns:props="clr-namespace:STranslate.Style.Themes;assembly=STranslate.Style"
    xmlns:vm="clr-namespace:STranslate.ViewModels"
    xmlns:xf="clr-namespace:XamlFlair;assembly=XamlFlair.WPF"
    d:DataContext="{d:DesignInstance Type=vm:OutputViewModel}"
    d:DesignHeight="450"
    d:DesignWidth="800"
    mc:Ignorable="d">
    <UserControl.Resources>
        <common:BindingProxy x:Key="OutputVm" Data="{Binding}" />
        <Storyboard x:Key="FadeInStoryboard">
            <DoubleAnimation
                Storyboard.TargetName="HeaderCopySP"
                Storyboard.TargetProperty="Opacity"
                To="1"
                Duration="0:0:1" />
        </Storyboard>
        <Storyboard x:Key="FadeOutStoryboard">
            <DoubleAnimation
                Storyboard.TargetName="HeaderCopySP"
                Storyboard.TargetProperty="Opacity"
                To="0"
                Duration="0:0:1" />
        </Storyboard>
    </UserControl.Resources>
    <ListBox
        Background="Transparent"
        BorderThickness="0"
        ItemsSource="{Binding Translators}"
        PreviewMouseWheel="ListBox_PreviewMouseWheel"
        ScrollViewer.HorizontalScrollBarVisibility="Disabled">
        <ListBox.ItemTemplate>
            <DataTemplate>
                <Border
                    Width="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type ListBox}}, Path=Width}"
                    xf:Animations.Primary="{xf:Animate BasedOn={StaticResource FadeInAndGrow},
                                                       Delay=10,
                                                       TransformOn=Render}"
                    Style="{DynamicResource BorderInOutputStyle}"
                    Visibility="{Binding IsEnabled, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <Expander
                        x:Name="TranslatorExpander"
                        VerticalAlignment="Top"
                        common:ExpanderHelper.DataResult="{Binding Data.Result, Converter={StaticResource String2IsEnableConverter}}"
                        ExpandDirection="Down"
                        SnapsToDevicePixels="True">
                        <!--#region Resources-BindingProxy 传递 ITranslator-->
                        <Expander.Resources>
                            <common:BindingProxy x:Key="TranslatorService" Data="{Binding .}" />
                        </Expander.Resources>
                        <!--#endregion-->
                        <!--#region Header-->
                        <Expander.Header>
                            <Grid Height="30" Background="Transparent">
                                <i:Interaction.Triggers>
                                    <!--  // https://stackoverflow.com/questions/3870214/eventtrigger-with-setter-in-wpf //  -->
                                    <i:EventTrigger EventName="MouseEnter">
                                        <i:ChangePropertyAction
                                            PropertyName="Visibility"
                                            TargetName="ExtraSettingsTb"
                                            Value="Visible" />
                                    </i:EventTrigger>
                                    <i:EventTrigger EventName="MouseLeave">
                                        <i:ChangePropertyAction
                                            PropertyName="Visibility"
                                            TargetName="ExtraSettingsTb"
                                            Value="Collapsed" />
                                    </i:EventTrigger>
                                </i:Interaction.Triggers>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <!--  // 右侧其他按钮 //  -->
                                <StackPanel
                                    Grid.Column="1"
                                    Margin="0,0,30,0"
                                    HorizontalAlignment="Right"
                                    VerticalAlignment="Center"
                                    FlowDirection="RightToLeft"
                                    Orientation="Horizontal">

                                    <!--  // 重试按钮 //  -->
                                    <Button
                                        Width="26"
                                        Height="26"
                                        Margin="2,0"
                                        Background="Transparent"
                                        Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.SingleTranslateCommand}"
                                        CommandParameter="{Binding}"
                                        Content="&#xe60f;"
                                        Cursor="Hand"
                                        FontSize="{DynamicResource FontSize17}"
                                        Style="{DynamicResource ButtonIconStyle}"
                                        ToolTip="{DynamicResource Output.Retry}"
                                        Visibility="{Binding Data.IsSuccess, Converter={StaticResource BooleanToVisibilityReverseConverter}}" />

                                    <!--  // 复制按钮到Expander上 Expander收起时显示//  -->
                                    <StackPanel x:Name="HeaderCopySP" Orientation="Horizontal">
                                        <StackPanel.Visibility>
                                            <MultiBinding Converter="{StaticResource MultiExpanderValue2VisibilityConverter}">
                                                <Binding Path="IsExpanded" RelativeSource="{RelativeSource AncestorType=Expander}" />
                                                <Binding Path="DataContext.CommonSettingVM.ShowCopyOnHeader" RelativeSource="{RelativeSource AncestorType=Window}" />
                                                <Binding Path="Data.IsSuccess" />
                                            </MultiBinding>
                                        </StackPanel.Visibility>
                                        <!--  TTS  -->
                                        <Button
                                            Margin="2,2,0,2"
                                            Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.TTSCommand}"
                                            CommandParameter="{Binding Data.Result}"
                                            Content="&#xe610;"
                                            Cursor="Hand"
                                            Style="{DynamicResource ButtonCopyIconStyle}"
                                            ToolTip="{DynamicResource Output.TextToSpeech}"
                                            Visibility="{Binding Data.Result, Converter={StaticResource VisibilityConverter}}" />
                                        <!--  大驼峰复制  -->
                                        <Button
                                            Margin="2,2,0,2"
                                            Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.CopyLargeHumpResultCommand}"
                                            CommandParameter="{Binding Data.Result}"
                                            Content="&#xe601;"
                                            Cursor="Hand"
                                            Style="{DynamicResource ButtonCopyIconStyle}"
                                            ToolTip="{DynamicResource Output.CopyAsLargeCamelCase}">
                                            <Button.Visibility>
                                                <MultiBinding Converter="{StaticResource StringBoolean2VisibilityConverter}">
                                                    <Binding Path="Data.Result" />
                                                    <Binding Path="Data.IsShowLargeHumpCopyBtn" Source="{StaticResource OutputVm}" />
                                                </MultiBinding>
                                            </Button.Visibility>
                                        </Button>
                                        <!--  小驼峰复制  -->
                                        <Button
                                            Margin="2,2,0,2"
                                            Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.CopySmallHumpResultCommand}"
                                            CommandParameter="{Binding Data.Result}"
                                            Content="&#xe602;"
                                            Cursor="Hand"
                                            Style="{DynamicResource ButtonCopyIconStyle}"
                                            ToolTip="{DynamicResource Output.CopyAsSmallCamelCase}">
                                            <Button.Visibility>
                                                <MultiBinding Converter="{StaticResource StringBoolean2VisibilityConverter}">
                                                    <Binding Path="Data.Result" />
                                                    <Binding Path="Data.IsShowSmallHumpCopyBtn" Source="{StaticResource OutputVm}" />
                                                </MultiBinding>
                                            </Button.Visibility>
                                        </Button>
                                        <!--  蛇形复制  -->
                                        <Button
                                            Margin="2,2,0,2"
                                            Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.CopySnakeResultCommand}"
                                            CommandParameter="{Binding Data.Result}"
                                            Content="&#xe600;"
                                            Cursor="Hand"
                                            Style="{DynamicResource ButtonCopyIconStyle}"
                                            ToolTip="{DynamicResource Output.CopyAsSnakeCase}">
                                            <Button.Visibility>
                                                <MultiBinding Converter="{StaticResource StringBoolean2VisibilityConverter}">
                                                    <Binding Path="Data.Result" />
                                                    <Binding Path="Data.IsShowSnakeCopyBtn" Source="{StaticResource OutputVm}" />
                                                </MultiBinding>
                                            </Button.Visibility>
                                        </Button>
                                        <!--  普通复制  -->
                                        <Button
                                            Margin="2,2,0,2"
                                            Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.CopyResultCommand}"
                                            CommandParameter="{Binding Data.Result}"
                                            Content="&#xe692;"
                                            Cursor="Hand"
                                            Style="{DynamicResource ButtonCopyIconStyle}"
                                            ToolTip="{DynamicResource Output.CopyDirectly}"
                                            Visibility="{Binding Data.Result, Converter={StaticResource VisibilityConverter}}" />
                                    </StackPanel>

                                    <!--  // 更多设置按钮 //  -->
                                    <ToggleButton
                                        Name="ExtraSettingsTb"
                                        Width="26"
                                        Height="26"
                                        Margin="3,0"
                                        Background="Transparent"
                                        Content="&#xe8b7;"
                                        Cursor="Hand"
                                        Style="{DynamicResource ToggleButtonIconStyle}"
                                        Visibility="Collapsed">
                                        <i:Interaction.Triggers>
                                            <i:EventTrigger EventName="Checked">
                                                <i:ChangePropertyAction
                                                    PropertyName="IsOpen"
                                                    TargetObject="{Binding ElementName=ExtraSettingsPopup}"
                                                    Value="True" />
                                            </i:EventTrigger>
                                        </i:Interaction.Triggers>
                                    </ToggleButton>
                                    <Popup
                                        Name="ExtraSettingsPopup"
                                        MinWidth="40"
                                        MinHeight="30"
                                        AllowsTransparency="True"
                                        Placement="Bottom"
                                        PlacementTarget="{Binding ElementName=ExtraSettingsTb}"
                                        PopupAnimation="Slide"
                                        StaysOpen="False">
                                        <i:Interaction.Triggers>
                                            <i:EventTrigger EventName="Closed">
                                                <i:ChangePropertyAction
                                                    PropertyName="IsChecked"
                                                    TargetObject="{Binding ElementName=ExtraSettingsTb}"
                                                    Value="{Binding ElementName=ExtraSettingsTb, Path=IsMouseOver}" />
                                            </i:EventTrigger>
                                        </i:Interaction.Triggers>
                                        <Border Style="{DynamicResource BorderStyle}">
                                            <StackPanel FlowDirection="LeftToRight">

                                                <!--  // 执行翻译 //  -->
                                                <Border
                                                    PreviewMouseDown="UI_PreviewMouseDown"
                                                    Style="{DynamicResource BorderExtraSettingsStyle}"
                                                    ToolTip="{DynamicResource Output.ExecuteTranslationTooltip}">
                                                    <i:Interaction.Triggers>
                                                        <i:EventTrigger EventName="PreviewMouseLeftButtonDown">
                                                            <i:InvokeCommandAction Command="{Binding Source={StaticResource OutputVm}, Path=Data.ExecuteTranslateCommand}">
                                                                <i:InvokeCommandAction.CommandParameter>
                                                                    <MultiBinding Converter="{StaticResource MultiValue2ListConverter}">
                                                                        <Binding Path="Data" Source="{StaticResource TranslatorService}" />
                                                                        <Binding ElementName="ExtraSettingsPopup" />
                                                                    </MultiBinding>
                                                                </i:InvokeCommandAction.CommandParameter>
                                                            </i:InvokeCommandAction>
                                                        </i:EventTrigger>
                                                    </i:Interaction.Triggers>
                                                    <Grid Margin="0,5">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="30" />
                                                            <ColumnDefinition Width="80" />
                                                        </Grid.ColumnDefinitions>

                                                        <TextBlock
                                                            Grid.Column="1"
                                                            HorizontalAlignment="Left"
                                                            FontSize="{DynamicResource FontSize14}"
                                                            Text="{DynamicResource Output.ExecuteTranslation}" />
                                                    </Grid>
                                                </Border>

                                                <!--  // 执行回译 //  -->
                                                <Border
                                                    PreviewMouseDown="UI_PreviewMouseDown"
                                                    Style="{DynamicResource BorderExtraSettingsStyle}"
                                                    ToolTip="{DynamicResource Output.ExecuteBackTranslationTooltip}"
                                                    Visibility="{Binding Type, Converter={StaticResource DictToCollapsedConverter}}">
                                                    <i:Interaction.Triggers>
                                                        <i:EventTrigger EventName="PreviewMouseLeftButtonDown">
                                                            <i:InvokeCommandAction Command="{Binding Source={StaticResource OutputVm}, Path=Data.ExecuteTranslateBackCommand}">
                                                                <i:InvokeCommandAction.CommandParameter>
                                                                    <MultiBinding Converter="{StaticResource MultiValue2ListConverter}">
                                                                        <Binding Path="Data" Source="{StaticResource TranslatorService}" />
                                                                        <Binding ElementName="ExtraSettingsPopup" />
                                                                    </MultiBinding>
                                                                </i:InvokeCommandAction.CommandParameter>
                                                            </i:InvokeCommandAction>
                                                        </i:EventTrigger>
                                                    </i:Interaction.Triggers>
                                                    <Grid Margin="0,5">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="30" />
                                                            <ColumnDefinition Width="80" />
                                                        </Grid.ColumnDefinitions>

                                                        <TextBlock
                                                            Grid.Column="1"
                                                            HorizontalAlignment="Left"
                                                            FontSize="{DynamicResource FontSize14}"
                                                            Text="{DynamicResource Output.ExecuteBackTranslation}" />
                                                    </Grid>
                                                </Border>

                                                <Separator />

                                                <!--  // 自动翻译 //  -->
                                                <Border
                                                    PreviewMouseDown="UI_PreviewMouseDown"
                                                    Style="{DynamicResource BorderExtraSettingsStyle}"
                                                    ToolTip="{DynamicResource Output.AutoExecuteTooltip}">
                                                    <i:Interaction.Triggers>
                                                        <i:EventTrigger EventName="PreviewMouseLeftButtonDown">
                                                            <i:InvokeCommandAction Command="{Binding Source={StaticResource OutputVm}, Path=Data.CanAutoExecuteCommand}">
                                                                <i:InvokeCommandAction.CommandParameter>
                                                                    <MultiBinding Converter="{StaticResource MultiValue2ListConverter}">
                                                                        <Binding Path="Data" Source="{StaticResource TranslatorService}" />
                                                                        <Binding ElementName="ExtraSettingsPopup" />
                                                                    </MultiBinding>
                                                                </i:InvokeCommandAction.CommandParameter>
                                                            </i:InvokeCommandAction>
                                                        </i:EventTrigger>
                                                    </i:Interaction.Triggers>
                                                    <Grid Margin="0,5">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="30" />
                                                            <ColumnDefinition Width="80" />
                                                        </Grid.ColumnDefinitions>

                                                        <Label
                                                            HorizontalAlignment="Left"
                                                            Content="&#xec9e;"
                                                            FontFamily="{DynamicResource IconFont}"
                                                            FontSize="{DynamicResource FontSize14}"
                                                            FontWeight="Bold"
                                                            Visibility="{Binding Source={StaticResource TranslatorService}, Path=Data.AutoExecute, Converter={StaticResource BooleanToVisibilityConverter}}" />

                                                        <TextBlock
                                                            Grid.Column="1"
                                                            HorizontalAlignment="Left"
                                                            FontSize="{DynamicResource FontSize14}"
                                                            Text="{DynamicResource Output.AutoExecute}" />
                                                    </Grid>
                                                </Border>

                                                <!--  // 自动回译 //  -->
                                                <Border
                                                    PreviewMouseDown="UI_PreviewMouseDown"
                                                    Style="{DynamicResource BorderExtraSettingsStyle}"
                                                    ToolTip="{DynamicResource Output.AutoBackTranslationTooltip}"
                                                    Visibility="{Binding Type, Converter={StaticResource DictToCollapsedConverter}}">
                                                    <i:Interaction.Triggers>
                                                        <i:EventTrigger EventName="PreviewMouseLeftButtonDown">
                                                            <i:InvokeCommandAction Command="{Binding Source={StaticResource OutputVm}, Path=Data.CanAutoExecuteTranslateBackCommand}">
                                                                <i:InvokeCommandAction.CommandParameter>
                                                                    <MultiBinding Converter="{StaticResource MultiValue2ListConverter}">
                                                                        <Binding Path="Data" Source="{StaticResource TranslatorService}" />
                                                                        <Binding ElementName="ExtraSettingsPopup" />
                                                                    </MultiBinding>
                                                                </i:InvokeCommandAction.CommandParameter>
                                                            </i:InvokeCommandAction>
                                                        </i:EventTrigger>
                                                    </i:Interaction.Triggers>
                                                    <Grid Margin="0,5">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="30" />
                                                            <ColumnDefinition Width="80" />
                                                        </Grid.ColumnDefinitions>

                                                        <Label
                                                            HorizontalAlignment="Left"
                                                            Content="&#xec9e;"
                                                            FontFamily="{DynamicResource IconFont}"
                                                            FontSize="{DynamicResource FontSize14}"
                                                            FontWeight="Bold"
                                                            Visibility="{Binding Source={StaticResource TranslatorService}, Path=Data.AutoExecuteTranslateBack, Converter={StaticResource BooleanToVisibilityConverter}}" />

                                                        <TextBlock
                                                            Grid.Column="1"
                                                            HorizontalAlignment="Left"
                                                            FontSize="{DynamicResource FontSize14}"
                                                            Text="{DynamicResource Output.AutoBackTranslation}" />
                                                    </Grid>
                                                </Border>

                                                <!--  // 配置服务 //  -->
                                                <Border
                                                    PreviewMouseDown="UI_PreviewMouseDown"
                                                    Style="{DynamicResource BorderExtraSettingsStyle}"
                                                    ToolTip="{DynamicResource Output.ConfigureServiceTooltip}">
                                                    <i:Interaction.Triggers>
                                                        <i:EventTrigger EventName="PreviewMouseLeftButtonDown">
                                                            <i:InvokeCommandAction Command="{Binding Source={StaticResource OutputVm}, Path=Data.NavigateToServiceCommand}">
                                                                <i:InvokeCommandAction.CommandParameter>
                                                                    <MultiBinding Converter="{StaticResource MultiValue2ListConverter}">
                                                                        <Binding Path="Data" Source="{StaticResource TranslatorService}" />
                                                                        <Binding ElementName="ExtraSettingsPopup" />
                                                                    </MultiBinding>
                                                                </i:InvokeCommandAction.CommandParameter>
                                                            </i:InvokeCommandAction>
                                                        </i:EventTrigger>
                                                    </i:Interaction.Triggers>
                                                    <Grid Margin="0,5">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="30" />
                                                            <ColumnDefinition Width="80" />
                                                        </Grid.ColumnDefinitions>

                                                        <TextBlock
                                                            Grid.Column="1"
                                                            HorizontalAlignment="Left"
                                                            FontSize="{DynamicResource FontSize14}"
                                                            Text="{DynamicResource Output.ConfigureService}" />
                                                    </Grid>
                                                </Border>

                                                <Separator />

                                                <!--  // 关闭服务 //  -->
                                                <Border
                                                    PreviewMouseDown="UI_PreviewMouseDown"
                                                    Style="{DynamicResource BorderExtraSettingsStyle}"
                                                    ToolTip="{DynamicResource Output.CloseServiceTooltip}">
                                                    <i:Interaction.Triggers>
                                                        <i:EventTrigger EventName="PreviewMouseLeftButtonDown">
                                                            <i:InvokeCommandAction Command="{Binding Source={StaticResource OutputVm}, Path=Data.CloseServiceCommand}">
                                                                <i:InvokeCommandAction.CommandParameter>
                                                                    <MultiBinding Converter="{StaticResource MultiValue2ListConverter}">
                                                                        <Binding Path="Data" Source="{StaticResource TranslatorService}" />
                                                                        <Binding ElementName="ExtraSettingsPopup" />
                                                                    </MultiBinding>
                                                                </i:InvokeCommandAction.CommandParameter>
                                                            </i:InvokeCommandAction>
                                                        </i:EventTrigger>
                                                    </i:Interaction.Triggers>
                                                    <Grid Margin="0,5">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="30" />
                                                            <ColumnDefinition Width="80" />
                                                        </Grid.ColumnDefinitions>

                                                        <TextBlock
                                                            Grid.Column="1"
                                                            HorizontalAlignment="Left"
                                                            FontSize="{DynamicResource FontSize14}"
                                                            Text="{DynamicResource Output.CloseService}" />
                                                    </Grid>
                                                </Border>
                                            </StackPanel>
                                        </Border>
                                    </Popup>
                                </StackPanel>

                                <!--  // 左侧服务图标、名称 //  -->
                                <StackPanel VerticalAlignment="Center" Orientation="Horizontal">
                                    <Image
                                        Width="18"
                                        Margin="10,0,0,0"
                                        Source="{Binding Icon, Converter={StaticResource String2IconConverter}}" />
                                    <TextBlock
                                        MaxWidth="{Binding Source={StaticResource OutputVm}, Path=Data.TitleMaxWidth}"
                                        Margin="10,0,0,0"
                                        props:ThemeProps.Foreground="{DynamicResource TextForeground}"
                                        FontSize="{DynamicResource FontSize14}"
                                        Text="{Binding Name}"
                                        TextTrimming="CharacterEllipsis"
                                        ToolTip="{Binding Name}" />
                                    <ToggleButton
                                        x:Name="PART_Switch"
                                        Height="22"
                                        MaxWidth="{Binding Source={StaticResource OutputVm}, Path=Data.PromptMaxWidth}"
                                        Margin="10,0,0,0"
                                        Padding="4,0"
                                        Background="Transparent"
                                        BorderBrush="{DynamicResource ThemeAccentColor}"
                                        BorderThickness="1"
                                        Content="{Binding UserDefinePrompts, Converter={StaticResource PromptConverter}}"
                                        Cursor="Hand"
                                        FontFamily="{DynamicResource UserFont}"
                                        FontSize="{DynamicResource FontSize14}"
                                        Style="{DynamicResource ToggleButtonIconStyle}"
                                        ToolTip="{Binding UserDefinePrompts, Converter={StaticResource PromptConverter}}">
                                        <ToggleButton.Visibility>
                                            <MultiBinding Converter="{StaticResource PromptToVisibilityMultiConverter}">
                                                <Binding Path="UserDefinePrompts" />
                                                <Binding Path="Data.IsPromptToggleVisible" Source="{StaticResource OutputVm}" />
                                            </MultiBinding>
                                        </ToggleButton.Visibility>
                                        <i:Interaction.Triggers>
                                            <i:EventTrigger EventName="Checked">
                                                <i:ChangePropertyAction
                                                    PropertyName="IsOpen"
                                                    TargetObject="{Binding ElementName=PART_Popup}"
                                                    Value="True" />
                                            </i:EventTrigger>
                                        </i:Interaction.Triggers>
                                    </ToggleButton>
                                    <Popup
                                        x:Name="PART_Popup"
                                        MinWidth="40"
                                        MinHeight="30"
                                        AllowsTransparency="True"
                                        Placement="Bottom"
                                        PlacementTarget="{Binding ElementName=PART_Switch}"
                                        PopupAnimation="Slide"
                                        StaysOpen="False">
                                        <!--  https://github.com/JamesnetGroup/smartdate/blob/main/src/SmartDateControl/UI/Units/SmartDate.cs#L88  -->
                                        <!--  在关闭的时候强行指定Switch的IsChecked属性为MouseOver，如果重复点那么开关就不会关闭，不会继续执行Checked事件的打开Popup控件  -->
                                        <i:Interaction.Triggers>
                                            <i:EventTrigger EventName="Closed">
                                                <i:ChangePropertyAction
                                                    PropertyName="IsChecked"
                                                    TargetObject="{Binding ElementName=PART_Switch}"
                                                    Value="{Binding ElementName=PART_Switch, Path=IsMouseOver}" />
                                            </i:EventTrigger>
                                        </i:Interaction.Triggers>
                                        <Border Style="{DynamicResource BorderStyle}">
                                            <ListBox
                                                x:Name="PromptLb"
                                                Background="Transparent"
                                                BorderThickness="0"
                                                ItemsSource="{Binding Source={StaticResource TranslatorService}, Path=Data.UserDefinePrompts}">
                                                <ListBox.ItemTemplate>
                                                    <DataTemplate DataType="{x:Type model:UserDefinePrompt}">
                                                        <Border
                                                            x:Name="PromptBorder"
                                                            Margin="3"
                                                            HorizontalAlignment="Center"
                                                            props:ThemeProps.Background="{DynamicResource BorderBackground}"
                                                            BorderBrush="{x:Null}"
                                                            CornerRadius="5">
                                                            <Grid Margin="0,5">
                                                                <Grid.ColumnDefinitions>
                                                                    <ColumnDefinition Width="30" />
                                                                    <ColumnDefinition Width="70" />
                                                                </Grid.ColumnDefinitions>

                                                                <Label
                                                                    HorizontalAlignment="Left"
                                                                    Content="&#xec9e;"
                                                                    FontFamily="{DynamicResource IconFont}"
                                                                    FontSize="{DynamicResource FontSize14}"
                                                                    FontWeight="Bold"
                                                                    Visibility="{Binding Enabled, Converter={StaticResource BooleanToVisibilityHiddenConverter}}" />

                                                                <TextBlock
                                                                    Grid.Column="1"
                                                                    HorizontalAlignment="Left"
                                                                    FontSize="{DynamicResource FontSize14}"
                                                                    Text="{Binding Name}"
                                                                    TextTrimming="CharacterEllipsis"
                                                                    ToolTip="{Binding Name}" />
                                                            </Grid>
                                                        </Border>
                                                        <DataTemplate.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter TargetName="PromptBorder" Property="props:ThemeProps.Background" Value="{DynamicResource BtnMouseOverBackground}" />
                                                            </Trigger>
                                                        </DataTemplate.Triggers>
                                                    </DataTemplate>
                                                </ListBox.ItemTemplate>
                                                <i:Interaction.Triggers>
                                                    <i:EventTrigger EventName="PreviewMouseLeftButtonUp">
                                                        <i:InvokeCommandAction Command="{Binding Source={StaticResource OutputVm}, Path=Data.SelectedPromptCommand}">
                                                            <i:InvokeCommandAction.CommandParameter>
                                                                <MultiBinding Converter="{StaticResource MultiValue2ListConverter}">
                                                                    <Binding Path="Data" Source="{StaticResource TranslatorService}" />
                                                                    <Binding ElementName="PromptLb" Path="SelectedItem" />
                                                                    <Binding ElementName="PART_Popup" />
                                                                </MultiBinding>
                                                            </i:InvokeCommandAction.CommandParameter>
                                                        </i:InvokeCommandAction>
                                                    </i:EventTrigger>
                                                </i:Interaction.Triggers>
                                            </ListBox>
                                        </Border>
                                    </Popup>
                                    <!--  // 加载动画 //  -->
                                    <control:LoadingUc2
                                        Margin="10,0,0,0"
                                        DotColor="{DynamicResource ThemeAccentColor}"
                                        Opacity=".8">
                                        <control:LoadingUc2.IsLoading>
                                            <MultiBinding Converter="{StaticResource MultiValue2BooleanConverter}">
                                                <Binding Path="IsExecuting" />
                                                <Binding Path="IsTranslateBackExecuting" />
                                            </MultiBinding>
                                        </control:LoadingUc2.IsLoading>
                                    </control:LoadingUc2>
                                    <!--  IsLoading="{Binding IsExecuting}"  -->
                                </StackPanel>
                            </Grid>
                        </Expander.Header>
                        <!--#endregion-->
                        <!--#region Interaction Triggers-手动触发Expander的Expanded事件-->
                        <i:Interaction.Triggers>
                            <i:EventTrigger EventName="Expanded">
                                <i:InvokeCommandAction Command="{Binding Source={StaticResource OutputVm}, Path=Data.ExpanderHeaderCommand}">
                                    <i:InvokeCommandAction.CommandParameter>
                                        <MultiBinding Converter="{StaticResource MultiValue2ListConverter}">
                                            <Binding ElementName="TranslatorExpander" />
                                            <Binding Path="." />
                                        </MultiBinding>
                                    </i:InvokeCommandAction.CommandParameter>
                                </i:InvokeCommandAction>
                            </i:EventTrigger>
                        </i:Interaction.Triggers>
                        <!--#endregion-->
                        <!--#region Triggers Expander Header Copy Button Fade In/Out Animation-->
                        <Expander.Triggers>
                            <EventTrigger RoutedEvent="Expander.Expanded">
                                <BeginStoryboard Storyboard="{StaticResource FadeOutStoryboard}" />
                            </EventTrigger>
                            <EventTrigger RoutedEvent="Expander.Collapsed">
                                <BeginStoryboard Storyboard="{StaticResource FadeInStoryboard}" />
                            </EventTrigger>
                        </Expander.Triggers>
                        <!--#endregion-->
                        <!--#region Content-->
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*" />
                                <RowDefinition Height="Auto" MinHeight="20" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>

                            <!--#region 普通显示-->

                            <TextBox
                                PreviewMouseWheel="TextBox_PreviewMouseWheel"
                                Style="{DynamicResource TextBoxOutputStyle}"
                                Tag="{Binding Data.IsSuccess}"
                                Text="{Binding Data.Result}">
                                <TextBox.Visibility>
                                    <MultiBinding Converter="{StaticResource DictResultMultiVisibilityCollapsedConverter}">
                                        <Binding Path="Data.IsSuccess" />
                                        <Binding Path="Type" />
                                    </MultiBinding>
                                </TextBox.Visibility>
                            </TextBox>
                            <StackPanel
                                Grid.Row="1"
                                Margin="5"
                                VerticalAlignment="Bottom"
                                Orientation="Horizontal">
                                <StackPanel.Visibility>
                                    <MultiBinding Converter="{StaticResource DictIconMultiVisibilityCollapsedConverter}">
                                        <Binding Path="Type" />
                                        <Binding Path="Data.IsSuccess" />
                                    </MultiBinding>
                                </StackPanel.Visibility>
                                <!--  TTS  -->
                                <Button
                                    Margin="5,0,0,0"
                                    Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.TTSCommand}"
                                    CommandParameter="{Binding Data.Result}"
                                    Content="&#xe610;"
                                    Cursor="Hand"
                                    Style="{DynamicResource ButtonCopyIconStyle}"
                                    ToolTip="{DynamicResource Output.TextToSpeech}"
                                    Visibility="{Binding Data.Result, Converter={StaticResource VisibilityConverter}}" />
                                <!--  蛇形复制  -->
                                <Button
                                    Margin="5,0,0,0"
                                    Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.CopySnakeResultCommand}"
                                    CommandParameter="{Binding Data.Result}"
                                    Content="&#xe600;"
                                    Cursor="Hand"
                                    Style="{DynamicResource ButtonCopyIconStyle}"
                                    ToolTip="{DynamicResource Output.CopyAsSnakeCase}">
                                    <Button.Visibility>
                                        <MultiBinding Converter="{StaticResource StringBoolean2VisibilityConverter}">
                                            <Binding Path="Data.Result" />
                                            <Binding Path="Data.IsShowSnakeCopyBtn" Source="{StaticResource OutputVm}" />
                                        </MultiBinding>
                                    </Button.Visibility>
                                </Button>
                                <!--  小驼峰复制  -->
                                <Button
                                    Margin="5,0,0,0"
                                    Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.CopySmallHumpResultCommand}"
                                    CommandParameter="{Binding Data.Result}"
                                    Content="&#xe602;"
                                    Cursor="Hand"
                                    Style="{DynamicResource ButtonCopyIconStyle}"
                                    ToolTip="{DynamicResource Output.CopyAsSmallCamelCase}">
                                    <Button.Visibility>
                                        <MultiBinding Converter="{StaticResource StringBoolean2VisibilityConverter}">
                                            <Binding Path="Data.Result" />
                                            <Binding Path="Data.IsShowSmallHumpCopyBtn" Source="{StaticResource OutputVm}" />
                                        </MultiBinding>
                                    </Button.Visibility>
                                </Button>
                                <!--  大驼峰复制  -->
                                <Button
                                    Margin="5,0,0,0"
                                    Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.CopyLargeHumpResultCommand}"
                                    CommandParameter="{Binding Data.Result}"
                                    Content="&#xe601;"
                                    Cursor="Hand"
                                    Style="{DynamicResource ButtonCopyIconStyle}"
                                    ToolTip="{DynamicResource Output.CopyAsLargeCamelCase}">
                                    <Button.Visibility>
                                        <MultiBinding Converter="{StaticResource StringBoolean2VisibilityConverter}">
                                            <Binding Path="Data.Result" />
                                            <Binding Path="Data.IsShowLargeHumpCopyBtn" Source="{StaticResource OutputVm}" />
                                        </MultiBinding>
                                    </Button.Visibility>
                                </Button>
                                <!--  普通复制  -->
                                <Button
                                    Margin="5,0,0,0"
                                    Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.CopyResultCommand}"
                                    CommandParameter="{Binding Data.Result}"
                                    Content="&#xe692;"
                                    Cursor="Hand"
                                    Style="{DynamicResource ButtonCopyIconStyle}"
                                    ToolTip="{DynamicResource Output.CopyDirectly}"
                                    Visibility="{Binding Data.Result, Converter={StaticResource VisibilityConverter}}" />
                                <!--  插入文本  -->
                                <Button
                                    Margin="5,0,0,0"
                                    Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.InsertResultCommand}"
                                    Content="&#xe677;"
                                    Cursor="Hand"
                                    Style="{DynamicResource ButtonCopyIconStyle}"
                                    ToolTip="{DynamicResource Output.InsertResult}"
                                    Visibility="{Binding Data.Result, Converter={StaticResource VisibilityConverter}}">
                                    <Button.CommandParameter>
                                        <MultiBinding Converter="{StaticResource MultiValue2ListConverter}">
                                            <Binding Path="Data.Result" />
                                            <Binding RelativeSource="{RelativeSource AncestorType=Window}" />
                                        </MultiBinding>
                                    </Button.CommandParameter>
                                </Button>
                                <!--  回译该结果  -->
                                <Button
                                    Margin="5,0,0,0"
                                    Content="&#xe687;"
                                    Cursor="Hand"
                                    Style="{DynamicResource ButtonCopyIconStyle}"
                                    ToolTip="{DynamicResource Output.BackTranslateTooltip}">
                                    <i:Interaction.Triggers>
                                        <i:EventTrigger EventName="PreviewMouseLeftButtonDown">
                                            <i:InvokeCommandAction Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.SingleTranslateBackCommand}" CommandParameter="{Binding .}" />
                                        </i:EventTrigger>
                                    </i:Interaction.Triggers>
                                    <Button.Visibility>
                                        <MultiBinding Converter="{StaticResource StringBoolean2VisibilityConverter}">
                                            <Binding Path="Data.Result" />
                                            <Binding Path="Data.IsShowTranslateBackBtn" Source="{StaticResource OutputVm}" />
                                        </MultiBinding>
                                    </Button.Visibility>
                                </Button>
                            </StackPanel>

                            <!--#endregion-->

                            <!--#region 词典显示-->

                            <TextBox Grid.Row="0">
                                <TextBox.Visibility>
                                    <MultiBinding Converter="{StaticResource DictResultMultiVisibilityConverter}">
                                        <Binding Path="Data.IsSuccess" />
                                        <Binding Path="Type" />
                                    </MultiBinding>
                                </TextBox.Visibility>
                                <TextBox.Style>
                                    <Style />
                                </TextBox.Style>
                                <TextBox.Template>
                                    <ControlTemplate>
                                        <StackPanel>
                                            <StackPanel.Resources>
                                                <Style BasedOn="{StaticResource TextBoxOutputStyle}" TargetType="{x:Type TextBox}" />
                                            </StackPanel.Resources>
                                            <TextBox
                                                FontSize="{DynamicResource FontSize22}"
                                                FontWeight="Bold"
                                                Text="{Binding Data.Result, Converter={StaticResource DictConverter}, ConverterParameter=0}" />
                                            <TextBox
                                                Margin="10,8,10,0"
                                                PreviewMouseWheel="TextBox_PreviewMouseWheel"
                                                Text="{Binding Data.Result, Converter={StaticResource DictConverter}, ConverterParameter=1}"
                                                Visibility="{Binding RelativeSource={RelativeSource Self}, Path=Text, Converter={StaticResource DictItemVisibilityConverter}}" />
                                            <TextBox
                                                Margin="10,8,10,0"
                                                PreviewMouseWheel="TextBox_PreviewMouseWheel"
                                                Text="{Binding Data.Result, Converter={StaticResource DictConverter}, ConverterParameter=2}"
                                                Visibility="{Binding RelativeSource={RelativeSource Self}, Path=Text, Converter={StaticResource DictItemVisibilityConverter}}" />
                                            <TextBox
                                                Margin="10,8,10,0"
                                                PreviewMouseWheel="TextBox_PreviewMouseWheel"
                                                Text="{Binding Data.Result, Converter={StaticResource DictConverter}, ConverterParameter=3}"
                                                Visibility="{Binding RelativeSource={RelativeSource Self}, Path=Text, Converter={StaticResource DictItemVisibilityConverter}}" />
                                            <TextBox
                                                Margin="10,8,10,0"
                                                PreviewMouseWheel="TextBox_PreviewMouseWheel"
                                                Text="{Binding Data.Result, Converter={StaticResource DictConverter}, ConverterParameter=4}"
                                                Visibility="{Binding RelativeSource={RelativeSource Self}, Path=Text, Converter={StaticResource DictItemVisibilityConverter}}" />
                                        </StackPanel>
                                    </ControlTemplate>
                                </TextBox.Template>
                            </TextBox>
                            <StackPanel
                                Grid.Row="1"
                                Margin="5"
                                VerticalAlignment="Bottom"
                                Orientation="Horizontal">
                                <StackPanel.Visibility>
                                    <MultiBinding Converter="{StaticResource DictIconMultiVisibilityConverter}">
                                        <Binding Path="Type" />
                                        <Binding Path="Data.IsSuccess" />
                                    </MultiBinding>
                                </StackPanel.Visibility>
                                <!--  TTS  -->
                                <Button
                                    Margin="5,0,0,0"
                                    Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.TTSCommand}"
                                    CommandParameter="{Binding Data.Result, Converter={StaticResource DictGetWordConverter}}"
                                    Content="&#xe610;"
                                    Cursor="Hand"
                                    Style="{DynamicResource ButtonCopyIconStyle}"
                                    ToolTip="{DynamicResource Output.TextToSpeech}"
                                    Visibility="{Binding Data.Result, Converter={StaticResource VisibilityConverter}}" />
                                <!--  普通复制  -->
                                <Button
                                    Margin="5,0,0,0"
                                    Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.CopyResultCommand}"
                                    CommandParameter="{Binding Data.Result, Converter={StaticResource DictGetWordConverter}}"
                                    Content="&#xe692;"
                                    Cursor="Hand"
                                    Style="{DynamicResource ButtonCopyIconStyle}"
                                    ToolTip="{DynamicResource Output.CopyDirectly}"
                                    Visibility="{Binding Data.Result, Converter={StaticResource VisibilityConverter}}" />
                                <!--  插入文本  -->
                                <Button
                                    Margin="5,0,0,0"
                                    Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.InsertResultCommand}"
                                    Content="&#xe677;"
                                    Cursor="Hand"
                                    Style="{DynamicResource ButtonCopyIconStyle}"
                                    ToolTip="{DynamicResource Output.InsertResult}"
                                    Visibility="{Binding Data.Result, Converter={StaticResource VisibilityConverter}}">
                                    <Button.CommandParameter>
                                        <MultiBinding Converter="{StaticResource MultiValue2ListConverter}">
                                            <Binding Converter="{StaticResource DictGetWordConverter}" Path="Data.Result" />
                                            <Binding RelativeSource="{RelativeSource AncestorType=Window}" />
                                        </MultiBinding>
                                    </Button.CommandParameter>
                                </Button>
                            </StackPanel>

                            <!--#endregion-->

                            <!--  回译结果  -->
                            <Grid
                                Grid.Row="2"
                                xf:Animations.Primary="{xf:Animate TransformOn=Layout,
                                                                   BasedOn={StaticResource ScaleVertically},
                                                                   Event=Visibility,
                                                                   Duration=400}"
                                ToolTip="{DynamicResource Output.BackTranslationResultTooltip}"
                                Visibility="{Binding Data.TranslateBackResult, Converter={StaticResource VisibilityConverter}}">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="*" />
                                    <RowDefinition Height="Auto" MinHeight="20" />
                                </Grid.RowDefinitions>
                                <Separator
                                    Height="1.5"
                                    Margin="0,0,0,3"
                                    HorizontalAlignment="Stretch"
                                    VerticalAlignment="Top"
                                    Style="{DynamicResource SeparatorStyle}" />
                                <TextBox
                                    Grid.Row="1"
                                    PreviewMouseWheel="TextBox_PreviewMouseWheel"
                                    Style="{DynamicResource TextBoxOutputStyle}"
                                    Tag="{Binding Data.IsTranslateBackSuccess}"
                                    Text="{Binding Data.TranslateBackResult}">
                                    <TextBox.InputBindings>
                                        <MouseBinding
                                            Command="{Binding Data.CloseTranslationBackUiCommand, Source={StaticResource OutputVm}}"
                                            CommandParameter="{Binding .}"
                                            MouseAction="RightDoubleClick" />
                                    </TextBox.InputBindings>
                                </TextBox>

                                <StackPanel
                                    Grid.Row="2"
                                    Margin="5"
                                    VerticalAlignment="Bottom"
                                    Orientation="Horizontal"
                                    Visibility="{Binding Data.IsSuccess, Converter={StaticResource BooleanToVisibilityConverter}}">
                                    <!--  TTS  -->
                                    <Button
                                        Margin="5,0,0,0"
                                        Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.TTSCommand}"
                                        CommandParameter="{Binding Data.TranslateBackResult}"
                                        Content="&#xe610;"
                                        Cursor="Hand"
                                        Style="{DynamicResource ButtonCopyIconStyle}"
                                        ToolTip="{DynamicResource Output.TextToSpeech}"
                                        Visibility="{Binding Data.TranslateBackResult, Converter={StaticResource VisibilityConverter}}" />
                                    <!--  蛇形复制  -->
                                    <Button
                                        Margin="5,0,0,0"
                                        Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.CopySnakeResultCommand}"
                                        CommandParameter="{Binding Data.TranslateBackResult}"
                                        Content="&#xe600;"
                                        Cursor="Hand"
                                        Style="{DynamicResource ButtonCopyIconStyle}"
                                        ToolTip="{DynamicResource Output.CopyAsSnakeCase}">
                                        <Button.Visibility>
                                            <MultiBinding Converter="{StaticResource StringBoolean2VisibilityConverter}">
                                                <Binding Path="Data.TranslateBackResult" />
                                                <Binding Path="Data.IsShowSnakeCopyBtn" Source="{StaticResource OutputVm}" />
                                            </MultiBinding>
                                        </Button.Visibility>
                                    </Button>
                                    <!--  小驼峰复制  -->
                                    <Button
                                        Margin="5,0,0,0"
                                        Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.CopySmallHumpResultCommand}"
                                        CommandParameter="{Binding Data.TranslateBackResult}"
                                        Content="&#xe602;"
                                        Cursor="Hand"
                                        Style="{DynamicResource ButtonCopyIconStyle}"
                                        ToolTip="{DynamicResource Output.CopyAsSmallCamelCase}">
                                        <Button.Visibility>
                                            <MultiBinding Converter="{StaticResource StringBoolean2VisibilityConverter}">
                                                <Binding Path="Data.TranslateBackResult" />
                                                <Binding Path="Data.IsShowSmallHumpCopyBtn" Source="{StaticResource OutputVm}" />
                                            </MultiBinding>
                                        </Button.Visibility>
                                    </Button>
                                    <!--  大驼峰复制  -->
                                    <Button
                                        Margin="5,0,0,0"
                                        Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.CopyLargeHumpResultCommand}"
                                        CommandParameter="{Binding Data.TranslateBackResult}"
                                        Content="&#xe601;"
                                        Cursor="Hand"
                                        Style="{DynamicResource ButtonCopyIconStyle}"
                                        ToolTip="{DynamicResource Output.CopyAsLargeCamelCase}">
                                        <Button.Visibility>
                                            <MultiBinding Converter="{StaticResource StringBoolean2VisibilityConverter}">
                                                <Binding Path="Data.TranslateBackResult" />
                                                <Binding Path="Data.IsShowLargeHumpCopyBtn" Source="{StaticResource OutputVm}" />
                                            </MultiBinding>
                                        </Button.Visibility>
                                    </Button>
                                    <!--  普通复制  -->
                                    <Button
                                        Margin="5,0,0,0"
                                        Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.CopyResultCommand}"
                                        CommandParameter="{Binding Data.TranslateBackResult}"
                                        Content="&#xe692;"
                                        Cursor="Hand"
                                        Style="{DynamicResource ButtonCopyIconStyle}"
                                        ToolTip="{DynamicResource Output.CopyDirectly}"
                                        Visibility="{Binding Data.TranslateBackResult, Converter={StaticResource VisibilityConverter}}" />

                                    <!--  // 插入文本 //  -->
                                    <Button
                                        Margin="5,0,0,0"
                                        Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.InsertResultCommand}"
                                        Content="&#xe677;"
                                        Cursor="Hand"
                                        Style="{DynamicResource ButtonCopyIconStyle}"
                                        ToolTip="{DynamicResource Output.InsertResult}"
                                        Visibility="{Binding Data.TranslateBackResult, Converter={StaticResource VisibilityConverter}}">
                                        <Button.CommandParameter>
                                            <MultiBinding Converter="{StaticResource MultiValue2ListConverter}">
                                                <Binding Path="Data.TranslateBackResult" />
                                                <Binding RelativeSource="{RelativeSource AncestorType=Window}" />
                                            </MultiBinding>
                                        </Button.CommandParameter>
                                    </Button>
                                </StackPanel>
                            </Grid>
                        </Grid>
                        <!--#endregion-->
                    </Expander>
                </Border>
            </DataTemplate>
        </ListBox.ItemTemplate>
    </ListBox>
</UserControl>