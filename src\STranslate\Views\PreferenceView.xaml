﻿<Window
    x:Class="STranslate.Views.PreferenceView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:menu="clr-namespace:STranslate.Style.Styles.Navigation;assembly=STranslate.Style"
    xmlns:props="clr-namespace:STranslate.Style.Themes;assembly=STranslate.Style"
    xmlns:view="clr-namespace:STranslate.Views"
    xmlns:vm="clr-namespace:STranslate.ViewModels"
    Title="{DynamicResource Preference.WindowTitleInTaskbar}"
    Width="800"
    Height="650"
    MinWidth="800"
    MinHeight="650"
    d:DataContext="{d:DesignInstance Type=vm:PreferenceViewModel}"
    props:ThemeProps.Background="{DynamicResource BorderBackground}"
    props:ThemeProps.BorderBrush="{DynamicResource BorderBrushColor}"
    BorderThickness="1"
    FontFamily="{DynamicResource UserFont}"
    Icon="{DynamicResource Icon}"
    WindowStartupLocation="CenterScreen"
    mc:Ignorable="d">
    <WindowChrome.WindowChrome>
        <WindowChrome ResizeBorderThickness="5" />
    </WindowChrome.WindowChrome>
    <Window.InputBindings>
        <KeyBinding
            Key="Esc"
            Command="{Binding CloseCommand}"
            CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}" />
        <KeyBinding
            Key="S"
            Command="{Binding SaveCommand}"
            Modifiers="Ctrl" />
    </Window.InputBindings>
    <i:Interaction.Triggers>
        <i:EventTrigger EventName="StateChanged">
            <i:InvokeCommandAction Command="{Binding WindowStateChangeCommand}" CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}" />
        </i:EventTrigger>
    </i:Interaction.Triggers>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="50" />
            <RowDefinition />
        </Grid.RowDefinitions>

        <!--  // Header //  -->
        <Border
            Name="Header"
            props:ThemeProps.Background="{DynamicResource BorderBackground}"
            CornerRadius="5"
            MouseDown="Header_MouseDown"
            MouseLeftButtonDown="Header_MouseLeftButtonDown"
            WindowChrome.IsHitTestVisibleInChrome="True">

            <Grid>
                <!--  // Title //  -->
                <StackPanel
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center"
                    Orientation="Horizontal">
                    <Image
                        Height="30"
                        Margin="20,0,0,0"
                        Source="{DynamicResource STranslate}" />
                    <TextBlock
                        Margin="20,0,0,0"
                        VerticalAlignment="Center"
                        props:ThemeProps.Foreground="{DynamicResource NavigationForeground}"
                        FontSize="{DynamicResource FontSize24}"
                        FontWeight="Bold"
                        Text="{DynamicResource Preference.WindowTitle}" />
                </StackPanel>

                <!--  // Button //  -->
                <StackPanel
                    Margin="15,0"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Center"
                    Orientation="Horizontal">
                    <Button
                        Command="{Binding MinimizeCommand}"
                        CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                        Content="&#xe676;"
                        FontSize="{DynamicResource FontSize20}"
                        Style="{DynamicResource ButtonIconStyle}" />
                    <Button
                        Command="{Binding MaximizeCommand}"
                        CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                        Content="{Binding MaximizeContent}"
                        FontWeight="Bold"
                        Style="{DynamicResource ButtonIconStyle}" />
                    <Button
                        Command="{Binding CloseCommand}"
                        CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                        Content="&#xe64d;"
                        FontSize="{DynamicResource FontSize16}"
                        FontWeight="Bold"
                        Style="{DynamicResource ButtonCloseStyle}" />
                </StackPanel>
            </Grid>

        </Border>

        <!--  // Content //  -->
        <Grid Grid.Row="1">
            <!--  // Base Grid //  -->

            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="120" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <!--  // 侧边栏 //  -->

            <Grid HorizontalAlignment="Left">

                <StackPanel Width="228" Height="640">
                    <menu:Btn
                        Command="{Binding CommonPageCommand}"
                        IsChecked="{Binding PType, Converter={StaticResource PreferenceType2BooleanConverter}, ConverterParameter=Common}"
                        Style="{StaticResource BtnStyle}">
                        <TextBlock Style="{StaticResource Text_Style}" Text="{DynamicResource Preference.Navi.Common}" />
                    </menu:Btn>

                    <menu:Btn
                        Command="{Binding HotkeyPageCommand}"
                        IsChecked="{Binding PType, Converter={StaticResource PreferenceType2BooleanConverter}, ConverterParameter=Hotkey}"
                        Style="{StaticResource BtnStyle}">
                        <TextBlock Style="{StaticResource Text_Style}" Text="{DynamicResource Preference.Navi.Hotkey}" />
                    </menu:Btn>

                    <menu:Btn
                        Command="{Binding ServicePageCommand}"
                        IsChecked="{Binding PType, Converter={StaticResource PreferenceType2BooleanConverter}, ConverterParameter=Service}"
                        Style="{StaticResource BtnStyle}">
                        <TextBlock Style="{StaticResource Text_Style}" Text="{DynamicResource Preference.Navi.Service}" />
                    </menu:Btn>

                    <!--<menu:Btn
                        Command="{Binding TranslatorPageCommand}"
                        IsChecked="{Binding PType, Converter={StaticResource PreferenceType2BooleanConverter}, ConverterParameter=Translator}"
                        Style="{StaticResource BtnStyle}">
                        <TextBlock Style="{StaticResource Text_Style}" Text="翻译服务" />
                    </menu:Btn>-->

                    <!--<menu:Btn
                        Command="{Binding OCRPageCommand}"
                        IsChecked="{Binding PType, Converter={StaticResource PreferenceType2BooleanConverter}, ConverterParameter=OCR}"
                        Style="{StaticResource BtnStyle}">
                        <TextBlock Style="{StaticResource Text_Style}" Text="OCR服务" />
                    </menu:Btn>

                    <menu:Btn
                        Command="{Binding TTSPageCommand}"
                        IsChecked="{Binding PType, Converter={StaticResource PreferenceType2BooleanConverter}, ConverterParameter=TTS}"
                        Style="{StaticResource BtnStyle}">
                        <TextBlock Style="{StaticResource Text_Style}" Text="语音服务" />
                    </menu:Btn>-->
                    <!--  //  分隔符号  //  -->
                    <!--<Separator Background="DarkGray" Margin="10,20"/>-->

                    <menu:Btn
                        Command="{Binding ReplacePageCommand}"
                        IsChecked="{Binding PType, Converter={StaticResource PreferenceType2BooleanConverter}, ConverterParameter=Replace}"
                        Style="{StaticResource BtnStyle}">
                        <TextBlock Style="{StaticResource Text_Style}" Text="{DynamicResource Preference.Navi.Replace}" />
                    </menu:Btn>

                    <menu:Btn
                        Command="{Binding FavoritePageCommand}"
                        IsChecked="{Binding PType, Converter={StaticResource PreferenceType2BooleanConverter}, ConverterParameter=Favorite}"
                        Style="{StaticResource BtnStyle}"
                        Visibility="Collapsed">
                        <TextBlock Style="{StaticResource Text_Style}" Text="收藏夹" />
                    </menu:Btn>

                    <menu:Btn
                        Command="{Binding HistoryPageCommand}"
                        IsChecked="{Binding PType, Converter={StaticResource PreferenceType2BooleanConverter}, ConverterParameter=History}"
                        Style="{StaticResource BtnStyle}">
                        <TextBlock Style="{StaticResource Text_Style}" Text="{DynamicResource Preference.Navi.History}" />
                    </menu:Btn>

                    <menu:Btn
                        Command="{Binding BackupPageCommand}"
                        IsChecked="{Binding PType, Converter={StaticResource PreferenceType2BooleanConverter}, ConverterParameter=Backup}"
                        Style="{StaticResource BtnStyle}">
                        <TextBlock Style="{StaticResource Text_Style}" Text="{DynamicResource Preference.Navi.Backup}" />
                    </menu:Btn>

                    <menu:Btn
                        Command="{Binding AboutPageCommand}"
                        IsChecked="{Binding PType, Converter={StaticResource PreferenceType2BooleanConverter}, ConverterParameter=About}"
                        Style="{StaticResource BtnStyle}">
                        <TextBlock Style="{StaticResource Text_Style}" Text="{DynamicResource Preference.Navi.About}" />
                    </menu:Btn>


                    <!--<menu:Btn Style="{StaticResource BtnStyle}"
                              Command="{Binding BomXmlCommand}">
                        <Grid>
                            <Image Source="/Images/002_shoucang.png"
                                   Style="{StaticResource Image_Style}" />
                            <TextBlock Text="BOM XML" Style="{StaticResource Text_Style}" />
                        </Grid>
                    </menu:Btn>-->

                </StackPanel>

            </Grid>

            <Grid Grid.Column="1">

                <ContentControl x:Name="Pages" Content="{Binding CurrentView}" />

            </Grid>

        </Grid>

        <!--  // Notify //  -->
        <view:ToastView
            x:Name="Notify"
            Grid.Row="0"
            Grid.RowSpan="2"
            Margin="0,8,0,0"
            VerticalAlignment="Top"
            Visibility="Collapsed" />
    </Grid>
</Window>