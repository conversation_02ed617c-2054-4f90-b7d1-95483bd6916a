﻿<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:props="clr-namespace:STranslate.Style.Themes">

    <!--  // 主界面 Border //  -->
    <Style x:Key="WindowStyle" TargetType="Border">
        <Setter Property="props:ThemeProps.Background" Value="{DynamicResource BorderBackground}" />
        <Setter Property="props:ThemeProps.BorderBrush" Value="{DynamicResource BorderBrushColor}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="CornerRadius" Value="10" />
    </Style>

</ResourceDictionary>