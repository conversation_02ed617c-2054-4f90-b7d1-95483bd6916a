using CommunityToolkit.Mvvm.ComponentModel;
using Newtonsoft.Json;

namespace STranslate.Model;

#region Chat Request

/// <summary>
/// 聊天请求模型
/// </summary>
public class ChatRequest
{
    [JsonProperty("message")] public string Message { get; set; } = string.Empty;

    [JsonProperty("system_prompt")] public string SystemPrompt { get; set; } = string.Empty;

    [JsonProperty("history")] public List<ChatMessage> History { get; set; } = [];

    [JsonProperty("keep_context")] public bool KeepContext { get; set; } = true;

    public ChatRequest(string message, string systemPrompt = "", List<ChatMessage>? history = null, bool keepContext = true)
    {
        Message = message;
        SystemPrompt = systemPrompt;
        History = history ?? [];
        KeepContext = keepContext;
    }
}

#endregion

#region Chat Message

/// <summary>
/// 聊天消息模型
/// </summary>
public partial class ChatMessage : ObservableObject
{
    [ObservableProperty] private string _role = string.Empty;
    [ObservableProperty] private string _content = string.Empty;
    [ObservableProperty] private DateTime _timestamp = DateTime.Now;

    public ChatMessage() { }

    public ChatMessage(string role, string content)
    {
        Role = role;
        Content = content;
        Timestamp = DateTime.Now;
    }

    /// <summary>
    /// 创建用户消息
    /// </summary>
    /// <param name="content">消息内容</param>
    /// <returns></returns>
    public static ChatMessage User(string content) => new("user", content);

    /// <summary>
    /// 创建助手消息
    /// </summary>
    /// <param name="content">消息内容</param>
    /// <returns></returns>
    public static ChatMessage Assistant(string content) => new("assistant", content);

    /// <summary>
    /// 创建系统消息
    /// </summary>
    /// <param name="content">消息内容</param>
    /// <returns></returns>
    public static ChatMessage System(string content) => new("system", content);
}

#endregion

#region Chat Result

/// <summary>
/// 聊天结果模型，组合TranslationResult以复用现有UI
/// </summary>
public partial class ChatResult : ObservableObject
{
    [ObservableProperty] private List<ChatMessage> _conversationHistory = [];
    [ObservableProperty] private string _model = string.Empty;
    [ObservableProperty] private int _tokenUsage;

    // 组合TranslationResult的属性
    [ObservableProperty] private bool _isSuccess = true;
    [ObservableProperty] private string _result = string.Empty;
    [ObservableProperty] private bool _isTranslateBackSuccess = true;
    [ObservableProperty] private string _translateBackResult = string.Empty;

    public Exception? Exception { get; set; }

    /// <summary>
    /// 成功时使用的构造函数
    /// </summary>
    /// <param name="result">回复内容</param>
    /// <param name="history">对话历史</param>
    /// <param name="model">使用的模型</param>
    /// <param name="tokenUsage">Token使用量</param>
    private ChatResult(string result, List<ChatMessage>? history = null, string model = "", int tokenUsage = 0)
    {
        IsSuccess = true;
        Result = result;
        ConversationHistory = history ?? [];
        Model = model;
        TokenUsage = tokenUsage;
        Exception = null;
    }

    /// <summary>
    /// 失败时使用的构造函数
    /// </summary>
    /// <param name="errorMessage">错误消息</param>
    /// <param name="exception">异常信息</param>
    private ChatResult(string errorMessage, Exception? exception = null)
    {
        IsSuccess = false;
        Result = errorMessage;
        ConversationHistory = [];
        Model = string.Empty;
        TokenUsage = 0;
        Exception = exception;
    }

    /// <summary>
    /// 清空时使用的构造函数
    /// </summary>
    private ChatResult()
    {
        IsSuccess = true;
        Result = string.Empty;
        ConversationHistory = [];
        Model = string.Empty;
        TokenUsage = 0;
        Exception = null;
    }

    /// <summary>
    /// 静态方法用于清空
    /// </summary>
    /// <returns></returns>
    public static ChatResult Reset => new();

    /// <summary>
    /// 静态方法用于创建成功的结果
    /// </summary>
    /// <param name="result">回复内容</param>
    /// <param name="history">对话历史</param>
    /// <param name="model">使用的模型</param>
    /// <param name="tokenUsage">Token使用量</param>
    /// <returns></returns>
    public static ChatResult Success(string result, List<ChatMessage>? history = null, string model = "", int tokenUsage = 0)
    {
        return new ChatResult(result, history, model, tokenUsage);
    }

    /// <summary>
    /// 静态方法用于创建失败的结果
    /// </summary>
    /// <param name="errorMessage">错误消息</param>
    /// <param name="exception">异常信息</param>
    /// <returns></returns>
    public static ChatResult Fail(string errorMessage, Exception? exception = null)
    {
        return new ChatResult(errorMessage, exception);
    }
}

#endregion

#region Service Type Extension

/// <summary>
/// AI助手服务类型枚举扩展
/// </summary>
public enum AIAssistantType
{
    OpenAIAssistant = 1000,
    ClaudeAssistant = 1001,
    OllamaAssistant = 1002,
    DeepSeekAssistant = 1003,
    ChatglmAssistant = 1004,
    GeminiAssistant = 1005,
    AzureOpenAIAssistant = 1006,
    DeerAPIAssistant = 1007,
    OpenRouterAssistant = 1008,
    BaiduBceAssistant = 1009,
    QwenAssistant = 1010,
    CustomAssistant = 1999
}

#endregion
