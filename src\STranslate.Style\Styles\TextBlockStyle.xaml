﻿<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:props="clr-namespace:STranslate.Style.Themes">

    <Style x:Key="LanguageMarkTextBlockStyle" TargetType="TextBlock">
        <Setter Property="props:ThemeProps.Foreground" Value="{DynamicResource TextBlockLangForeground}" />
        <Setter Property="FontSize" Value="{DynamicResource FontSize14}" />
        <Setter Property="VerticalAlignment" Value="Center" />
    </Style>

    <Style TargetType="TextBlock">
        <Setter Property="props:ThemeProps.Foreground" Value="{DynamicResource TextForeground}" />
        <Setter Property="VerticalAlignment" Value="Center" />
    </Style>

    <Style
        x:Key="IconTextBlock"
        BasedOn="{StaticResource {x:Type TextBlock}}"
        TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{DynamicResource IconFont}" />
    </Style>

    <Style x:Key="InfoTextBlock" TargetType="TextBlock">
        <Setter Property="props:ThemeProps.Foreground" Value="{DynamicResource TextForeground}" />
        <Setter Property="FontFamily" Value="{DynamicResource IconFont}" />
        <Setter Property="FontSize" Value="{DynamicResource FontSize14}" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="ToolTipService.InitialShowDelay" Value="1" />
        <Setter Property="Text" Value="&#xe60b;" />
        <Setter Property="Margin" Value="6,0,0,0" />
    </Style>
</ResourceDictionary>