<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="clr-namespace:STranslate.Style.Controls"
    xmlns:model="clr-namespace:STranslate.Model;assembly=STranslate.Model">
    <Style TargetType="{x:Type controls:TermsControl}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type controls:TermsControl}">
                    <ScrollViewer
                        Padding="2"
                        Focusable="False"
                        Template="{DynamicResource ScrollViewerControlTemplate}">
                        <ItemsPresenter />
                    </ScrollViewer>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="ItemTemplate">
            <Setter.Value>
                <DataTemplate DataType="model:Term">
                    <Border
                        Padding="5"
                        BorderBrush="#DDD"
                        BorderThickness="0,0,0,1">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="2*" />
                                <ColumnDefinition Width="2*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <TextBox Text="{Binding SourceText, UpdateSourceTrigger=PropertyChanged}" />

                            <TextBox
                                Grid.Column="1"
                                Margin="5,0,0,0"
                                Text="{Binding TargetText, UpdateSourceTrigger=PropertyChanged}" />

                            <Button
                                Grid.Column="2"
                                Margin="5,0,0,0"
                                Command="{Binding RelativeSource={RelativeSource AncestorType={x:Type controls:TermsControl}}, Path=DeleteCommand}"
                                CommandParameter="{Binding}"
                                Content="&#xe74b;"
                                Style="{DynamicResource ButtonIconStyle}" />
                        </Grid>
                    </Border>
                </DataTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>
