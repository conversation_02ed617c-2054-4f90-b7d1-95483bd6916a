﻿<UserControl x:Class="STranslate.Views.NotifyIcon"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:proxy="clr-namespace:STranslate.Style.Commons;assembly=STranslate.Style"
             xmlns:tb="http://www.hardcodet.net/taskbar"
             xmlns:vm="clr-namespace:STranslate.ViewModels"
             x:Name="NotifyUc"
             d:DataContext="{d:DesignInstance Type=vm:NotifyIconViewModel}"
             d:DesignHeight="450"
             d:DesignWidth="800"
             mc:Ignorable="d">
    <!-- 程序托盘菜单 -->
    <UserControl.Resources>
        <proxy:BindingProxy x:Key="NotifyIcon" Data="{Binding ElementName=NotifyUc}" />
        <proxy:BindingProxy x:Key="MainView" Data="{Binding RelativeSource={RelativeSource AncestorType=Window}}" />
    </UserControl.Resources>
    <tb:TaskbarIcon x:Name="TrayIcon"
                    DoubleClickCommand="{Binding DoubleClickCommand}"
                    DoubleClickCommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                    IconSource="{Binding NIModel.IconSource}"
                    ToolTipText="{Binding NIModel.ToolTip}"
                    TrayContextMenuOpen="TrayIcon_TrayContextMenuOpen">
        <tb:TaskbarIcon.ContextMenu>
            <!-- // 编译出来似乎有点显示Bug，再手动指定一下DataContext // -->
            <ContextMenu x:Name="TrayIconContextMenu"
                         d:DataContext="{d:DesignInstance Type=vm:NotifyIconViewModel}"
                         DataContext="{Binding Source={StaticResource NotifyIcon}, Path=Data.DataContext}">

                <MenuItem Command="{Binding InputTranslateCommand}"
                          CommandParameter="{Binding Source={StaticResource MainView}, Path=Data}"
                          Header="{DynamicResource NotifyIcon.InputTranslate}"
                          Icon="&#xe694;" />

                <MenuItem Command="{Binding ScreenShotTranslateCommand}"
                          CommandParameter="placehold"
                          Header="{DynamicResource NotifyIcon.ScreenShotTranslate}"
                          Icon="&#xe679;" />

                <MenuItem Command="{Binding MousehookTranslateCommand}"
                          CommandParameter="{Binding Source={StaticResource MainView}, Path=Data}"
                          Header="{DynamicResource NotifyIcon.MousehookTranslate}"
                          Icon="&#xeb94;"
                          IsCheckable="{Binding IsMousehook}" />

                <MenuItem Command="{Binding ClipboardMonitorCommand}"
                          CommandParameter="{Binding Source={StaticResource MainView}, Path=Data}"
                          Header="{DynamicResource NotifyIcon.ClipboardMonitor}"
                          Icon="&#xeabc;"
                          IsCheckable="{Binding IsClipboardMonitor}" />

                <Separator Margin="0,2" Style="{DynamicResource SeparatorStyle}" />

                <MenuItem Command="{Binding OCRCommand}"
                          CommandParameter="placehold"
                          Header="{DynamicResource NotifyIcon.OCR}"
                          Icon="&#xe695;" />

                <MenuItem Command="{Binding SilentOCRCommand}"
                          CommandParameter="placehold"
                          Header="{DynamicResource NotifyIcon.SilentOCR}"
                          Icon="&#xe861;" />

                <MenuItem Command="{Binding QRCodeCommand}"
                          CommandParameter="placehold"
                          Header="{DynamicResource NotifyIcon.QRCode}"
                          Icon="&#xe642;" />

                <Separator Margin="0,2" Style="{DynamicResource SeparatorStyle}" />

                <MenuItem Command="{Binding OpenMainWindowCommand}"
                          CommandParameter="{Binding Source={StaticResource MainView}, Path=Data}"
                          Header="{DynamicResource NotifyIcon.OpenMainWindow}"
                          Icon="&#xe65b;" />

                <MenuItem Command="{Binding OpenPreferenceCommand}" Header="{DynamicResource NotifyIcon.OpenPreference}" Icon="&#xe656;" />

                <Separator Margin="0,2" Style="{DynamicResource SeparatorStyle}" />

                <MenuItem Command="{Binding ForbiddenShortcutsCommand}"
                          CommandParameter="{Binding Source={StaticResource MainView}, Path=Data}"
                          Header="{DynamicResource NotifyIcon.ForbiddenShortcuts}"
                          Icon="&#xe615;"
                          IsCheckable="{Binding IsForbiddenShortcuts}" />

                <Separator Margin="0,2" Style="{DynamicResource SeparatorStyle}" />

                <MenuItem Command="{Binding ExitCommand}" Header="{DynamicResource NotifyIcon.Exit}" Icon="&#xe66b;" />
            </ContextMenu>
        </tb:TaskbarIcon.ContextMenu>
    </tb:TaskbarIcon>
</UserControl>