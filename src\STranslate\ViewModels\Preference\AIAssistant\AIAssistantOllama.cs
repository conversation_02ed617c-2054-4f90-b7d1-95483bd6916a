using CommunityToolkit.Mvvm.Input;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using STranslate.Helper;
using STranslate.Log;
using STranslate.Model;
using STranslate.Util;
using System.ComponentModel;
using System.Net.Http;
using System.Text;

namespace STranslate.ViewModels.Preference.AIAssistant;

public partial class AIAssistantOllama : AIAssistantBase
{
    #region Constructor

    public AIAssistantOllama()
        : this(Guid.NewGuid(), "http://localhost:11434", "Ollama助手")
    {
    }

    public AIAssistantOllama(
        Guid guid,
        string url,
        string name = "",
        IconType icon = IconType.OllamaAssistant,
        string appID = "",
        string appKey = "",
        bool isEnabled = true,
        ServiceType type = ServiceType.OllamaAssistant
    )
    {
        Identify = guid;
        Url = url;
        Name = name;
        Icon = icon;
        AppID = appID;
        AppKey = appKey;
        IsEnabled = isEnabled;
        Type = type;
        
        // 设置默认模型
        Model = "llama3.2:latest";
        Models = [
            "llama3.2:latest",
            "llama3.1:latest",
            "qwen2.5:latest",
            "deepseek-r1:latest",
            "gemma2:latest",
            "mistral:latest",
            "codellama:latest"
        ];
    }

    #endregion Constructor

    #region Properties

    [JsonIgnore] public bool KeyHide { get; set; } = false; // Ollama通常不需要API密钥

    #endregion Properties

    #region Test Methods

    [RelayCommand]
    [property: JsonIgnore]
    private async Task TestAsync(CancellationToken token)
    {
        var chatRequest = new ChatRequest("Hello, please respond with 'Test successful!' to confirm the connection.");
        var result = string.Empty;
        
        try
        {
            await ChatAsync(chatRequest, chunk => result += chunk, token);
            ToastHelper.Show("测试成功", WindowType.Preference);
        }
        catch (Exception ex)
        {
            ToastHelper.Show($"测试失败: {ex.Message}", WindowType.Preference);
            LogService.Logger.Error($"[{Name}] 测试失败: {ex.Message}");
        }
    }

    #endregion Test Methods

    #region Interface Implementation

    public override async Task ChatAsync(ChatRequest request, Action<string> onDataReceived, CancellationToken token)
    {
        if (string.IsNullOrEmpty(Url))
            throw new Exception("请先完善配置");

        UriBuilder uriBuilder = new(Url);
        if (uriBuilder.Path == "/")
            uriBuilder.Path = "/api/chat";

        // 选择模型
        var selectedModel = string.IsNullOrEmpty(Model) ? "llama3.2:latest" : Model.Trim();

        // 构建消息列表
        var messages = new List<object>();
        
        // 添加系统提示词
        var systemPrompt = GetActiveSystemPrompt();
        if (!string.IsNullOrWhiteSpace(systemPrompt))
        {
            messages.Add(new { role = "system", content = systemPrompt });
        }

        // 添加历史记录
        if (KeepContext && ChatHistory.Count > 0)
        {
            foreach (var msg in ChatHistory.TakeLast(MaxHistoryLength))
            {
                messages.Add(new { role = msg.Role, content = msg.Content });
            }
        }

        // 添加当前用户消息
        messages.Add(new { role = "user", content = request.Message });

        // 构建请求数据
        var reqData = new
        {
            model = selectedModel,
            messages = messages,
            stream = true,
            options = new
            {
                temperature = Math.Clamp(Temperature, 0, 2)
            }
        };

        var jsonData = JsonConvert.SerializeObject(reqData);
        var assistantResponse = string.Empty;

        try
        {
            await HttpUtil.PostAsync(
                uriBuilder.Uri,
                jsonData,
                string.Empty, // Ollama通常不需要API密钥
                msg =>
                {
                    if (string.IsNullOrEmpty(msg?.Trim()))
                        return;

                    try
                    {
                        // 解析JSON数据
                        var parsedData = JsonConvert.DeserializeObject<JObject>(msg);
                        if (parsedData == null) return;

                        // 检查是否完成
                        if (bool.TryParse(parsedData["done"]?.ToString() ?? "", out var done) && done)
                            return;

                        // 提取content的值
                        var contentValue = parsedData["message"]?["content"]?.ToString();
                        if (!string.IsNullOrEmpty(contentValue))
                        {
                            assistantResponse += contentValue;
                            onDataReceived?.Invoke(contentValue);
                        }
                    }
                    catch
                    {
                        // 忽略解析错误
                    }
                },
                token
            ).ConfigureAwait(false);

            // 对话完成后，添加到历史记录
            if (KeepContext && !string.IsNullOrWhiteSpace(assistantResponse))
            {
                AddToHistory(ChatMessage.User(request.Message));
                AddToHistory(ChatMessage.Assistant(assistantResponse));
            }
        }
        catch (OperationCanceledException)
        {
            throw;
        }
        catch (HttpRequestException ex) when (ex.StatusCode == null)
        {
            var msg = $"请检查Ollama服务是否正在运行: {Name} ({Url}).\n{ex.Message}";
            throw new HttpRequestException(msg);
        }
        catch (HttpRequestException)
        {
            throw;
        }
        catch (Exception ex)
        {
            var msg = ex.Message;
            if (ex.InnerException is HttpRequestException httpEx)
            {
                if (httpEx.Data.Contains("StatusCode"))
                {
                    var statusCode = httpEx.Data["StatusCode"]!.ToString();
                    msg = $"请求失败，状态码: {statusCode}";
                }
            }
            throw new Exception(msg);
        }
    }

    public override ITranslator Clone()
    {
        return new AIAssistantOllama
        {
            Identify = Identify,
            Type = Type,
            IsEnabled = IsEnabled,
            Icon = Icon,
            Name = Name,
            Url = Url,
            Data = TranslationResult.Reset,
            AppID = AppID,
            AppKey = AppKey,
            AutoExecute = AutoExecute,
            AutoExecuteTranslateBack = AutoExecuteTranslateBack,
            IsExecuting = IsExecuting,
            IsTranslateBackExecuting = IsTranslateBackExecuting,
            Temperature = Temperature,
            Model = Model,
            Models = new BindingList<string>(Models.ToList()),
            UserDefinePrompts = new BindingList<UserDefinePrompt>(UserDefinePrompts.Select(x => x.Clone()).Cast<UserDefinePrompt>().ToList()),
            SystemPrompt = SystemPrompt,
            ChatHistory = new BindingList<ChatMessage>(),
            MaxHistoryLength = MaxHistoryLength,
            KeepContext = KeepContext,
            KeyHide = KeyHide
        };
    }

    #endregion Interface Implementation
}
