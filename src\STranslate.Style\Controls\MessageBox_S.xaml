﻿<Window
    x:Class="STranslate.Style.Controls.MessageBox_S"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:props="clr-namespace:STranslate.Style.Themes"
    Title="MessageBox"
    Width="380"
    MinHeight="200"
    props:ThemeProps.Background="{DynamicResource NavigationBackground}"
    props:ThemeProps.BorderBrush="{DynamicResource BorderBrushColor}"
    BorderThickness="1"
    FontFamily="{DynamicResource UserFont}"
    Icon="{DynamicResource Icon}"
    ResizeMode="NoResize"
    SizeToContent="Height"
    Topmost="True"
    WindowChrome.IsHitTestVisibleInChrome="True"
    WindowStartupLocation="CenterScreen"
    mc:Ignorable="d">

    <WindowChrome.WindowChrome>
        <WindowChrome />
    </WindowChrome.WindowChrome>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="35" />
            <RowDefinition Height="*" />
            <RowDefinition Height="50" />
        </Grid.RowDefinitions>

        <!--  标题栏  -->
        <Grid
            Grid.Row="0"
            props:ThemeProps.Background="{DynamicResource NavigationBackground}"
            MouseDown="Header_MouseDown">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="50" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="36" />
            </Grid.ColumnDefinitions>

            <!--  应用图标  -->
            <Image
                Grid.Column="0"
                Width="24"
                Margin="10,0"
                HorizontalAlignment="Left"
                VerticalAlignment="Center"
                Source="{DynamicResource STranslate}" />

            <!--  标题文本  -->
            <TextBlock
                x:Name="TitleTxt"
                Grid.Column="1"
                VerticalAlignment="Center"
                props:ThemeProps.Foreground="{DynamicResource NavigationForeground}"
                FontSize="{DynamicResource FontSize20}"
                Text="" />

            <!--  关闭按钮  -->
            <Button
                x:Name="CloseButton"
                Grid.Column="3"
                Width="30"
                Height="30"
                Margin="3"
                Click="CloseButton_Click"
                Content="&#xE8BB;"
                Style="{DynamicResource ButtonCloseStyle}" />
        </Grid>

        <!--  消息文本  -->
        <ScrollViewer
            Grid.Row="1"
            MaxHeight="200"
            Margin="15"
            VerticalScrollBarVisibility="Auto">
            <TextBlock
                x:Name="Messages"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="{DynamicResource FontSize18}"
                Text="Message"
                TextWrapping="Wrap" />
        </ScrollViewer>

        <!--  按钮区域  -->
        <StackPanel
            Grid.Row="2"
            Margin="0,0,10,10"
            HorizontalAlignment="Right"
            Orientation="Horizontal">

            <!--  第一个按钮 (Yes/OK)  -->
            <Button
                x:Name="FirstButton"
                Height="30"
                MinWidth="60"
                Margin="20,0,0,0"
                Content="{DynamicResource Confirm}"
                IsDefault="True" />

            <!--  第二个按钮 (No/Cancel)  -->
            <Button
                x:Name="SecondButton"
                Height="30"
                MinWidth="60"
                Margin="10,0,0,0"
                Content="{DynamicResource Cancel}"
                IsCancel="True" />

            <!--  第三个按钮 (Cancel for YesNoCancel)  -->
            <Button
                x:Name="ThirdButton"
                Height="30"
                MinWidth="60"
                Margin="10,0,20,0"
                Content="{DynamicResource Cancel}"
                Visibility="Collapsed" />
        </StackPanel>
    </Grid>
</Window>