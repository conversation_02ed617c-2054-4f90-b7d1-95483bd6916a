﻿<UserControl
    x:Class="STranslate.Views.LangView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:common="clr-namespace:STranslate.Style.Commons;assembly=STranslate.Style"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:model="clr-namespace:STranslate.Model;assembly=STranslate.Model"
    xmlns:vm="clr-namespace:STranslate.ViewModels"
    d:DataContext="{d:DesignInstance Type=vm:MainViewModel}"
    d:DesignHeight="450"
    d:DesignWidth="800"
    mc:Ignorable="d">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="2*" />
            <ColumnDefinition />
            <ColumnDefinition Width="2*" />
        </Grid.ColumnDefinitions>

        <!--  // SourceLang //  -->
        <ComboBox
            x:Name="SourceLangCb"
            Grid.Column="0"
            MinWidth="130"
            HorizontalAlignment="Right"
            common:LangAwareSelector.IsLangAware="True"
            DisplayMemberPath="Description"
            SelectedValue="{Binding SourceLang, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
            SelectedValuePath="Value">
            <ComboBox.ItemsSource>
                <MultiBinding Converter="{StaticResource MultiLangFilterConverter}">
                    <Binding Source="{common:LangEnumeration {x:Type model:LangEnum}}" />
                    <Binding Path="InputVM.OftenUsedLang" />
                </MultiBinding>
            </ComboBox.ItemsSource>
        </ComboBox>

        <!--  // Exchange Button //  -->
        <Button
            Grid.Column="1"
            Command="{Binding ExchangeSourceTargetCommand}"
            Content="&#xe665;"
            Style="{DynamicResource ButtonIconStyle}" />

        <!--  // TargetLang //  -->
        <ComboBox
            x:Name="TargetLangCb"
            Grid.Column="2"
            MinWidth="130"
            HorizontalAlignment="Left"
            common:LangAwareSelector.IsLangAware="True"
            DisplayMemberPath="Description"
            SelectedValue="{Binding TargetLang, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
            SelectedValuePath="Value">
            <ComboBox.ItemsSource>
                <MultiBinding Converter="{StaticResource MultiLangFilterConverter}">
                    <Binding Source="{common:LangEnumeration {x:Type model:LangEnum}}" />
                    <Binding Path="InputVM.OftenUsedLang" />
                </MultiBinding>
            </ComboBox.ItemsSource>
        </ComboBox>
    </Grid>
</UserControl>