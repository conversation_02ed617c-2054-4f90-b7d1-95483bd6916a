﻿<UserControl
    x:Class="STranslate.Views.Preference.TTSPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dd="urn:gong-wpf-dragdrop"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:props="clr-namespace:STranslate.Style.Themes;assembly=STranslate.Style"
    xmlns:vm="clr-namespace:STranslate.ViewModels.Preference"
    d:DataContext="{d:DesignInstance Type=vm:TTSViewModel}"
    d:DesignHeight="450"
    d:DesignWidth="800"
    d:FontFamily="{DynamicResource UserFont}"
    d:FontSize="{DynamicResource FontSize18}"
    Style="{StaticResource ResourceKey=Page_Style}"
    mc:Ignorable="d">
    <Border CornerRadius="5">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <!--  // Header //  -->
            <StackPanel Margin="10,5">
                <!--<TextBlock FontSize="{DynamicResource FontSize20}" Text="{DynamicResource Service.TTS.Title}" />-->
                <TextBlock FontSize="{DynamicResource FontSize18}" Text="{DynamicResource Service.TTS.SubTitle}" />
            </StackPanel>

            <Grid Grid.Row="1">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*" />
                    <RowDefinition Height="60" />
                </Grid.RowDefinitions>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*" MaxWidth="300" />
                        <ColumnDefinition Width="3*" />
                    </Grid.ColumnDefinitions>

                    <!--  // TTS //  -->
                    <Border
                        Margin="10"
                        props:ThemeProps.Background="{DynamicResource BorderBackground}"
                        props:ThemeProps.BorderBrush="{DynamicResource BorderBrushColor}"
                        BorderThickness="1"
                        CornerRadius="6">
                        <ListBox
                            x:Name="TTSListBox"
                            dd:DragDrop.IsDragSource="True"
                            dd:DragDrop.IsDropTarget="True"
                            dd:DragDrop.UseDefaultDragAdorner="True"
                            Background="Transparent"
                            BorderThickness="0"
                            ItemsSource="{Binding CurTTSServiceList}"
                            ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                            ScrollViewer.VerticalScrollBarVisibility="Auto"
                            SelectedIndex="{Binding SelectedIndex}">
                            <i:Interaction.Triggers>
                                <i:EventTrigger EventName="SelectionChanged">
                                    <i:InvokeCommandAction Command="{Binding TogglePageCommand}" CommandParameter="{Binding ElementName=TTSListBox, Path=SelectedItem}" />
                                </i:EventTrigger>
                            </i:Interaction.Triggers>
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <Border
                                        x:Name="TTSServiceControl"
                                        Height="50"
                                        Style="{DynamicResource BorderInOutputStyle}">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="40" />
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="auto" MaxWidth="48" />
                                                <ColumnDefinition Width="50" />
                                            </Grid.ColumnDefinitions>

                                            <Image Width="20" Source="{Binding Icon, Converter={StaticResource String2IconConverter}}" />

                                            <TextBlock
                                                Grid.Column="1"
                                                VerticalAlignment="Center"
                                                Text="{Binding Name}"
                                                TextTrimming="CharacterEllipsis" />

                                            <Border
                                                Grid.Column="2"
                                                Margin="0,0,6,0"
                                                Padding="1"
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                Background="{Binding Type, Converter={StaticResource ServiceType2BrushConverter}}"
                                                CornerRadius="6">
                                                <TextBlock
                                                    Padding="2,0"
                                                    props:ThemeProps.Foreground="{DynamicResource ServiceTypeForeground}"
                                                    FontSize="{DynamicResource FontSize12}"
                                                    Text="{Binding Type, Converter={StaticResource ServiceType2StringConverter}}" />
                                            </Border>

                                            <ToggleButton Grid.Column="3" IsChecked="{Binding IsEnabled}" />
                                        </Grid>
                                    </Border>
                                    <DataTemplate.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter TargetName="TTSServiceControl" Property="props:ThemeProps.Background" Value="{DynamicResource BtnMouseOverBackground}" />
                                        </Trigger>
                                        <DataTrigger Binding="{Binding Path=IsSelected, RelativeSource={RelativeSource FindAncestor, AncestorType=ListBoxItem}}" Value="True">
                                            <Setter TargetName="TTSServiceControl" Property="props:ThemeProps.Background" Value="{DynamicResource BtnPressedBackground}" />
                                        </DataTrigger>
                                    </DataTemplate.Triggers>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>
                    </Border>

                    <!--  // Content //  -->
                    <ContentControl
                        Grid.Column="1"
                        Margin="0,10,10,10"
                        Content="{Binding TtsServiceContent}" />
                </Grid>

                <!--  // Footer //  -->
                <Grid Grid.Row="1" Margin="20,0">

                    <!--  // Add Service //  -->
                    <Grid>
                        <ToggleButton
                            x:Name="BTN_Add"
                            Width="50"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Center"
                            HorizontalContentAlignment="Center"
                            VerticalContentAlignment="Center"
                            props:ThemeProps.BorderBrush="{DynamicResource BorderBrushColor}"
                            BorderThickness="1"
                            Content="+"
                            Style="{DynamicResource ToggleButtonIconStyle}">
                            <i:Interaction.Triggers>
                                <i:EventTrigger EventName="Checked">
                                    <i:ChangePropertyAction
                                        PropertyName="IsOpen"
                                        TargetObject="{Binding ElementName=Popup_Add}"
                                        Value="True" />
                                </i:EventTrigger>
                            </i:Interaction.Triggers>
                        </ToggleButton>
                        <Popup
                            x:Name="Popup_Add"
                            MinWidth="80"
                            MinHeight="30"
                            AllowsTransparency="True"
                            Placement="Bottom"
                            PlacementTarget="{Binding ElementName=BTN_Add}"
                            PopupAnimation="Slide"
                            StaysOpen="False">
                            <i:Interaction.Triggers>
                                <i:EventTrigger EventName="Closed">
                                    <i:ChangePropertyAction
                                        PropertyName="IsChecked"
                                        TargetObject="{Binding ElementName=BTN_Add}"
                                        Value="{Binding ElementName=BTN_Add, Path=IsMouseOver}" />
                                </i:EventTrigger>
                            </i:Interaction.Triggers>
                            <Border Style="{DynamicResource BorderStyle}">
                                <ListBox
                                    Name="TTSServiceListBox"
                                    Background="Transparent"
                                    BorderThickness="0"
                                    ItemsSource="{Binding TtsServices}"
                                    ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                                    ScrollViewer.VerticalScrollBarVisibility="Hidden">
                                    <i:Interaction.Triggers>
                                        <i:EventTrigger EventName="PreviewMouseLeftButtonUp">
                                            <i:InvokeCommandAction Command="{Binding AddCommand}">
                                                <i:InvokeCommandAction.CommandParameter>
                                                    <MultiBinding Converter="{StaticResource MultiValue2ListConverter}">
                                                        <Binding ElementName="TTSServiceListBox" Path="SelectedItem" />
                                                        <Binding ElementName="Popup_Add" />
                                                    </MultiBinding>
                                                </i:InvokeCommandAction.CommandParameter>
                                            </i:InvokeCommandAction>
                                        </i:EventTrigger>
                                    </i:Interaction.Triggers>
                                    <ListBox.ItemTemplate>
                                        <DataTemplate>
                                            <Border x:Name="TTSServiceControl" Style="{DynamicResource BorderPopupStyle}">
                                                <Grid Margin="10,5">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="40" />
                                                        <ColumnDefinition Width="*" />
                                                        <ColumnDefinition Width="40" />
                                                    </Grid.ColumnDefinitions>

                                                    <Image Width="20" Source="{Binding Icon, Converter={StaticResource String2IconConverter}}" />

                                                    <TextBlock
                                                        Grid.Column="1"
                                                        VerticalAlignment="Center"
                                                        Text="{Binding Name}" />

                                                    <Border
                                                        Grid.Column="2"
                                                        Padding="1"
                                                        HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"
                                                        Background="{Binding Type, Converter={StaticResource ServiceType2BrushConverter}}"
                                                        CornerRadius="6">
                                                        <TextBlock
                                                            Padding="2,0"
                                                            props:ThemeProps.Foreground="{DynamicResource ServiceTypeForeground}"
                                                            FontSize="{DynamicResource FontSize12}"
                                                            Text="{Binding Type, Converter={StaticResource ServiceType2StringConverter}}" />
                                                    </Border>
                                                </Grid>
                                            </Border>
                                            <DataTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter TargetName="TTSServiceControl" Property="props:ThemeProps.Background" Value="{DynamicResource BtnMouseOverBackground}" />
                                                </Trigger>
                                            </DataTemplate.Triggers>
                                        </DataTemplate>
                                    </ListBox.ItemTemplate>
                                </ListBox>
                            </Border>
                        </Popup>

                        <Button
                            x:Name="BTN_Del"
                            Width="50"
                            Margin="54,0,0,0"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Center"
                            HorizontalContentAlignment="Center"
                            VerticalContentAlignment="Center"
                            props:ThemeProps.BorderBrush="{DynamicResource BorderBrushColor}"
                            BorderThickness="1"
                            Command="{Binding DeleteCommand}"
                            CommandParameter="{Binding ElementName=TTSListBox, Path=SelectedItem}"
                            Content="-"
                            Style="{DynamicResource ButtonIconStyle}" />
                    </Grid>

                    <!--  // Save //  -->
                    <Grid>
                        <Button
                            Margin="0,0,128,0"
                            HorizontalAlignment="Right"
                            HorizontalContentAlignment="Center"
                            VerticalContentAlignment="Center"
                            Content="&#xe994; "
                            Style="{DynamicResource ButtonIconStyle}"
                            Visibility="Collapsed" />
                        <Button
                            Width="60"
                            Margin="0,0,76,0"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Center"
                            HorizontalContentAlignment="Center"
                            VerticalContentAlignment="Center"
                            Command="{Binding ResetCommand}"
                            Content="{DynamicResource Preference.Reset}" />
                        <Button
                            Width="60"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Center"
                            HorizontalContentAlignment="Center"
                            VerticalContentAlignment="Center"
                            Command="{Binding SaveCommand}"
                            Content="{DynamicResource Preference.Save}" />
                    </Grid>
                </Grid>
            </Grid>
        </Grid>
    </Border>
</UserControl>