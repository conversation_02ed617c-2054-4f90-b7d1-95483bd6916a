﻿<UserControl
    x:Class="STranslate.Views.Preference.Translator.TranslatorEcdictPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:common="clr-namespace:STranslate.Style.Commons;assembly=STranslate.Style"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:model="clr-namespace:STranslate.Model;assembly=STranslate.Model"
    xmlns:props="clr-namespace:STranslate.Style.Themes;assembly=STranslate.Style"
    xmlns:vm="clr-namespace:STranslate.ViewModels.Preference.Translator"
    d:DataContext="{d:DesignInstance Type=vm:TranslatorEcdict}"
    d:DesignHeight="450"
    d:DesignWidth="800"
    Background="Transparent"
    FontSize="{DynamicResource FontSize18}"
    mc:Ignorable="d">
    <Border
        Padding="10,0,0,0"
        props:ThemeProps.BorderBrush="{DynamicResource BorderBrushColor}"
        BorderThickness="1"
        CornerRadius="4">
        <StackPanel>
            <Grid Margin="0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="80" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <TextBlock Text="{DynamicResource Service.Name}" />

                <common:PlaceholderTextBox
                    Grid.Column="1"
                    MinWidth="160"
                    HorizontalAlignment="Left"
                    Placeholder="简明英汉词典"
                    Text="{Binding Name, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" />
            </Grid>

            <Grid Margin="0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="80" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <TextBlock Text="{DynamicResource Service.Type}" />

                <Border
                    Grid.Column="1"
                    HorizontalAlignment="Left"
                    Background="{Binding Type, Converter={StaticResource ServiceType2BrushConverter}}"
                    CornerRadius="5">
                    <TextBlock
                        Margin="5,2"
                        VerticalAlignment="Center"
                        props:ThemeProps.Foreground="{DynamicResource ServiceTypeForeground}"
                        Text="{Binding Type, Converter={StaticResource ServiceTypeConverter}}" />
                </Border>
            </Grid>

            <Grid Margin="0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="80" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <TextBlock Text="{DynamicResource Service.AutoExecute}" ToolTip="{DynamicResource Service.AutoExecute.Tooltip}" />

                <ToggleButton
                    Grid.Column="1"
                    HorizontalAlignment="Left"
                    IsChecked="{Binding AutoExecute}" />
            </Grid>

            <Grid Margin="0,10" Visibility="Collapsed">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="80" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <TextBlock Text="{DynamicResource Service.AutoTransBack}" ToolTip="{DynamicResource Service.AutoTransBack.Tooltip}" />

                <ToggleButton
                    Grid.Column="1"
                    HorizontalAlignment="Left"
                    IsChecked="{Binding AutoExecuteTranslateBack}" />
            </Grid>

            <Grid Margin="0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="80" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <TextBlock Text="{DynamicResource Service.Icon}" />

                <ComboBox
                    Grid.Column="1"
                    HorizontalAlignment="Left"
                    BorderThickness="1"
                    ItemsSource="{Binding Icons}"
                    SelectedValue="{Binding Icon}"
                    SelectedValuePath="Key"
                    Style="{DynamicResource IconComboBoxStyle}" />
            </Grid>

            <Grid Margin="0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="80" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <TextBlock Text="{DynamicResource Service.Translator.ECDict.Home}" />
                <TextBlock
                    Grid.Column="1"
                    Margin="10,0"
                    ToolTip="{DynamicResource Service.OpenInBrower}">
                    <Hyperlink Click="Hyperlink_Click">
                        <ContentControl Content="{DynamicResource Service.Translator.ECDict.ProjectHome}" />
                    </Hyperlink>
                </TextBlock>
            </Grid>

            <Grid Margin="0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="80" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <TextBlock Text="{DynamicResource Service.OCR.PaddleOCR.Data}" />
                <ProgressBar
                    Grid.Column="1"
                    Margin="110,0,0,0"
                    FontSize="{DynamicResource FontSize16}"
                    Maximum="100"
                    Visibility="{Binding IsShowProcessBar, Converter={StaticResource BooleanToVisibilityConverter}}"
                    Value="{Binding ProcessValue}" />
                <Button
                    Grid.Column="1"
                    Width="100"
                    HorizontalAlignment="Left"
                    Command="{Binding DownloadResourceCommand}"
                    Content="{DynamicResource Service.OCR.PaddleOCR.Download}"
                    Visibility="{Binding HasDB, Converter={StaticResource BooleanToVisibilityReverseConverter}}" />
                <Button
                    Grid.Column="1"
                    Width="100"
                    HorizontalAlignment="Left"
                    Command="{Binding DownloadResourceCancelCommand}"
                    Content="{DynamicResource Cancel}"
                    Visibility="{Binding IsShowProcessBar, Converter={StaticResource BooleanToVisibilityConverter}}" />
                <Button
                    Grid.Column="1"
                    Width="100"
                    HorizontalAlignment="Left"
                    Command="{Binding CheckResourceCommand}"
                    Content="{DynamicResource Service.OCR.PaddleOCR.Check}"
                    Visibility="{Binding HasDB, Converter={StaticResource BooleanToVisibilityConverter}}" />
                <Button
                    Grid.Column="1"
                    Width="60"
                    Margin="110,0,0,0"
                    HorizontalAlignment="Left"
                    Command="{Binding DeleteResourceCommand}"
                    Content="{DynamicResource Service.Delete}"
                    Visibility="{Binding HasDB, Converter={StaticResource BooleanToVisibilityConverter}}" />
                <TextBlock
                    Grid.Column="1"
                    Margin="180,0,0,0"
                    Text="{Binding DbFileSize}"
                    Visibility="{Binding HasDB, Converter={StaticResource BooleanToVisibilityConverter}}" />
            </Grid>

            <Grid Margin="0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="80" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <TextBlock Text="{DynamicResource Service.Description}" />
                <TextBlock Grid.Column="1" Text="{DynamicResource Service.Translator.ECDict.Description}" />
                <TextBlock
                    Grid.Column="1"
                    Margin="280,0,0,0"
                    Style="{DynamicResource InfoTextBlock}"
                    ToolTip="{DynamicResource Service.Translator.ECDict.Tip}" />
            </Grid>
        </StackPanel>
    </Border>
</UserControl>