﻿<UserControl
    x:Class="STranslate.Views.Preference.OCR.PaddleOCRPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:common="clr-namespace:STranslate.Style.Commons;assembly=STranslate.Style"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:model="clr-namespace:STranslate.Model;assembly=STranslate.Model"
    xmlns:props="clr-namespace:STranslate.Style.Themes;assembly=STranslate.Style"
    xmlns:vm="clr-namespace:STranslate.ViewModels.Preference.OCR"
    d:DataContext="{d:DesignInstance Type=vm:PaddleOCR}"
    d:DesignHeight="450"
    d:DesignWidth="800"
    Background="Transparent"
    FontSize="{DynamicResource FontSize18}"
    mc:Ignorable="d">
    <Border
        Padding="10,0,0,0"
        props:ThemeProps.BorderBrush="{DynamicResource BorderBrushColor}"
        BorderThickness="1"
        CornerRadius="4">
        <StackPanel>
            <Grid Margin="0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="80" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <TextBlock Text="{DynamicResource Service.Name}" />

                <common:PlaceholderTextBox
                    Grid.Column="1"
                    MinWidth="160"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center"
                    Placeholder="PaddleOCR"
                    Text="{Binding Name, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" />
            </Grid>

            <Grid Margin="0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="80" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <TextBlock Text="{DynamicResource Service.Icon}" />

                <ComboBox
                    Grid.Column="1"
                    HorizontalAlignment="Left"
                    BorderThickness="1"
                    ItemsSource="{Binding Icons}"
                    SelectedValue="{Binding Icon}"
                    SelectedValuePath="Key"
                    Style="{DynamicResource IconComboBoxStyle}" />
            </Grid>

            <Grid Margin="0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="80" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <TextBlock Text="{DynamicResource Service.OCR.PaddleOCR.Data}" />

                <Button
                    Grid.Column="1"
                    Width="100"
                    HorizontalAlignment="Left"
                    Command="{Binding DownloadCommand}"
                    Content="{DynamicResource Service.OCR.PaddleOCR.Download}"
                    Visibility="{Binding HasData, Converter={StaticResource BooleanToVisibilityReverseConverter}}" />
                <Button
                    Grid.Column="1"
                    Width="100"
                    HorizontalAlignment="Left"
                    Command="{Binding DownloadCancelCommand}"
                    Content="{DynamicResource Cancel}"
                    Visibility="{Binding IsShowProcessBar, Converter={StaticResource BooleanToVisibilityConverter}}" />

                <ProgressBar
                    Grid.Column="1"
                    Margin="110,0,0,0"
                    FontSize="{DynamicResource FontSize16}"
                    Maximum="100"
                    Visibility="{Binding IsShowProcessBar, Converter={StaticResource BooleanToVisibilityConverter}}"
                    Value="{Binding ProcessValue}" />
                <Button
                    Grid.Column="1"
                    Width="100"
                    HorizontalAlignment="Left"
                    Command="{Binding CheckDataCommand}"
                    Content="{DynamicResource Service.OCR.PaddleOCR.Check}"
                    Visibility="{Binding HasData, Converter={StaticResource BooleanToVisibilityConverter}}" />
                <Button
                    Grid.Column="1"
                    Width="60"
                    Margin="110,0,0,0"
                    HorizontalAlignment="Left"
                    Command="{Binding DeleteDataCommand}"
                    Content="{DynamicResource Service.Delete}"
                    Visibility="Collapsed" />
                <!--  Visibility="{Binding HasData, Converter={StaticResource BooleanToVisibilityConverter}}"  -->
            </Grid>

            <Grid Margin="0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="80" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <TextBlock Text="Other: " />
                <TextBlock
                    Grid.Column="1"
                    Margin="10,0"
                    ToolTip="{DynamicResource Service.OpenInBrower}">
                    <Hyperlink Click="Hyperlink_Click">
                        <ContentControl Content="{DynamicResource Service.EnterOfficialWebsite}" />
                    </Hyperlink>
                </TextBlock>
            </Grid>
        </StackPanel>
    </Border>
</UserControl>