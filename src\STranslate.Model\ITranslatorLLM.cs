﻿using System.ComponentModel;

namespace STranslate.Model;

public interface ITranslatorLLM : ITranslator
{
    double Temperature { get; set; }

    string Model { get; set; }

    BindingList<string> Models { get; set; }

    /// <summary>
    ///     手动通知属性更新
    /// </summary>
    /// <param name="name"></param>
    void ManualPropChanged(params string[] name);


    Task TranslateAsync(object request, Action<string> onDataReceived, CancellationToken token);
}

/// <summary>
/// AI助手接口，继承自ITranslatorLLM以复用现有功能
/// </summary>
public interface IAIAssistant : ITranslatorLLM
{
    /// <summary>
    /// 系统提示词
    /// </summary>
    string SystemPrompt { get; set; }

    /// <summary>
    /// 对话历史记录
    /// </summary>
    BindingList<ChatMessage> ChatHistory { get; set; }

    /// <summary>
    /// 最大对话历史长度
    /// </summary>
    int MaxHistoryLength { get; set; }

    /// <summary>
    /// 是否保持对话上下文
    /// </summary>
    bool KeepContext { get; set; }

    /// <summary>
    /// AI助手对话方法
    /// </summary>
    /// <param name="request">聊天请求</param>
    /// <param name="onDataReceived">流式数据回调</param>
    /// <param name="token">取消令牌</param>
    /// <returns></returns>
    Task ChatAsync(ChatRequest request, Action<string> onDataReceived, CancellationToken token);

    /// <summary>
    /// 清空对话历史
    /// </summary>
    void ClearHistory();

    /// <summary>
    /// 添加消息到历史记录
    /// </summary>
    /// <param name="message">消息</param>
    void AddToHistory(ChatMessage message);
}
