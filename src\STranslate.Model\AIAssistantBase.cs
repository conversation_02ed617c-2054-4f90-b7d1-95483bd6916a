using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Newtonsoft.Json;
using System.ComponentModel;

namespace STranslate.Model;

/// <summary>
/// AI助手服务基类，提供通用的对话管理功能
/// </summary>
public abstract partial class AIAssistantBase : ObservableObject, IAIAssistant
{
    #region Properties

    [ObservableProperty] private Guid _identify = Guid.NewGuid();
    [ObservableProperty] private ServiceType _type;
    [ObservableProperty] private IconType _icon;
    [ObservableProperty] private bool _isEnabled = true;
    [ObservableProperty] private string _name = string.Empty;
    [ObservableProperty] private string _url = string.Empty;
    [ObservableProperty] private bool _autoExecute = true;
    [ObservableProperty] private bool _autoExecuteTranslateBack = false;
    [ObservableProperty] private TranslationResult _data = TranslationResult.Reset;
    [ObservableProperty] private string _appID = string.Empty;
    [ObservableProperty] private string _appKey = string.Empty;
    [ObservableProperty] private bool _isExecuting = false;
    [ObservableProperty] private bool _isTranslateBackExecuting = false;

    // ITranslatorLLM properties
    [JsonIgnore][ObservableProperty] private double _temperature = 0.7;

    [JsonIgnore] private string _model = string.Empty;
    public virtual string Model
    {
        get => _model;
        set => SetProperty(ref _model, value);
    }

    [JsonIgnore] private BindingList<string> _models = [];
    public virtual BindingList<string> Models
    {
        get => _models;
        set => SetProperty(ref _models, value);
    }

    // 兼容现有的UserDefinePrompts，但用于AI助手的预设对话模板
    [JsonIgnore]
    private BindingList<UserDefinePrompt> _userDefinePrompts = [
        new UserDefinePrompt(
            "通用助手",
            [
                new Prompt("system", "You are a helpful AI assistant. Please provide accurate, helpful, and concise responses to user questions.")
            ],
            true
        ),
        new UserDefinePrompt(
            "编程助手",
            [
                new Prompt("system", "You are an expert programming assistant. Help users with coding questions, debugging, and best practices. Provide clear explanations and working code examples.")
            ]
        ),
        new UserDefinePrompt(
            "写作助手",
            [
                new Prompt("system", "You are a professional writing assistant. Help users improve their writing, provide suggestions for clarity and style, and assist with various writing tasks.")
            ]
        )
    ];
    public virtual BindingList<UserDefinePrompt> UserDefinePrompts
    {
        get => _userDefinePrompts;
        set => SetProperty(ref _userDefinePrompts, value);
    }

    // IAIAssistant properties
    [ObservableProperty] private string _systemPrompt = "You are a helpful AI assistant. Please provide accurate, helpful, and concise responses.";
    [ObservableProperty] private BindingList<ChatMessage> _chatHistory = [];
    [ObservableProperty] private int _maxHistoryLength = 20;
    [ObservableProperty] private bool _keepContext = true;

    #endregion

    #region Constructor

    protected AIAssistantBase()
    {
        // 初始化默认系统提示词
        SystemPrompt = "You are a helpful AI assistant. Please provide accurate, helpful, and concise responses.";

        // 初始化对话历史
        ChatHistory = [];

        // 设置默认参数
        MaxHistoryLength = 20;
        KeepContext = true;
        Temperature = 0.7;
    }

    #endregion

    #region Commands

    [RelayCommand]
    [property: JsonIgnore]
    private void SelectedPrompt(List<object> obj)
    {
        var userDefinePrompt = (UserDefinePrompt)obj.First();
        foreach (var item in UserDefinePrompts) item.Enabled = false;
        userDefinePrompt.Enabled = true;

        // 更新系统提示词
        var systemPrompt = userDefinePrompt.Prompts.FirstOrDefault(p => p.Role == "system");
        if (systemPrompt != null)
        {
            SystemPrompt = systemPrompt.Content;
        }

        ManualPropChanged(nameof(UserDefinePrompts), nameof(SystemPrompt));
    }

    [RelayCommand]
    [property: JsonIgnore]
    private void ClearChatHistory()
    {
        ClearHistory();
    }

    #endregion

    #region Abstract Methods

    /// <summary>
    /// 语言转换器，子类需要实现（AI助手通常不需要语言转换，返回null即可）
    /// </summary>
    /// <param name="lang">语言枚举</param>
    /// <returns>转换后的语言代码</returns>
    public virtual string? LangConverter(LangEnum lang) => null;

    /// <summary>
    /// 翻译方法，为了兼容现有接口，AI助手可以将其重定向到对话功能
    /// </summary>
    /// <param name="request">请求对象</param>
    /// <param name="token">取消令牌</param>
    /// <returns>翻译结果</returns>
    public virtual async Task<TranslationResult> TranslateAsync(object request, CancellationToken token)
    {
        if (request is not RequestModel req)
            throw new Exception($"请求数据出错: {request}");

        var result = string.Empty;
        var chatRequest = new ChatRequest(req.Text, SystemPrompt, ChatHistory.ToList(), KeepContext);

        await ChatAsync(chatRequest, chunk => result += chunk, token);

        return TranslationResult.Success(result);
    }

    /// <summary>
    /// 流式翻译方法，为了兼容现有接口，重定向到ChatAsync
    /// </summary>
    /// <param name="request">请求对象</param>
    /// <param name="onDataReceived">数据接收回调</param>
    /// <param name="token">取消令牌</param>
    /// <returns></returns>
    public virtual async Task TranslateAsync(object request, Action<string> onDataReceived, CancellationToken token)
    {
        if (request is not RequestModel req)
            throw new Exception($"请求数据出错: {request}");

        var chatRequest = new ChatRequest(req.Text, SystemPrompt, ChatHistory.ToList(), KeepContext);
        await ChatAsync(chatRequest, onDataReceived, token);
    }

    /// <summary>
    /// AI助手对话方法，子类需要实现具体的API调用逻辑
    /// </summary>
    /// <param name="request">聊天请求</param>
    /// <param name="onDataReceived">流式数据回调</param>
    /// <param name="token">取消令牌</param>
    /// <returns></returns>
    public abstract Task ChatAsync(ChatRequest request, Action<string> onDataReceived, CancellationToken token);

    /// <summary>
    /// 克隆方法，子类需要实现
    /// </summary>
    /// <returns>克隆的实例</returns>
    public abstract ITranslator Clone();

    #endregion

    #region Chat History Management

    /// <summary>
    /// 清空对话历史
    /// </summary>
    public virtual void ClearHistory()
    {
        ChatHistory.Clear();
    }

    /// <summary>
    /// 添加消息到历史记录
    /// </summary>
    /// <param name="message">消息</param>
    public virtual void AddToHistory(ChatMessage message)
    {
        if (!KeepContext) return;

        ChatHistory.Add(message);

        // 限制历史记录长度
        while (ChatHistory.Count > MaxHistoryLength)
        {
            ChatHistory.RemoveAt(0);
        }
    }

    /// <summary>
    /// 获取用于API调用的消息列表
    /// </summary>
    /// <param name="currentMessage">当前用户消息</param>
    /// <returns>包含系统提示词和历史记录的消息列表</returns>
    protected virtual List<ChatMessage> BuildMessageList(string currentMessage)
    {
        var messages = new List<ChatMessage>();

        // 添加系统提示词
        if (!string.IsNullOrWhiteSpace(SystemPrompt))
        {
            messages.Add(ChatMessage.System(SystemPrompt));
        }

        // 添加历史记录
        if (KeepContext && ChatHistory.Count > 0)
        {
            messages.AddRange(ChatHistory);
        }

        // 添加当前用户消息
        messages.Add(ChatMessage.User(currentMessage));

        return messages;
    }

    #endregion

    #region Utility Methods

    /// <summary>
    /// 手动通知属性更新
    /// </summary>
    /// <param name="name">属性名称</param>
    public void ManualPropChanged(params string[] name)
    {
        if (name.Length == 0) return;

        foreach (var prop in name)
        {
            OnPropertyChanged(prop);
        }
    }

    /// <summary>
    /// 处理流式响应的通用方法
    /// </summary>
    /// <param name="userMessage">用户消息</param>
    /// <param name="onDataReceived">数据接收回调</param>
    /// <param name="apiCall">API调用函数</param>
    /// <param name="token">取消令牌</param>
    /// <returns></returns>
    protected async Task HandleStreamingResponse(
        string userMessage,
        Action<string> onDataReceived,
        Func<List<ChatMessage>, Action<string>, CancellationToken, Task> apiCall,
        CancellationToken token)
    {
        var messages = BuildMessageList(userMessage);
        var assistantResponse = string.Empty;

        // 包装回调以收集完整响应
        Action<string> wrappedCallback = (chunk) =>
        {
            assistantResponse += chunk;
            onDataReceived?.Invoke(chunk);
        };

        try
        {
            await apiCall(messages, wrappedCallback, token);

            // 对话完成后，添加到历史记录
            if (KeepContext && !string.IsNullOrWhiteSpace(assistantResponse))
            {
                AddToHistory(ChatMessage.User(userMessage));
                AddToHistory(ChatMessage.Assistant(assistantResponse));
            }
        }
        catch (Exception)
        {
            // 发生异常时不添加到历史记录
            throw;
        }
    }

    /// <summary>
    /// 获取当前激活的系统提示词
    /// </summary>
    /// <returns>系统提示词</returns>
    protected virtual string GetActiveSystemPrompt()
    {
        var activePrompt = UserDefinePrompts.FirstOrDefault(x => x.Enabled);
        if (activePrompt != null)
        {
            var systemPrompt = activePrompt.Prompts.FirstOrDefault(p => p.Role == "system");
            if (systemPrompt != null)
            {
                return systemPrompt.Content;
            }
        }
        return SystemPrompt;
    }

    #endregion
}
